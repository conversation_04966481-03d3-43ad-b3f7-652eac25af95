import dotenv from 'dotenv';
import { envRegistry } from '../utils/env';

// Load environment variables first
dotenv.config();

// Register core environment variables
envRegistry.registerMultiple([
  {
    name: 'PORT',
    description: 'Port number for the server',
    defaultValue: '3002',
    required: false,
    module: 'Server'
  },
  {
    name: 'NODE_ENV',
    description: 'Environment (development, production, test)',
    defaultValue: 'development',
    required: false,
    module: 'Server'
  },
  {
    name: 'FRONTEND_URL',
    description: 'Frontend URL for CORS configuration',
    defaultValue: 'http://localhost:3000',
    required: false,
    module: 'Server'
  },
  {
    name: 'RATE_LIMIT_WINDOW_MS',
    description: 'Rate limiting window in milliseconds',
    defaultValue: '900000',
    required: false,
    module: 'Server'
  },
  {
    name: 'RATE_LIMIT_MAX_REQUESTS',
    description: 'Maximum number of requests per window',
    defaultValue: '100',
    required: false,
    module: 'Server'
  }
]);

export const initializeConfig = async () => {
  // Import modules in sequence to ensure proper initialization
  await import('../utils/logger');
  await import('../config/prismaClient');
  await import('../config/supabase');
}; 