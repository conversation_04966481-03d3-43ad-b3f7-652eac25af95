import prisma from '../../config/prismaClient';
import { Deal } from './deal.types';
import { DealStatus } from '../../generated/prisma';

const statusToPrisma: Record<string, DealStatus> = {
  'פעיל': DealStatus.ACTIVE,
  'הושלם': DealStatus.COMPLETED,
  'בוטל': DealStatus.CANCELLED
};

const statusFromPrisma: Record<DealStatus, string> = {
  [DealStatus.ACTIVE]: 'פעיל',
  [DealStatus.COMPLETED]: 'הושלם',
  [DealStatus.CANCELLED]: 'בוטל'
};

function mapToDeal(prismaDeal: any): Deal {
  return {
    id: prismaDeal.id,
    name: prismaDeal.name,
    category: prismaDeal.category,
    description: prismaDeal.description,
    targetSize: prismaDeal.target_size,
    currentSize: prismaDeal.current_size,
    status: statusFromPrisma[prismaDeal.status as DealStatus] as import('./deal.types').DealStatusString,
    organizerId: prismaDeal.organizer_id,
    organizerName: prismaDeal.organizer_name,
    imageUrl: prismaDeal.image_url,
    dataAiHint: prismaDeal.data_ai_hint,
    createdAt: prismaDeal.created_at,
    participants: prismaDeal.participants
  };
}

function mapToPrismaData(deal: Partial<Deal>): any {
  const data: any = {};
  if (deal.name !== undefined) data.name = deal.name;
  if (deal.category !== undefined) data.category = deal.category;
  if (deal.description !== undefined) data.description = deal.description;
  if (deal.targetSize !== undefined) data.target_size = deal.targetSize;
  if (deal.currentSize !== undefined) data.current_size = deal.currentSize;
  if (deal.status !== undefined) data.status = statusToPrisma[deal.status];
  if (deal.organizerId !== undefined) data.organizer_id = deal.organizerId;
  if (deal.organizerName !== undefined) data.organizer_name = deal.organizerName;
  if (deal.imageUrl !== undefined) data.image_url = deal.imageUrl;
  if (deal.dataAiHint !== undefined) data.data_ai_hint = deal.dataAiHint;
  if (deal.participants !== undefined) data.participants = deal.participants;
  return data;
}

export class DealService {
  constructor() {
    // Using centralized Prisma client
  }

  async createDeal(deal: Omit<Deal, 'id'>): Promise<Deal> {
    const prismaData = mapToPrismaData(deal);
    const created = await prisma.deal.create({ data: prismaData });
    return mapToDeal(created);
  }

  async getDeals(): Promise<Deal[]> {
    const deals = await prisma.deal.findMany({ orderBy: { created_at: 'desc' } });
    return deals.map(mapToDeal);
  }

  async getDeal(id: string): Promise<Deal | null> {
    const deal = await prisma.deal.findUnique({ where: { id } });
    return deal ? mapToDeal(deal) : null;
  }

  async updateDeal(id: string, data: Partial<Deal>): Promise<Deal | null> {
    const prismaData = mapToPrismaData(data);
    const deal = await prisma.deal.update({ where: { id }, data: prismaData });
    return mapToDeal(deal);
  }

  async deleteDeal(id: string): Promise<void> {
    await prisma.deal.delete({ where: { id } });
  }
} 