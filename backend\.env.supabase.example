# Supabase Environment Configuration
# Copy this file to .env and update the values

# Database
POSTGRES_PASSWORD=your_super_secret_postgres_password

# JWT Secret (generate with: openssl rand -base64 32)
JWT_SECRET=your_super_secret_jwt_token_with_at_least_32_characters_long

# API URLs
API_EXTERNAL_URL=http://localhost:8000
SUPABASE_PUBLIC_URL=http://localhost:8000
SITE_URL=http://localhost:3000

# Auth Configuration
DISABLE_SIGNUP=false
ENABLE_EMAIL_SIGNUP=true
ENABLE_EMAIL_AUTOCONFIRM=true
JWT_EXPIRY=3600
ADDITIONAL_REDIRECT_URLS=""

# Email Configuration (optional - for production)
SMTP_ADMIN_EMAIL=<EMAIL>
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
SMTP_SENDER_NAME=Kibbutz App

# Email URL Paths
MAILER_URLPATHS_INVITE=/auth/v1/verify
MAILER_URLPATHS_CONFIRMATION=/auth/v1/verify
MAILER_URLPATHS_RECOVERY=/auth/v1/verify
MAILER_URLPATHS_EMAIL_CHANGE=/auth/v1/verify

# Studio Configuration
STUDIO_DEFAULT_ORGANIZATION=Kibbutz Community
STUDIO_DEFAULT_PROJECT=Kibbutz App

# Image Processing
IMGPROXY_ENABLE_WEBP_DETECTION=true

# Expo/Frontend Environment Variables
EXPO_PUBLIC_SUPABASE_URL=http://localhost:8000
EXPO_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0

# Database URL for Prisma
DATABASE_URL=postgresql://postgres:your_super_secret_postgres_password@localhost:5432/postgres
