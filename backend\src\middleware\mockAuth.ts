import { Request, Response, NextFunction } from 'express';
import { getModuleLogger } from '../utils/logger';
import prisma from '../config/prismaClient';
import { AuthenticatedUser } from './rbacMiddleware';

const logger = getModuleLogger('MockAuthMiddleware');

export const mockAuthenticateToken = async (req: Request, res: Response, next: NextFunction): Promise<void | Response> => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader) {
      logger.warn('No authorization header provided');
      return res.status(401).json({ 
        success: false,
        error: 'No authorization header' 
      });
    }

    const token = authHeader.split(' ')[1];
    if (!token) {
      logger.warn('No token provided in authorization header');
      return res.status(401).json({ 
        success: false,
        error: 'No token provided' 
      });
    }

    // In test environment, we'll just decode the JWT token
    // The token should be created using the getAuthToken function from test setup
    const tokenParts = token.split('.');
    if (tokenParts.length !== 3 || !tokenParts[1]) {
      return res.status(401).json({
        success: false,
        error: 'Invalid token format'
      });
    }

    const payload = tokenParts[1];
    const decoded = JSON.parse(Buffer.from(payload, 'base64').toString());

    // Fetch user data from database to get role information
    const dbUser = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: {
        id: true,
        email: true,
        role: true,
        user_type: true,
        first_name: true,
        last_name: true
      }
    });

    if (!dbUser) {
      logger.warn('User not found in database', { userId: decoded.userId });
      return res.status(401).json({
        success: false,
        error: 'User not found'
      });
    }

    // Mock the enhanced user object structure
    req.user = {
      id: dbUser.id,
      email: dbUser.email,
      role: dbUser.role,
      user_type: dbUser.user_type,
      first_name: dbUser.first_name,
      last_name: dbUser.last_name
    };

    next();
  } catch (error) {
    logger.error('Mock authentication middleware error:', error);
    return res.status(500).json({ 
      success: false,
      error: 'Internal server error' 
    });
  }
}; 