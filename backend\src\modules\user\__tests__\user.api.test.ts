import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import request from 'supertest';
import { app } from '../../../app';
import { SupabaseAuthWrapper } from '../../../__tests__/supabaseAuth';
import { UserRole, UserType } from '../../../generated/prisma';

describe('User API', () => {
  let adminAuth: SupabaseAuthWrapper;
  let userAuth: SupabaseAuthWrapper;
  let adminUser: any;
  let regularUser: any;
  let adminToken: string;
  let userToken: string;

  beforeEach(async () => {
    // Create admin user
    adminAuth = new SupabaseAuthWrapper();
    const { user: adminDbUser, token: adminT } = await adminAuth.signUp();
    adminUser = adminDbUser;
    adminToken = adminT;

    // Promote to admin via API
    const promoteResponse = await request(app)
      .put(`/api/users/${adminUser.id}`)
      .set('Authorization', `Bearer ${adminToken}`)
      .send({ role: UserRole.ADMIN });

    if (promoteResponse.status !== 200) {
      throw new Error(`Failed to promote user to admin: ${promoteResponse.body.error}`);
    }

    // Update local admin user data with the promoted role
    adminUser = promoteResponse.body.data;

    // Create regular user
    userAuth = new SupabaseAuthWrapper();
    const { user: regDbUser, token: regT } = await userAuth.signUp();
    regularUser = regDbUser;
    userToken = regT;
  });

  afterEach(async () => {
    try {
      // First delete from Supabase while we still have valid tokens
      if (userAuth) {
        await userAuth.deleteUser();
      }
      if (adminAuth) {
        await adminAuth.deleteUser();
      }

      // Then delete from our database
      // These might fail if the user was already deleted from Supabase, which is fine
      if (regularUser) {
        try {
          await request(app)
            .delete(`/api/users/${regularUser.id}`)
            .set('Authorization', `Bearer ${userToken}`);
        } catch (error) {
          // Ignore 401/404 errors as they likely mean the user was already deleted
          if (typeof error === 'object' && error && 'status' in error && (error as any).status !== 401 && (error as any).status !== 404) {
            throw error;
          }
        }
      }
      if (adminUser) {
        try {
          await request(app)
            .delete(`/api/users/${adminUser.id}`)
            .set('Authorization', `Bearer ${adminToken}`);
        } catch (error) {
          // Ignore 401/404 errors as they likely mean the user was already deleted
          if (typeof error === 'object' && error && 'status' in error && (error as any).status !== 401 && (error as any).status !== 404) {
            throw error;
          }
        }
      }
    } catch (error) {
      // Log but don't fail the test if cleanup fails
      if (error instanceof Error) {
        console.warn('Cleanup warning:', error.message);
      } else {
        console.warn('Cleanup warning:', error);
      }
    }
  });

  describe('User Count', () => {
    it('should get total user count', async () => {
      const response = await request(app)
        .get('/api/users/count')
        .set('Authorization', `Bearer ${adminToken}`);
      expect(response.status).toBe(200);
      expect(typeof response.body.data).toBe('number');
      expect(response.body.data).toBeGreaterThan(0);
    });
  });

  describe('User Management', () => {
    it('should get all users (temporarily open to all users)', async () => {
      const response = await request(app)
        .get('/api/users')
        .set('Authorization', `Bearer ${userToken}`);
      expect(response.status).toBe(200);
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data.length).toBeGreaterThan(0);
    });

    it('should get user by ID', async () => {
      const response = await request(app)
        .get(`/api/users/${regularUser.id}`)
        .set('Authorization', `Bearer ${userToken}`);
      expect(response.status).toBe(200);
      expect(response.body.data.id).toBe(regularUser.id);
      expect(response.body.data.email).toBe(regularUser.email);
    });

    it('should not get non-existent user', async () => {
      const response = await request(app)
        .get('/api/users/non-existent-id')
        .set('Authorization', `Bearer ${userToken}`);
      expect(response.status).toBe(404);
    });

    it('should create user profile (temporarily open to all users)', async () => {
      // Create a new Supabase user
      const tempAuth = new SupabaseAuthWrapper();
      const { user: tempUser, token: tempToken } = await tempAuth.signUp();
      const newUserData = {
        id: tempUser.id,
        email: tempUser.email,
        firstName: 'Test',
        lastName: 'User',
        role: UserRole.USER,
        userType: UserType.ADULT,
        language: 'en'
      };
      const response = await request(app)
        .post('/api/users')
        .set('Authorization', `Bearer ${tempToken}`)
        .send(newUserData);
      expect(response.status).toBe(201);
      expect(response.body.data.email).toBe(newUserData.email);
      expect(response.body.data.role).toBe(newUserData.role);
      // Clean up
      await request(app)
        .delete(`/api/users/${response.body.data.id}`)
        .set('Authorization', `Bearer ${tempToken}`);
      await tempAuth.deleteUser();
    });

    it('should update user', async () => {
      const updateData = {
        firstName: 'Updated',
        lastName: 'Name',
        phone: '1234567890',
        language: 'es'
      };
      const response = await request(app)
        .put(`/api/users/${regularUser.id}`)
        .set('Authorization', `Bearer ${userToken}`)
        .send(updateData);
      expect(response.status).toBe(200);
      expect(response.body.data.first_name).toBe(updateData.firstName);
      expect(response.body.data.last_name).toBe(updateData.lastName);
      expect(response.body.data.phone).toBe(updateData.phone);
      expect(response.body.data.language).toBe(updateData.language);
    });

    it('should not update non-existent user', async () => {
      const updateData = {
        firstName: 'Updated',
        lastName: 'Name'
      };
      const response = await request(app)
        .put('/api/users/non-existent-id')
        .set('Authorization', `Bearer ${userToken}`)
        .send(updateData);
      expect(response.status).toBe(404);
    });

    it('should delete user (temporarily open to all users)', async () => {
      // Create a new user to delete
      const tempAuth = new SupabaseAuthWrapper();
      const { user: tempUser } = await tempAuth.signUp();
      // Delete via API
      const response = await request(app)
        .delete(`/api/users/${tempUser.id}`)
        .set('Authorization', `Bearer ${userToken}`);
      expect(response.status).toBe(200);
      expect(response.body.message).toBe('User deleted successfully');
      // Clean up Supabase
      await tempAuth.deleteUser();
    });
  });
}); 