import React from 'react';
import { ScrollView, ScrollViewProps, StyleSheet, Platform } from 'react-native';

interface WebCompatibleScrollViewProps extends ScrollViewProps {
  children: React.ReactNode;
  headerHeight?: number;
}

export default function WebCompatibleScrollView({ 
  children, 
  style, 
  contentContainerStyle,
  headerHeight = 60,
  ...props 
}: WebCompatibleScrollViewProps) {
  const webCompatibleStyle = Platform.OS === 'web' ? {
    height: `calc(100vh - ${headerHeight}px)`,
    overflowY: 'auto' as const,
    overflowX: 'hidden' as const,
  } : {};

  const webCompatibleContentStyle = Platform.OS === 'web' ? {} : {
    minHeight: '100%',
    flexGrow: 1,
  };

  return (
    <ScrollView
      style={[styles.defaultStyle, webCompatibleStyle, style]}
      contentContainerStyle={[webCompatibleContentStyle, contentContainerStyle]}
      showsVerticalScrollIndicator={true}
      nestedScrollEnabled={true}
      keyboardShouldPersistTaps="handled"
      {...props}
    >
      {children}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  defaultStyle: {
    flex: 1,
  },
});
