import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { useNavigation } from '@react-navigation/native';
import { useAuth } from '../../contexts/AuthContext';
import { getTextAlign } from '../../utils/rtl';
import Header from '../../components/Header';
import WebCompatibleScrollView from '../../components/WebCompatibleScrollView';
import { createWebCompatibleContainerStyle, createWebCompatibleScrollContentStyle } from '../../utils/webStyles';

export default function RegisterScreen() {
  const { t } = useTranslation();
  const navigation = useNavigation<any>();
  const { register } = useAuth();
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    displayName: '',
    phone: '',
    dateOfBirth: new Date(),
    userType: 'ADULT' as 'ADULT' | 'YOUTH' | 'CHILD' | 'EXTERNAL',
  });
  const [loading, setLoading] = useState(false);

  const handleRegister = async () => {
    console.log('🚀 Registration button clicked');

    if (!formData.email || !formData.password || !formData.displayName) {
      console.log('❌ Validation failed: Missing required fields');
      Alert.alert(t('common.error'), t('auth.fillAllFields'));
      return;
    }

    if (formData.password !== formData.confirmPassword) {
      console.log('❌ Validation failed: Password mismatch');
      Alert.alert(t('common.error'), t('auth.passwordMismatch'));
      return;
    }

    console.log('✅ Validation passed, starting registration...');
    setLoading(true);

    try {
      console.log('📝 Registering user with data:', {
        email: formData.email,
        displayName: formData.displayName,
        userType: formData.userType,
        phone: formData.phone ? 'provided' : 'not provided'
      });

      await register({
        email: formData.email,
        password: formData.password,
        name: formData.displayName,
        phone: formData.phone,
        dateOfBirth: formData.dateOfBirth,
        userType: formData.userType,
      });

      console.log('🎉 Registration successful!');
      // Navigation will be handled automatically by AuthContext auth state change
    } catch (error: any) {
      console.error('💥 Registration failed:', error);
      console.error('Error details:', {
        message: error.message,
        code: error.code,
        stack: error.stack
      });

      // Provide more specific error messages
      let errorMessage = error.message || t('auth.registerError');

      if (error.code === 'auth/email-already-in-use') {
        errorMessage = 'כתובת האימייל כבר בשימוש';
      } else if (error.code === 'auth/weak-password') {
        errorMessage = 'הסיסמה חלשה מדי - נדרשים לפחות 6 תווים';
      } else if (error.code === 'auth/invalid-email') {
        errorMessage = 'כתובת אימייל לא תקינה';
      } else if (error.code === 'auth/network-request-failed') {
        errorMessage = 'בעיית רשת - בדוק את החיבור לאינטרנט';
      }

      Alert.alert(t('common.error'), errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <Header
        title={t('auth.register')}
        showBackButton={true}
      />
      <KeyboardAvoidingView
        style={styles.keyboardContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <WebCompatibleScrollView
          contentContainerStyle={styles.scrollContent}
          headerHeight={60}
        >
          <View style={styles.content}>
          <Text style={[styles.subtitle, { textAlign: getTextAlign() }]}>
            צור חשבון חדש
          </Text>
          <Text style={[styles.infoText, { textAlign: getTextAlign() }]}>
            💡 המשתמש הראשון שנרשם יהפוך למנהל העל של המערכת
          </Text>

          <View style={styles.form}>
            <TextInput
              style={[styles.input, { textAlign: getTextAlign() }]}
              placeholder={t('auth.email')}
              value={formData.email}
              onChangeText={(text) => setFormData({ ...formData, email: text })}
              keyboardType="email-address"
              autoCapitalize="none"
            />

            <TextInput
              style={[styles.input, { textAlign: getTextAlign() }]}
              placeholder="שם מלא"
              value={formData.displayName}
              onChangeText={(text) => setFormData({ ...formData, displayName: text })}
            />

            <TextInput
              style={[styles.input, { textAlign: getTextAlign() }]}
              placeholder="טלפון (אופציונלי)"
              value={formData.phone}
              onChangeText={(text) => setFormData({ ...formData, phone: text })}
              keyboardType="phone-pad"
            />

            <TextInput
              style={[styles.input, { textAlign: getTextAlign() }]}
              placeholder={t('auth.password')}
              value={formData.password}
              onChangeText={(text) => setFormData({ ...formData, password: text })}
              secureTextEntry
            />

            <TextInput
              style={[styles.input, { textAlign: getTextAlign() }]}
              placeholder={t('auth.confirmPassword')}
              value={formData.confirmPassword}
              onChangeText={(text) => setFormData({ ...formData, confirmPassword: text })}
              secureTextEntry
            />

            <View style={styles.roleContainer}>
              <Text style={[styles.roleLabel, { textAlign: getTextAlign() }]}>
                סוג המשתמש:
              </Text>
              <View style={styles.roleButtons}>
                <TouchableOpacity
                  style={[
                    styles.roleButton,
                    formData.userType === 'ADULT' && styles.roleButtonActive
                  ]}
                  onPress={() => setFormData({ ...formData, userType: 'ADULT' })}
                >
                  <Text style={[
                    styles.roleButtonText,
                    formData.userType === 'ADULT' && styles.roleButtonTextActive
                  ]}>Adult</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.roleButton,
                    formData.userType === 'YOUTH' && styles.roleButtonActive
                  ]}
                  onPress={() => setFormData({ ...formData, userType: 'YOUTH' })}
                >
                  <Text style={[
                    styles.roleButtonText,
                    formData.userType === 'YOUTH' && styles.roleButtonTextActive
                  ]}>Youth</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.roleButton,
                    formData.userType === 'CHILD' && styles.roleButtonActive
                  ]}
                  onPress={() => setFormData({ ...formData, userType: 'CHILD' })}
                >
                  <Text style={[
                    styles.roleButtonText,
                    formData.userType === 'CHILD' && styles.roleButtonTextActive
                  ]}>Child</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.roleButton,
                    formData.userType === 'EXTERNAL' && styles.roleButtonActive
                  ]}
                  onPress={() => setFormData({ ...formData, userType: 'EXTERNAL' })}
                >
                  <Text style={[
                    styles.roleButtonText,
                    formData.userType === 'EXTERNAL' && styles.roleButtonTextActive
                  ]}>External</Text>
                </TouchableOpacity>
              </View>
            </View>

            <TouchableOpacity
              style={[styles.button, loading && styles.buttonDisabled]}
              onPress={handleRegister}
              disabled={loading}
            >
              <Text style={styles.buttonText}>
                {loading ? 'נרשם...' : t('auth.registerButton')}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.linkButton}
              onPress={() => navigation.navigate('Login')}
            >
              <Text style={styles.linkText}>{t('auth.hasAccount')}</Text>
            </TouchableOpacity>
          </View>
          </View>
        </WebCompatibleScrollView>
      </KeyboardAvoidingView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: createWebCompatibleContainerStyle(),
  keyboardContainer: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingVertical: 20, // Add vertical padding for better scrolling
  },
  content: {
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  subtitle: {
    fontSize: 16,
    marginBottom: 10,
    color: '#666',
  },
  infoText: {
    fontSize: 14,
    marginBottom: 30,
    color: '#007AFF',
    backgroundColor: '#f0f8ff',
    padding: 10,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#007AFF',
  },
  form: {
    width: '100%',
  },
  input: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 15,
    marginBottom: 15,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  roleContainer: {
    marginBottom: 20,
  },
  roleLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  roleButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  roleButton: {
    flex: 1,
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 15,
    marginHorizontal: 5,
    borderWidth: 2,
    borderColor: '#ddd',
    alignItems: 'center',
  },
  roleButtonActive: {
    borderColor: '#007AFF',
    backgroundColor: '#f0f8ff',
  },
  roleButtonText: {
    fontSize: 14,
    color: '#666',
    fontWeight: 'bold',
  },
  roleButtonTextActive: {
    color: '#007AFF',
  },
  button: {
    backgroundColor: '#007AFF',
    borderRadius: 8,
    padding: 15,
    alignItems: 'center',
    marginTop: 10,
  },
  buttonDisabled: {
    backgroundColor: '#ccc',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  linkButton: {
    alignItems: 'center',
    marginTop: 15,
  },
  linkText: {
    color: '#007AFF',
    fontSize: 14,
  },
});
