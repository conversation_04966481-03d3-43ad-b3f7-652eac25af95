import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Modal,
} from 'react-native';
import { useTheme } from 'react-native-paper';
import { getTextAlign } from '../utils/rtl';

interface IconPickerProps {
  selectedIcon?: string;
  onIconSelect: (icon: string) => void;
  label?: string;
  style?: any;
}

// Icon categories based on the community mockups
const ICON_CATEGORIES = {
  popular: {
    title: 'פופולרי',
    icons: ['📅', '🎉', '🏠', '🍕', '⚽', '🎵', '📚', '💼', '🌟', '🎯', '🏆', '❤️']
  },
  events: {
    title: 'אירועים',
    icons: ['🎉', '🎊', '🎈', '🎂', '🎭', '🎪', '🎨', '🎵', '🎤', '🎸', '🎺', '🎬']
  },
  community: {
    title: 'קהילה',
    icons: ['🏘️', '🏠', '🏡', '🏢', '🏛️', '🏪', '🏬', '⛪', '🕌', '🏫', '🏥', '🏦']
  },
  activities: {
    title: 'פעילויות',
    icons: ['⚽', '🏀', '🏈', '⚾', '🎾', '🏐', '🏓', '🏸', '🥊', '🏊', '🚴', '🏃']
  },
  food: {
    title: 'אוכל',
    icons: ['🍕', '🍔', '🍟', '🌭', '🥪', '🌮', '🍝', '🍜', '🍲', '🥘', '🍰', '🧁']
  },
  nature: {
    title: 'טבע',
    icons: ['🌳', '🌲', '🌴', '🌿', '🍀', '🌺', '🌻', '🌹', '🌷', '🌸', '🌼', '🦋']
  },
  education: {
    title: 'חינוך',
    icons: ['📚', '📖', '✏️', '📝', '🎓', '🏫', '👨‍🏫', '👩‍🏫', '🧮', '📐', '🔬', '🧪']
  },
  symbols: {
    title: 'סמלים',
    icons: ['⭐', '🌟', '✨', '💫', '🔥', '💧', '⚡', '☀️', '🌙', '🎯', '🏆', '💎']
  }
};

export const IconPicker: React.FC<IconPickerProps> = ({
  selectedIcon = '📅',
  onIconSelect,
  label = 'בחר אייקון',
  style
}) => {
  const theme = useTheme();
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState('popular');

  const handleIconSelect = (icon: string) => {
    onIconSelect(icon);
    setModalVisible(false);
  };

  return (
    <View style={[styles.container, style]}>
      {label && (
        <Text style={[styles.label, { color: theme.colors.onBackground, textAlign: getTextAlign() }]}>
          {label}
        </Text>
      )}
      
      <TouchableOpacity
        style={[
          styles.selectedIconContainer,
          {
            borderColor: theme.colors.outline,
            backgroundColor: theme.colors.surface,
            borderWidth: 2,
          }
        ]}
        onPress={() => setModalVisible(true)}
      >
        <View style={styles.iconPreview}>
          <Text style={styles.selectedIcon}>{selectedIcon}</Text>
        </View>
        <View style={styles.iconTextContainer}>
          <Text style={[styles.selectedIconText, { color: theme.colors.onSurface }]}>
            לחץ לבחירת אייקון
          </Text>
          <Text style={[styles.selectedIconSubtext, { color: theme.colors.onSurfaceVariant }]}>
            בחר אייקון שמייצג את האירוע
          </Text>
        </View>
      </TouchableOpacity>

      <Modal
        visible={modalVisible}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: theme.colors.surface }]}>
            <View style={[styles.modalHeader, { borderBottomColor: theme.colors.outline }]}>
              <Text style={[styles.modalTitle, { color: theme.colors.onSurface }]}>
                בחר אייקון לאירוע
              </Text>
              <TouchableOpacity
                style={[styles.closeButton, { backgroundColor: theme.colors.errorContainer }]}
                onPress={() => setModalVisible(false)}
              >
                <Text style={[styles.closeButtonText, { color: theme.colors.onErrorContainer }]}>✕</Text>
              </TouchableOpacity>
            </View>

            {/* Category Tabs */}
            <View style={styles.categoryTabsContainer}>
              <View style={styles.categoryGrid}>
                {Object.entries(ICON_CATEGORIES).map(([key, category]) => (
                  <TouchableOpacity
                    key={key}
                    style={[
                      styles.categoryTab,
                      {
                        backgroundColor: selectedCategory === key ? theme.colors.primary : theme.colors.surfaceVariant,
                        transform: selectedCategory === key ? [{ scale: 1.05 }] : [{ scale: 1 }],
                      }
                    ]}
                    onPress={() => setSelectedCategory(key)}
                    activeOpacity={0.8}
                  >
                    <Text style={[
                      styles.categoryTabText,
                      {
                        color: selectedCategory === key ? theme.colors.onPrimary : theme.colors.onSurfaceVariant,
                        fontWeight: selectedCategory === key ? '700' : '600',
                      }
                    ]}>
                      {category.title}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Icons Grid */}
            <ScrollView style={styles.iconsContainer}>
              <View style={styles.iconsGrid}>
                {ICON_CATEGORIES[selectedCategory as keyof typeof ICON_CATEGORIES].icons.map((icon) => (
                  <TouchableOpacity
                    key={icon}
                    style={[
                      styles.iconButton,
                      {
                        backgroundColor: selectedIcon === icon ? theme.colors.primaryContainer : theme.colors.surface,
                        borderWidth: selectedIcon === icon ? 3 : 0,
                        borderColor: selectedIcon === icon ? theme.colors.primary : 'transparent',
                        transform: selectedIcon === icon ? [{ scale: 0.95 }] : [{ scale: 1 }],
                      }
                    ]}
                    onPress={() => handleIconSelect(icon)}
                    activeOpacity={0.7}
                  >
                    <Text style={[
                      styles.iconText,
                      { opacity: selectedIcon === icon ? 1 : 0.8 }
                    ]}>
                      {icon}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </ScrollView>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  selectedIconContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 16,
    minHeight: 80,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
  },
  iconPreview: {
    width: 56,
    height: 56,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
    backgroundColor: '#f8f9ff',
    borderWidth: 1,
    borderColor: '#e8eaff',
  },
  selectedIcon: {
    fontSize: 32,
  },
  iconTextContainer: {
    flex: 1,
  },
  selectedIconText: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  selectedIconSubtext: {
    fontSize: 14,
    opacity: 0.7,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 10,
  },
  modalContent: {
    width: '95%',
    maxWidth: 500,
    height: '90%',
    borderRadius: 20,
    overflow: 'hidden',
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 5 },
    shadowOpacity: 0.3,
    shadowRadius: 10,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingBottom: 16,
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '700',
  },
  closeButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  categoryTabsContainer: {
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
    paddingBottom: 20,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  categoryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-around',
    alignItems: 'center',
    gap: 8,
  },
  categoryTab: {
    paddingHorizontal: 12,
    paddingVertical: 12,
    borderRadius: 18,
    marginBottom: 10,
    borderWidth: 0,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 3,
    width: '22%',
    minHeight: 45,
    justifyContent: 'center',
    alignItems: 'center',
  },
  categoryTabText: {
    fontSize: 11,
    fontWeight: '600',
    textAlign: 'center',
    lineHeight: 14,
  },
  iconsContainer: {
    flex: 1,
    paddingHorizontal: 25,
    paddingTop: 20,
    paddingBottom: 20,
  },
  iconsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  iconButton: {
    width: '18%',
    aspectRatio: 1,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 18,
    borderWidth: 0,
    marginBottom: 18,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
  },
  iconText: {
    fontSize: 38,
    textAlign: 'center',
  },
});
