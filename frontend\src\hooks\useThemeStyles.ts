import { useTheme } from 'react-native-paper';
import { useThemePreferences } from '../contexts/ThemeContext';

export function useThemeStyles() {
  const theme = useTheme();
  const { preferences } = useThemePreferences();

  // Get font size multiplier
  const fontSizeMultipliers = {
    small: 0.85,
    medium: 1.0,
    large: 1.15,
    extraLarge: 1.3,
  };

  // Get spacing multiplier
  const spacingMultipliers = {
    compact: 0.75,
    medium: 1.0,
    comfortable: 1.25,
  };

  const fontMultiplier = fontSizeMultipliers[preferences.fontSize];
  const spacingMultiplier = spacingMultipliers[preferences.spacing];

  // Dynamic font sizes
  const fontSize = {
    h1: Math.round(32 * fontMultiplier),
    h2: Math.round(28 * fontMultiplier),
    h3: Math.round(24 * fontMultiplier),
    h4: Math.round(20 * fontMultiplier),
    h5: Math.round(18 * fontMultiplier),
    h6: Math.round(16 * fontMultiplier),
    subtitle1: Math.round(16 * fontMultiplier),
    subtitle2: Math.round(14 * fontMultiplier),
    body1: Math.round(16 * fontMultiplier),
    body2: Math.round(14 * fontMultiplier),
    button: Math.round(14 * fontMultiplier),
    caption: Math.round(12 * fontMultiplier),
    overline: Math.round(10 * fontMultiplier),
  };

  // Dynamic spacing
  const spacing = {
    xs: Math.round(4 * spacingMultiplier),
    sm: Math.round(8 * spacingMultiplier),
    md: Math.round(16 * spacingMultiplier),
    lg: Math.round(24 * spacingMultiplier),
    xl: Math.round(32 * spacingMultiplier),
    xxl: Math.round(48 * spacingMultiplier),
  };

  // Common text styles with dynamic sizing
  const textStyles = {
    h1: {
      fontSize: fontSize.h1,
      fontWeight: 'bold' as const,
      color: theme.colors.onBackground,
    },
    h2: {
      fontSize: fontSize.h2,
      fontWeight: 'bold' as const,
      color: theme.colors.onBackground,
    },
    h3: {
      fontSize: fontSize.h3,
      fontWeight: 'bold' as const,
      color: theme.colors.onBackground,
    },
    h4: {
      fontSize: fontSize.h4,
      fontWeight: '600' as const,
      color: theme.colors.onBackground,
    },
    h5: {
      fontSize: fontSize.h5,
      fontWeight: '600' as const,
      color: theme.colors.onBackground,
    },
    h6: {
      fontSize: fontSize.h6,
      fontWeight: '600' as const,
      color: theme.colors.onBackground,
    },
    subtitle1: {
      fontSize: fontSize.subtitle1,
      fontWeight: '500' as const,
      color: theme.colors.onBackground,
    },
    subtitle2: {
      fontSize: fontSize.subtitle2,
      fontWeight: '500' as const,
      color: theme.colors.onBackground,
    },
    body1: {
      fontSize: fontSize.body1,
      fontWeight: 'normal' as const,
      color: theme.colors.onBackground,
    },
    body2: {
      fontSize: fontSize.body2,
      fontWeight: 'normal' as const,
      color: theme.colors.onBackground,
    },
    button: {
      fontSize: fontSize.button,
      fontWeight: '500' as const,
      color: theme.colors.onPrimary,
    },
    caption: {
      fontSize: fontSize.caption,
      fontWeight: 'normal' as const,
      color: theme.colors.onBackground,
      opacity: 0.7,
    },
  };

  return {
    theme,
    preferences,
    fontSize,
    spacing,
    textStyles,
    fontMultiplier,
    spacingMultiplier,
  };
}
