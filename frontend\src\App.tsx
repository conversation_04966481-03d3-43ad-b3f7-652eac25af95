import React, { useMemo, useEffect, useRef, useState } from 'react';
import { StatusBar } from 'expo-status-bar';
import { useColorScheme, AppState, AppStateStatus, Platform } from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { PaperProvider } from 'react-native-paper';
import { NavigationContainer } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { ThemeProvider, useThemePreferences, getEffectiveTheme } from './contexts/ThemeContext';
import { createTheme } from './theme/variants';
import AppNavigator from './navigation/AppNavigator';
import './i18n/index';

// Test if basic React Native components work
console.log('App.tsx loaded successfully');

// Navigation state persistence key
const NAVIGATION_PERSISTENCE_KEY = 'NAVIGATION_STATE_V1';
const NAVIGATION_USER_KEY = 'NAVIGATION_USER_ID';

// Inner app component that uses theme context
const AppContent: React.FC = () => {
  const { preferences, isLoading } = useThemePreferences();
  const { user } = useAuth();
  const systemColorScheme = useColorScheme();
  const [isNavigationReady, setIsNavigationReady] = useState(false);
  const [initialNavigationState, setInitialNavigationState] = useState();
  const appState = useRef(AppState.currentState);
  const currentUserIdRef = useRef<string | null>(null);

  const isDark = getEffectiveTheme(preferences, systemColorScheme) === 'dark';

  // Memoize theme creation to prevent unnecessary re-creations
  const theme = useMemo(() => {
    return createTheme(preferences, isDark);
  }, [preferences, isDark]);

  // Load navigation state on app start
  useEffect(() => {
    const restoreState = async () => {
      try {
        const savedStateString = await AsyncStorage.getItem(NAVIGATION_PERSISTENCE_KEY);
        const savedUserId = await AsyncStorage.getItem(NAVIGATION_USER_KEY);
        const state = savedStateString ? JSON.parse(savedStateString) : undefined;

        // Only restore navigation state if it's for the same user (or no user)
        const currentUserId = user?.id || null;
        if (state !== undefined && savedUserId === currentUserId) {
          console.log('🔄 Restoring navigation state for user:', currentUserId);
          setInitialNavigationState(state);
        } else {
          console.log('🔄 Not restoring navigation state - user changed or no saved state');
          // Clear old navigation state if user changed
          if (savedUserId !== currentUserId) {
            await AsyncStorage.removeItem(NAVIGATION_PERSISTENCE_KEY);
            await AsyncStorage.setItem(NAVIGATION_USER_KEY, currentUserId || '');
          }
        }

        currentUserIdRef.current = currentUserId;
      } catch (e) {
        console.warn('Failed to restore navigation state:', e);
      } finally {
        setIsNavigationReady(true);
      }
    };

    if (!isNavigationReady) {
      restoreState();
    }
  }, [isNavigationReady, user?.id]);

  // Handle app state changes to maintain navigation state
  useEffect(() => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      console.log('App state changed from', appState.current, 'to', nextAppState);

      if (appState.current.match(/inactive|background/) && nextAppState === 'active') {
        console.log('App has come to the foreground - navigation state should be preserved');
      }

      appState.current = nextAppState;
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);

    // Web-specific: Handle page visibility changes
    if (Platform.OS === 'web') {
      const handleVisibilityChange = () => {
        if (document.visibilityState === 'visible') {
          console.log('Web page became visible - navigation state should be preserved');
          // Prevent any navigation resets when page becomes visible
        } else {
          console.log('Web page became hidden - saving current navigation state');
        }
      };

      // Also handle beforeunload to save state before page closes
      const handleBeforeUnload = () => {
        console.log('Web page is about to unload - navigation state already saved');
      };

      document.addEventListener('visibilitychange', handleVisibilityChange);
      window.addEventListener('beforeunload', handleBeforeUnload);

      return () => {
        subscription?.remove();
        document.removeEventListener('visibilitychange', handleVisibilityChange);
        window.removeEventListener('beforeunload', handleBeforeUnload);
      };
    }

    return () => {
      subscription?.remove();
    };
  }, []);

  if (isLoading || !isNavigationReady) {
    return null; // Or a loading screen
  }

  return (
    <PaperProvider theme={theme}>
      <NavigationContainer
        initialState={initialNavigationState}
        onStateChange={(state) => {
          // Save navigation state whenever it changes, along with current user ID
          const currentUserId = user?.id || null;
          AsyncStorage.setItem(NAVIGATION_PERSISTENCE_KEY, JSON.stringify(state));
          AsyncStorage.setItem(NAVIGATION_USER_KEY, currentUserId || '');
        }}
      >
        <AppNavigator />
      </NavigationContainer>
      <StatusBar style={isDark ? "light" : "dark"} />
    </PaperProvider>
  );
};

const App: React.FC = () => {
  console.log('🎉 Full Kibbutz App Loading...');

  if (typeof document !== 'undefined') {
    document.title = 'Kibbutz App';
  }

  return (
    <SafeAreaProvider>
      <AuthProvider>
        <ThemeProvider>
          <AppContent />
        </ThemeProvider>
      </AuthProvider>
    </SafeAreaProvider>
  );
};

export default App;
