export interface ChatGroup {
  id: string;
  name: string;
  createdAt: Date;
  createdBy: string;
  members: string[];
  iconUrl: string | null;
  dataAiHint: string | null;
  lastMessage: string | null;
  lastMessageTimestamp: Date | null;
}

export interface ChatMessage {
  id: string;
  groupId: string;
  senderId: string;
  senderName: string;
  text: string;
  timestamp: Date;
  avatarUrl: string;
  avatarDataAiHint: string | null;
}