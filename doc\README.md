# Kibbutz Community App

A comprehensive community management application built with modern technologies, featuring user-customizable UI themes, supporting Hebrew and English languages with full cross-platform support (Web, Android, iOS).

## ✨ Key Features

### 🎨 **User Interface Customization**
- **5 Color Schemes**: Default, Blue, Green, Purple, Orange
- **3 Theme Modes**: Light, Dark, Auto (system-based)
- **4 Font Sizes**: Small to Extra Large with dynamic scaling
- **3 Spacing Options**: Compact, Medium, Comfortable
- **Accessibility**: High contrast mode, RTL support
- **Real-time Preview**: See changes instantly
- **Cross-device Sync**: Preferences saved to user account

### 🏘️ **Community Management**
- **Event Management**: Community and tenant events with RSVP
  - Native date/time pickers with RTL support
  - Cross-platform calendar integration (Web, iOS, Android)
  - Hebrew interface with proper right-to-left alignment
- **Job Board**: Local job postings and applications
- **Food Sharing**: Community marketplace for fresh produce
- **Chat System**: Group and direct messaging
- **Surveys**: Community polls and feedback collection
- **Commercial Unions**: Group buying and deals

### 🔐 **Security & Authentication**
- **Supabase Auth**: Secure JWT-based authentication
- **Role-based Access**: Admin, Moderator, User permissions
- **Data Protection**: Encrypted storage and secure API calls
- **Cross-platform Sessions**: Seamless login across devices

## 🏗️ Architecture Overview

The Kibbutz app uses a hybrid architecture combining:
- **Frontend**: Expo/React Native with Supabase client
- **Backend**: Self-hosted Supabase services + Custom Express.js API
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: Supabase GoTrue (JWT-based)
- **Real-time**: Supabase Realtime for live updates
- **Storage**: Supabase Storage for file management

## 📁 Project Structure

```
Kibuttz/
├── frontend/                    # Expo/React Native app
│   ├── src/
│   │   ├── components/         # Reusable UI components
│   │   ├── screens/           # Screen components by feature
│   │   │   ├── auth/          # Authentication screens
│   │   │   ├── events/        # Event management
│   │   │   ├── food/          # Food sharing
│   │   │   ├── jobs/          # Job assignments
│   │   │   ├── chat/          # Community chat
│   │   │   ├── profile/       # User profile management
│   │   │   └── settings/      # UI customization settings
│   │   ├── services/          # API clients and services
│   │   ├── contexts/          # React contexts (Auth, Theme)
│   │   ├── theme/             # Theme system and variants
│   │   ├── lib/               # Supabase client setup
│   │   ├── navigation/        # Navigation configuration
│   │   ├── i18n/              # Hebrew/English translations
│   │   └── types/             # TypeScript definitions
│   └── .env                   # Frontend environment variables
├── backend/                     # Custom Express.js API
│   ├── src/
│   │   ├── modules/           # Feature modules
│   │   │   ├── auth/          # Authentication logic
│   │   │   ├── user/          # User management
│   │   │   ├── event/         # Event business logic
│   │   │   ├── food/          # Food management
│   │   │   ├── job/           # Job management
│   │   │   ├── survey/        # Survey system
│   │   │   ├── deal/          # Deal management
│   │   │   └── chat/          # Chat functionality
│   │   ├── middleware/        # JWT verification, CORS, etc.
│   │   ├── config/            # Supabase and database config
│   │   └── utils/             # Logging and utilities
│   ├── prisma/                # Database schema and migrations
│   ├── volumes/               # Docker volumes for Supabase
│   ├── docker-compose.yml     # Supabase services
│   └── .env                   # Backend environment variables
└── doc/                        # Documentation
    └── README.md              # This file
```

## 🗄️ Database Schema Structure

The application uses three main schemas in PostgreSQL:

1. **public** - Created by Prisma
   - Contains all business logic tables (User, Event, FoodItem, etc.)
   - Managed through Prisma migrations
   - Tables: User (with ui_preferences), Event, FoodItem, Job, Survey, Deal, ChatGroup, ChatMessage, etc.

2. **auth** - Created by Supabase GoTrue
   - Handles authentication and user management
   - Created automatically by Supabase auth service
   - Tables: users, sessions, identities, refresh_tokens, etc.

3. **storage** - Created by Supabase Storage
   - Manages file storage and uploads
   - Created automatically by Supabase storage service
   - Contains buckets and file metadata

### Schema Management
- The `public` schema is managed through Prisma migrations
- The `auth` and `storage` schemas are managed by Supabase services
- If auth schema gets deleted, follow the "Recreating Auth Schema" section below

## 🔐 Authentication Architecture

### Authentication Flow

1. **User Registration**:
   ```mermaid
   sequenceDiagram
       participant User
       participant Frontend
       participant Supabase
       participant Backend
       participant Database

       User->>Frontend: Submit registration
       Frontend->>Supabase: Create user (signUp)
       Supabase-->>Frontend: Return user + session
       Frontend->>Backend: Create user profile (with session token)
       Backend->>Database: Store user profile
       Backend-->>Frontend: Return user profile
       Frontend-->>User: Registration complete
   ```

2. **User Login**:
   ```mermaid
   sequenceDiagram
       participant User
       participant Frontend
       participant Supabase
       participant Backend
       participant Database

       User->>Frontend: Submit login
       Frontend->>Supabase: Sign in
       Supabase-->>Frontend: Return session
       Frontend->>Backend: Get user profile (with session token)
       Backend->>Database: Fetch user profile
       Backend-->>Frontend: Return user profile
       Frontend-->>User: Login complete
   ```

### Environment Setup

#### Frontend Environment (.env)
```bash
# Supabase Configuration
EXPO_PUBLIC_SUPABASE_URL=http://localhost:8000
EXPO_PUBLIC_SUPABASE_ANON_KEY=your-anon-key

# Custom Backend API
EXPO_PUBLIC_API_URL=http://localhost:3002
```

#### Backend Environment (.env)
```bash
# Supabase Configuration
SUPABASE_URL=http://localhost:8000
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Server Configuration
PORT=3002
FRONTEND_URL=http://localhost:3000

# JWT Configuration
JWT_SECRET=your-jwt-secret
```

### Authentication Flow Details

1. **Registration Process**:
   - Frontend creates user in Supabase Auth
   - Supabase returns user data and session token
   - Frontend uses session token to create user profile in backend
   - Backend verifies token with Supabase before creating profile
   - If profile creation fails, Supabase user is cleaned up

2. **Login Process**:
   - Frontend authenticates with Supabase
   - Supabase returns session token
   - Frontend uses token to fetch user profile from backend
   - Backend verifies token with Supabase
   - User profile is returned to frontend

3. **API Authentication**:
   - All protected API routes require valid JWT token
   - Token is verified using Supabase's JWT verification
   - User context is added to request object
   - Role-based access control is enforced

4. **Error Handling**:
   - Authentication errors return 401 status
   - Invalid tokens return 401 status
   - Server errors return 500 status
   - All errors include descriptive messages

### Security Considerations

1. **Token Management**:
   - JWT tokens are stored securely in AsyncStorage
   - Tokens are automatically refreshed
   - Expired tokens trigger re-authentication

2. **CORS Configuration**:
   - Backend allows requests from frontend origins
   - Credentials are included in requests
   - Specific HTTP methods are allowed

3. **Rate Limiting**:
   - Authentication endpoints are rate-limited
   - Prevents brute force attacks
   - Configurable limits in backend

4. **Data Protection**:
   - Sensitive data is never stored in frontend
   - Passwords are hashed in Supabase
   - User profiles are protected by JWT verification

### Troubleshooting Authentication

1. **Common Issues**:
   ```bash
   # Check Supabase services
   curl http://localhost:8000/auth/v1/settings

   # Verify JWT token
   curl -H "Authorization: Bearer YOUR_JWT" http://localhost:3002/api/users/me

   # Check backend logs
   docker-compose logs backend
   ```

2. **Reset Authentication**:
   ```bash
   # Clear frontend auth data
   await supabase.auth.signOut()
   await AsyncStorage.clear()

   # Reset backend auth
   docker-compose restart auth
   ```

3. **Debug Mode**:
   ```typescript
   // Enable debug logging
   supabase.auth.onAuthStateChange((event, session) => {
     console.log('Auth event:', event)
     console.log('Session:', session)
   })
   ```

## 🚀 Complete Installation Guide

### Prerequisites

1. **Node.js** (v18 or higher)
2. **Docker** and **Docker Compose**
3. **Git**
4. **Expo CLI** (for mobile development)

### Step 1: Clone and Setup Project

```bash
# Clone the repository
git clone <repository-url>
cd Kibuttz

# Install frontend dependencies
cd frontend
npm install

# Install backend dependencies
cd ../backend
npm install
```

### Step 2: Database Setup

```bash
# Navigate to backend directory
cd backend

# Start PostgreSQL and Supabase services
docker-compose up -d

# Wait for services to start (about 30-60 seconds)
# Check if services are running
docker-compose ps

# Generate Prisma client
npx prisma generate

# Run database migrations to create tables
npx prisma db push

# Optional: Seed the database with initial data
npx prisma db seed
```

### Step 3: Environment Configuration

#### Backend Environment (.env)
Create `backend/.env` with the following configuration:

```bash
# Database
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/postgres?schema=public

# Supabase Configuration
SUPABASE_URL=http://localhost:8000
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU

# Server Configuration
PORT=3002
NODE_ENV=development
FRONTEND_URL=http://localhost:3000

# JWT Configuration
JWT_SECRET=super-secret-jwt-token-with-at-least-32-characters-for-kibbutz-app

# Docker Compose Variables (for Supabase services)
POSTGRES_PASSWORD=postgres
API_EXTERNAL_URL=http://localhost:8000
SUPABASE_PUBLIC_URL=http://localhost:8000
SITE_URL=http://localhost:3000
DISABLE_SIGNUP=false
ENABLE_EMAIL_SIGNUP=true
ENABLE_EMAIL_AUTOCONFIRM=true
```

#### Frontend Environment (.env)
Create `frontend/.env` with the following configuration:

```bash
# Supabase Configuration (for auth and direct database access)
EXPO_PUBLIC_SUPABASE_URL=http://localhost:8000
EXPO_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0

# Custom Backend API (for business logic)
EXPO_PUBLIC_API_BASE_URL=http://localhost:3002
```

### Step 4: Start Development Servers

```bash
# Terminal 1: Start Supabase services (if not already running)
cd backend
docker-compose up -d

# Terminal 2: Start backend server
cd backend
npm run dev

# Terminal 3: Start frontend
cd frontend
npm start

# For specific platforms:
npm run web      # Web browser
npm run android  # Android emulator
npm run ios      # iOS simulator
```

### Step 5: Verify Installation

1. **Check Supabase Studio**: Visit http://localhost:3000
2. **Check Backend API**: Visit http://localhost:3002/health
3. **Check Frontend**: Visit http://localhost:19006 (Expo web)

### Step 6: Test UI Customization

1. **Register/Login**: Create a user account or login
2. **Access Settings**: Profile → "⚙️ הגדרות תצוגה"
3. **Customize Interface**:
   - Try different color schemes (Blue, Green, Purple, Orange)
   - Switch between Light/Dark/Auto themes
   - Adjust font sizes and spacing
   - Enable high contrast mode
4. **Verify Persistence**: Logout and login again to see saved preferences

## 📡 API Documentation

### Authentication Flow

1. **User Registration/Login** → Frontend gets JWT from Supabase GoTrue
2. **API Calls** → Frontend sends JWT to custom backend
3. **JWT Verification** → Backend validates with Supabase
4. **Business Logic** → Backend processes request with user context
5. **Database Operations** → Backend uses Prisma with full control

### Backend API Endpoints

#### Authentication Routes (`/auth`)
- `POST /auth/register` - User registration
- `POST /auth/login` - User login
- `POST /auth/refresh-token` - Token refresh

#### User Management (`/api/users`) - 🔒 All Protected
- `GET /api/users` - Get all users
- `POST /api/users` - Create new user
- `GET /api/users/:id` - Get specific user
- `PATCH /api/users/:id` - Update user
- `DELETE /api/users/:id` - Delete user
- `GET /api/users/:id/ui-preferences` - Get user UI preferences
- `PUT /api/users/:id/ui-preferences` - Update user UI preferences

#### Events (`/api/events`)
- `GET /api/events` - Get all events (public)
- `GET /api/events/:id` - Get specific event (public)
- `POST /api/events` - Create event 🔒
- `PUT /api/events/:id` - Update event 🔒
- `DELETE /api/events/:id` - Delete event 🔒

#### Food Management (`/api/food`)
- `GET /api/food` - Get all food items (public)
- `GET /api/food/:id` - Get specific food item (public)
- `POST /api/food` - Create food item 🔒
- `PUT /api/food/:id` - Update food item 🔒
- `DELETE /api/food/:id` - Delete food item 🔒

#### Job Management (`/api/jobs`)
- `GET /api/jobs` - Get all jobs (public)
- `GET /api/jobs/:id` - Get specific job (public)
- `POST /api/jobs` - Create job 🔒
- `PUT /api/jobs/:id` - Update job 🔒
- `DELETE /api/jobs/:id` - Delete job 🔒

#### Surveys (`/api/surveys`)
- `GET /api/surveys` - Get all surveys (public)
- `GET /api/surveys/:id` - Get specific survey (public)
- `POST /api/surveys` - Create survey 🔒
- `PUT /api/surveys/:id` - Update survey 🔒
- `DELETE /api/surveys/:id` - Delete survey 🔒

#### Deals (`/api/deals`)
- `GET /api/deals` - Get all deals (public)
- `GET /api/deals/:id` - Get specific deal (public)
- `POST /api/deals` - Create deal 🔒
- `PUT /api/deals/:id` - Update deal 🔒
- `DELETE /api/deals/:id` - Delete deal 🔒

#### Chat (`/api/chat`) - 🔒 All Protected
- `GET /api/chat/groups` - Get all chat groups
- `POST /api/chat/groups` - Create chat group
- `GET /api/chat/groups/:id` - Get specific chat group
- `PUT /api/chat/groups/:id` - Update chat group
- `DELETE /api/chat/groups/:id` - Delete chat group
- `GET /api/chat/messages` - Get all messages
- `POST /api/chat/messages` - Create message
- `GET /api/chat/messages/:id` - Get specific message
- `PUT /api/chat/messages/:id` - Update message
- `DELETE /api/chat/messages/:id` - Delete message

### Supabase Services (via Kong Gateway - Port 8000)

#### GoTrue Auth (`/auth/v1/`)
- User registration, login, password reset
- JWT token management
- Email confirmation

#### PostgREST (`/rest/v1/`)
- Direct database access via REST API
- Auto-generated from PostgreSQL schema
- Row Level Security (RLS) support

#### Realtime (`/realtime/v1/`)
- WebSocket connections for real-time features
- Database change subscriptions
- Live updates

#### Storage (`/storage/v1/`)
- File upload/download
- Image processing via ImgProxy
- Secure file access

## 🎨 User Interface Customization

The Kibbutz app features a comprehensive UI customization system that allows each user to personalize their experience with different themes, colors, fonts, and accessibility options.

### Features Overview

#### 🌙 **Theme Modes**
- **Light Theme**: Clean, bright interface for daytime use
- **Dark Theme**: Easy on the eyes for low-light environments
- **Auto Theme**: Automatically switches based on system settings

#### 🎨 **Color Schemes**
- **Default**: Purple-based Material Design colors
- **Blue**: Professional blue tones
- **Green**: Nature-inspired green palette
- **Purple**: Rich purple and violet shades
- **Orange**: Warm orange and amber colors

#### 📝 **Font Sizes**
- **Small**: Compact text for more content density
- **Medium**: Standard readable size (default)
- **Large**: Larger text for better readability
- **Extra Large**: Maximum size for accessibility

#### 📏 **Spacing Options**
- **Compact**: Tighter spacing for information density
- **Medium**: Balanced spacing (default)
- **Comfortable**: Generous spacing for easier interaction

#### 🔍 **Accessibility & RTL Support**
- **High Contrast**: Enhanced contrast for visual accessibility
- **RTL Layout**: Full right-to-left support for Hebrew interface
- **Native Date/Time Pickers**: Platform-appropriate input methods
- **Cross-platform Compatibility**: Web fallbacks for unsupported components
- **Real-time Preview**: See changes instantly before applying
- **Cross-platform**: Consistent experience on Web, Android, iOS

### Technical Implementation

#### Database Schema
```sql
-- UI preferences stored as JSONB in User table
ui_preferences JSONB DEFAULT '{
  "theme": "light",
  "colorScheme": "default",
  "fontSize": "medium",
  "spacing": "medium",
  "highContrast": false
}'
```

#### Frontend Architecture
```typescript
// Theme Context manages user preferences
const { preferences, updatePreferences } = useThemePreferences();

// Dynamic theme creation based on preferences
const theme = createTheme(preferences, isDark);

// Real-time theme switching
await updatePreferences({ theme: 'dark' });

// Platform-specific date/time picker implementation
const DateTimeInput = ({ mode, value, onChange }) => {
  if (Platform.OS === 'web') {
    return (
      <Modal visible={showPicker}>
        <TextInput
          placeholder={mode === 'date' ? 'YYYY-MM-DD (לדוגמה: 2024-12-25)' : 'HH:MM (לדוגמה: 14:30)'}
          value={value}
          onChangeText={onChange}
        />
      </Modal>
    );
  }

  return (
    <DateTimePicker
      mode={mode}
      display={Platform.OS === 'ios' ? 'spinner' : 'default'}
      value={value}
      onChange={onChange}
    />
  );
};
```

#### Backend API
```typescript
// Get user UI preferences
GET /api/users/:id/ui-preferences

// Update user UI preferences
PUT /api/users/:id/ui-preferences
{
  "uiPreferences": {
    "theme": "dark",
    "colorScheme": "blue",
    "fontSize": "large",
    "spacing": "comfortable",
    "highContrast": true
  }
}
```

### User Experience

#### Accessing Settings
1. **From Profile**: Navigate to Profile → "⚙️ הגדרות תצוגה"
2. **Direct Navigation**: Settings screen in app navigation
3. **Live Preview**: Changes apply immediately with preview

#### Data Persistence
- **Primary Storage**: PostgreSQL database (synced across devices)
- **Fallback Storage**: Local AsyncStorage (offline support)
- **Auto-sync**: Preferences sync when user logs in on new device

#### Performance
- **Instant Updates**: Theme changes apply immediately
- **Optimized Rendering**: Efficient theme switching without flicker
- **Memory Efficient**: Themes generated on-demand

### Customization Components

#### ThemeSelector
```typescript
<ThemeSelector
  title="🎨 ערכת צבעים"
  options={colorSchemeOptions}
  selectedValue={preferences.colorScheme}
  onSelect={handleColorSchemeChange}
/>
```

#### ThemeToggle
```typescript
<ThemeToggle
  title="🔍 ניגודיות גבוהה"
  value={preferences.highContrast}
  onToggle={handleHighContrastToggle}
/>
```

#### ThemePreview
```typescript
<ThemePreview preferences={preferences} />
```

### Recent UI Improvements (2024)

#### 📅 **Enhanced Date/Time Input System**
The event creation system has been significantly improved with platform-specific date and time pickers:

**Cross-Platform Date/Time Pickers**:
- **Web Platform**: Custom modal with text input and Hebrew placeholders
  - Format examples: "YYYY-MM-DD (לדוגמה: 2024-12-25)" for dates
  - Format examples: "HH:MM (לדוגמה: 14:30)" for times
- **iOS Platform**: Native spinner-style pickers in modals
- **Android Platform**: Native default system pickers

**RTL Layout Improvements**:
- **Form Row Layout**: `flexDirection: 'row-reverse'` for proper Hebrew RTL
- **Field Positioning**: Date field on right, time field on left (Hebrew reading order)
- **Text Alignment**: All text properly aligned to the right within input fields
- **Icon Placement**: Consistent icon positioning with proper spacing

**Technical Implementation**:
```typescript
// Platform-specific picker rendering
{Platform.OS === 'web' ? (
  <TextInput placeholder="YYYY-MM-DD (לדוגמה: 2024-12-25)" />
) : (
  <DateTimePicker mode="date" display="default" />
)}

// RTL form layout
formRow: {
  flexDirection: 'row-reverse', // Date on right, time on left
  gap: 15,
}
```

**User Experience**:
- **Intuitive Interface**: Native feel on each platform
- **Hebrew Placeholders**: Clear format examples in Hebrew
- **Immediate Feedback**: Real-time validation and formatting
- **Accessibility**: Proper focus management and screen reader support

### Internationalization
All UI customization options support Hebrew and English:
- Hebrew interface with RTL text alignment
- Culturally appropriate color choices
- Accessible design for Hebrew typography
- Platform-specific input methods with Hebrew localization

### Troubleshooting UI Customization

#### Common Issues

1. **Preferences Not Saving**:
   ```bash
   # Check if user is authenticated
   curl -H "Authorization: Bearer YOUR_JWT" http://localhost:3002/api/users/me

   # Verify backend API is running
   curl http://localhost:3002/health

   # Check browser console for errors
   ```

2. **Theme Not Applying**:
   ```typescript
   // Clear local storage and restart app
   await AsyncStorage.clear();

   // Check if ThemeProvider is wrapping the app
   // Verify theme context is accessible
   ```

3. **Date/Time Picker Issues**:
   ```typescript
   // Web platform: DateTimePicker not supported error
   // Solution: Web fallback with text input is automatically used

   // RTL alignment issues
   // Check formRow flexDirection: 'row-reverse'
   // Verify text alignment: textAlign: 'right'

   // Platform-specific debugging
   console.log('Platform:', Platform.OS);
   console.log('Picker visible:', showDatePicker);
   ```

4. **Reset to Defaults**:
   ```typescript
   // Use the reset button in settings
   // Or manually clear preferences
   await updatePreferences({
     theme: 'light',
     colorScheme: 'default',
     fontSize: 'medium',
     spacing: 'medium',
     highContrast: false
   });
   ```

## 🔧 Development Guidelines

### Frontend Development

#### Cross-Platform Code
```typescript
import { Platform } from 'react-native';

const styles = StyleSheet.create({
  container: {
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
      },
      android: {
        elevation: 5,
      },
      web: {
        boxShadow: '0px 2px 3.84px rgba(0, 0, 0, 0.25)',
      },
    }),
  },
});
```

#### API Integration
```typescript
// Use the API service for backend calls
import { apiService } from '../services/api';

const createEvent = async (eventData) => {
  try {
    const response = await apiService.createEvent(eventData);
    if (response.error) {
      throw new Error(response.error);
    }
    return response.data;
  } catch (error) {
    console.error('Failed to create event:', error);
    throw error;
  }
};
```

### Backend Development

#### Authentication Middleware
All protected routes use JWT verification:

```typescript
import { authenticateToken } from '../../middleware/auth';

// Apply to protected routes
router.post('/api/events', authenticateToken, eventController.createEvent);
```

#### Database Operations
Use Prisma for type-safe database operations:

```typescript
// Create event with user context
const createEvent = async (req: AuthenticatedRequest, res: Response) => {
  const { title, description, date, location } = req.body;
  const userId = req.user.id; // From JWT verification

  const event = await prisma.event.create({
    data: {
      title,
      description,
      date: new Date(date),
      location,
      createdBy: userId,
      communityId: 'default', // Get from user context
    },
  });

  res.json(event);
};
```

## 🧪 Testing

### Frontend Testing
```bash
cd frontend
npm test                    # Run all tests
npm run test:watch         # Watch mode
npm run test:coverage      # Coverage report
```

### Backend Testing
```bash
cd backend
npm test                    # Run all tests
npm run test:watch         # Watch mode
npm run test:e2e           # End-to-end tests
```

## 🚀 Deployment

### Production Environment Variables

#### Backend Production (.env.production)
```bash
DATABASE_URL=*********************************************/kibbutz
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-production-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-production-service-key
JWT_SECRET=your-super-secure-production-jwt-secret
NODE_ENV=production
PORT=3002
```

#### Frontend Production
```bash
EXPO_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=your-production-anon-key
EXPO_PUBLIC_API_BASE_URL=https://your-backend-domain.com
```

### Build Commands
```bash
# Backend
cd backend
npm run build
npm run start:prod

# Frontend
cd frontend
npm run build:web          # Web deployment
expo build:android         # Android APK
expo build:ios             # iOS build
```

## 🛠️ Tech Stack

### Frontend
- **Expo/React Native** - Cross-platform mobile framework
- **React Native Paper** - Material Design components
- **React Navigation** - Navigation library
- **@react-native-community/datetimepicker** - Native date/time pickers
- **TypeScript** - Type safety
- **i18next** - Internationalization (Hebrew/English)
- **Supabase Client** - Authentication and database access

### Backend
- **Express.js** - Web framework
- **Prisma** - Database ORM
- **TypeScript** - Type safety
- **Supabase** - Authentication and database services
- **Docker** - Containerization
- **Kong** - API Gateway

### Database & Services
- **PostgreSQL** - Primary database
- **Supabase GoTrue** - Authentication service
- **Supabase PostgREST** - Auto-generated REST API
- **Supabase Realtime** - Real-time subscriptions
- **Supabase Storage** - File storage

## 🔍 Troubleshooting

### Common Issues

#### Database Connection Issues
```bash
# Check if PostgreSQL is running
docker-compose ps

# Restart services
docker-compose down
docker-compose up -d

# Check logs
docker-compose logs postgres
```

#### Authentication Issues
```bash
# Verify Supabase services are running
curl http://localhost:8000/auth/v1/settings

# Check JWT token in frontend
console.log(await supabase.auth.getSession());

# Verify backend can reach Supabase
curl -H "Authorization: Bearer YOUR_JWT" http://localhost:3002/api/events
```

#### Frontend Build Issues
```bash
# Clear Expo cache
expo start --clear

# Reset Metro bundler
npx react-native start --reset-cache

# Reinstall dependencies
rm -rf node_modules package-lock.json
npm install
```

## 📚 Additional Resources

- [Supabase Documentation](https://supabase.com/docs)
- [Expo Documentation](https://docs.expo.dev/)
- [Prisma Documentation](https://www.prisma.io/docs)
- [React Native Documentation](https://reactnative.dev/docs/getting-started)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Development Setup

### Prerequisites
- Node.js (v18 or later)
- Docker and Docker Compose
- Git

### Initial Setup
1. Clone the repository
2. Install dependencies:
   ```bash
   # Install backend dependencies
   cd backend
   npm install

   # Install frontend dependencies
   cd ../frontend
   npm install
   ```
3. Set up environment variables:
   - Copy `.env.example` to `.env` in both frontend and backend directories
   - Update the values as needed

### Starting the Development Environment
1. Start Supabase services:
   ```bash
   cd backend
   docker-compose up -d
   ```
2. Start the backend server:
   ```bash
   cd backend
   npm run dev
   ```
3. Start the frontend development server:
   ```bash
   cd frontend
   npm start
   ```

### Recreating Auth Schema
If the auth schema gets deleted or corrupted, follow these steps to recreate it:

1. Stop the Supabase services:
   ```bash
   cd backend
   docker-compose down
   ```

2. Start the services again:
   ```bash
   docker-compose up -d
   ```

3. Create the auth schema:
   ```bash
   docker exec -it kibbutz-postgres psql -U postgres -c "CREATE SCHEMA auth;"
   ```

4. Restart the auth service to initialize the schema:
   ```bash
   docker restart kibbutz-auth
   ```

5. Verify the auth schema was created properly:
   ```bash
   docker exec -it kibbutz-postgres psql -U postgres -c "\dt auth.*"
   ```

You should see a list of auth tables including `users`, `sessions`, `identities`, etc.