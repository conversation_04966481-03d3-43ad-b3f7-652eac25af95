# Kibbutz App - Complete Stack Setup Guide

## 🏗️ Architecture Overview

This project implements the complete tech stack as specified:

### Frontend
- ✅ **Expo (React Native)** - Cross-platform mobile app (iOS/Android/web)
- ✅ **TypeScript** - Type-safe development
- ✅ **React Navigation** - Navigation system
- ✅ **React Native Paper** - Material Design components

### Database
- ✅ **PostgreSQL** - Running via Docker locally
- ✅ **Prisma ORM** - Type-safe database client

### Backend Services (Supabase Self-hosted)
- ✅ **PostgREST** - Auto-generated REST API
- ✅ **GoTrue** - Auth service for email/password, OAuth
- ✅ **Realtime** - Live data updates over WebSockets
- ✅ **Storage** - File uploads (images, documents)
- ✅ **Supabase Studio** - Web dashboard

### Custom Business Logic
- ✅ **Node.js + Express** - Custom backend API
- ✅ **Prisma ORM** - Database interactions

### Authentication
- ✅ **Firebase Auth** - Primary authentication
- ✅ **GoTrue** - Alternative/backup auth via Supabase

## 🚀 Quick Start

### 1. Environment Setup

Copy the environment template:
```bash
cp .env.supabase.example .env
```

Update the `.env` file with your values:
- Generate JWT_SECRET: `openssl rand -base64 32`
- Set strong POSTGRES_PASSWORD
- Configure email settings (optional for development)

### 2. Install Dependencies

Frontend dependencies:
```bash
npm install
```

Backend dependencies:
```bash
cd backend
npm install
cd ..
```

### 3. Start Supabase Services

Start all Supabase services with Docker:
```bash
npm run supabase:start
```

This starts:
- PostgreSQL (port 5432)
- PostgREST API (port 3001)
- GoTrue Auth (port 9999)
- Realtime (port 4000)
- Storage API (port 5000)
- Supabase Studio (port 3000)
- Kong Gateway (port 8000)

### 4. Setup Database

Generate Prisma client:
```bash
npm run db:generate
```

Push schema to database:
```bash
npm run db:push
```

### 5. Start Development Servers

Start the custom backend API:
```bash
npm run backend:dev
```

Start the Expo frontend:
```bash
npm start
```

## 📊 Service Ports

| Service | Port | URL | Description |
|---------|------|-----|-------------|
| Supabase Studio | 3000 | http://localhost:3000 | Database dashboard |
| PostgREST API | 3001 | http://localhost:3001 | Auto-generated REST API |
| Custom Backend | 3002 | http://localhost:3002 | Express.js API |
| Realtime | 4000 | ws://localhost:4000 | WebSocket connections |
| Storage API | 5000 | http://localhost:5000 | File upload/download |
| PostgreSQL | 5432 | localhost:5432 | Database connection |
| Kong Gateway | 8000 | http://localhost:8000 | API Gateway |
| GoTrue Auth | 9999 | http://localhost:9999 | Authentication service |

## 🔧 Available Scripts

### Frontend
- `npm start` - Start Expo development server
- `npm run android` - Run on Android
- `npm run ios` - Run on iOS
- `npm run web` - Run on web

### Database
- `npm run db:generate` - Generate Prisma client
- `npm run db:push` - Push schema to database
- `npm run db:migrate` - Run database migrations
- `npm run db:studio` - Open Prisma Studio

### Supabase
- `npm run supabase:start` - Start all services
- `npm run supabase:stop` - Stop all services
- `npm run supabase:reset` - Reset and restart services

### Backend
- `npm run backend:dev` - Start backend in development mode
- `npm run backend:build` - Build backend for production

## 🗄️ Database Schema

The Prisma schema includes all required models:
- **User** - User profiles and authentication
- **Event** - Community events
- **FoodItem** - Food marketplace
- **Job** - Job board
- **ChatGroup** & **ChatMessage** - Community chat
- **Survey** - Community surveys
- **Deal** - Group buying deals
- **InlinePhoto** - Image storage

## 🔐 Authentication Options

### Primary: Firebase Auth
- Configured in `src/config/firebase.ts`
- Supports email/password, OAuth providers
- Free tier: 50,000 monthly active users

### Secondary: Supabase GoTrue
- Available via `src/lib/supabase.ts`
- Backup authentication option
- Integrated with PostgreSQL

## 📱 Frontend Features

- Cross-platform (iOS/Android/web)
- Material Design with React Native Paper
- Internationalization (i18n) support
- TypeScript for type safety
- Navigation with React Navigation
- State management with React Context

## 🛠️ Development Tools

- **Supabase Studio** - Database management UI
- **Prisma Studio** - Database browser
- **ESLint** - Code linting
- **Prettier** - Code formatting
- **Jest** - Testing framework
- **TypeScript** - Type checking

## 🚀 Production Deployment

### Backend
1. Build the backend: `npm run backend:build`
2. Deploy to your preferred platform (Heroku, Railway, etc.)
3. Set environment variables in production

### Frontend
1. Build for web: `npm run build:web`
2. Build for mobile: Use EAS Build or Expo CLI

### Database
1. Use managed PostgreSQL (AWS RDS, Supabase Cloud, etc.)
2. Run migrations: `npm run db:migrate`

## 🔍 Troubleshooting

### Docker Issues
```bash
# Reset Docker containers
npm run supabase:reset

# Check container logs
docker-compose logs [service-name]
```

### Database Issues
```bash
# Reset Prisma client
npm run db:generate

# Check database connection
npm run db:studio
```

### Port Conflicts
If ports are in use, update the ports in `docker-compose.yml` and `.env` file.

## 📚 Documentation

- [Expo Documentation](https://docs.expo.dev/)
- [Supabase Documentation](https://supabase.com/docs)
- [Prisma Documentation](https://www.prisma.io/docs)
- [React Native Paper](https://reactnativepaper.com/)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests: `npm test`
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details.
