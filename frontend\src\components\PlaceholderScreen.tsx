import React from 'react';
import {
  View,
  Text,
  StyleSheet,
} from 'react-native';
import { getTextAlign } from '../utils/rtl';
import Header from './Header';

interface PlaceholderScreenProps {
  title: string;
  subtitle?: string;
  showBackButton?: boolean;
}

export default function PlaceholderScreen({
  title,
  subtitle,
  showBackButton = false
}: PlaceholderScreenProps) {
  return (
    <View style={styles.container}>
      {showBackButton && (
        <Header
          title={title}
          showBackButton={true}
        />
      )}
      <View style={[styles.content, !showBackButton && styles.centered]}>
        {!showBackButton && (
          <Text style={[styles.title, { textAlign: getTextAlign() }]}>
            {title}
          </Text>
        )}
        {subtitle && (
          <Text style={[styles.subtitle, { textAlign: getTextAlign() }]}>
            {subtitle}
          </Text>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f7fa',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
  },
});
