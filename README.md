# 🏘️ Kibbutz Community App

A comprehensive community management application built with modern technologies, featuring user-customizable UI themes, supporting Hebrew and English languages with full cross-platform support (Web, Android, iOS).

## ✨ Key Features

### 🎨 **User Interface Customization**
- **5 Color Schemes**: Default, Blue, Green, Purple, Orange
- **3 Theme Modes**: Light, Dark, Auto (system-based)
- **4 Font Sizes**: Small to Extra Large with dynamic scaling
- **RTL Support**: Full Hebrew right-to-left interface
- **Native Date/Time Pickers**: Platform-specific input methods

### 🏘️ **Community Management**
- **Event Management**: Community and tenant events with RSVP
- **Job Board**: Local job postings and applications
- **Food Sharing**: Community marketplace for fresh produce
- **Chat System**: Group and direct messaging
- **Surveys**: Community polls and feedback collection
- **Commercial Unions**: Group buying and deals

### 🔐 **Security & Authentication**
- **Supabase Auth**: Secure JWT-based authentication
- **Role-based Access**: Admin, Moderator, User permissions
- **Cross-platform Sessions**: Seamless login across devices

## 🚀 Quick Start

### Prerequisites
- Node.js (v18 or higher)
- Docker and Docker Compose
- Git
- Expo CLI (for mobile development)

### Installation

1. **Clone and Setup**:
   ```bash
   git clone <repository-url>
   cd Kibuttz
   
   # Install dependencies
   cd frontend && npm install
   cd ../backend && npm install
   ```

2. **Start Services**:
   ```bash
   # Start Supabase services
   cd backend
   docker-compose up -d
   
   # Setup database
   npx prisma generate
   npx prisma db push
   ```

3. **Configure Environment**:
   ```bash
   # Copy environment files
   cp backend/.env.example backend/.env
   cp frontend/.env.example frontend/.env
   ```

4. **Start Development Servers**:
   ```bash
   # Terminal 1: Backend
   cd backend && npm run dev
   
   # Terminal 2: Frontend
   cd frontend && npm start
   ```

5. **Access the App**:
   - **Web**: http://localhost:19006
   - **Backend API**: http://localhost:3002
   - **Supabase Studio**: http://localhost:3000

## 🎯 Recent Improvements (2024)

### 📅 **Enhanced Date/Time Input System**
- **Cross-Platform Pickers**: Native date/time selection on iOS/Android, web-compatible fallbacks
- **RTL Layout**: Proper Hebrew right-to-left form alignment
- **Platform Detection**: Automatic fallback for unsupported components
- **Hebrew Localization**: Format examples and placeholders in Hebrew

### 🎨 **UI/UX Enhancements**
- **Improved Form Layout**: Date field on right, time field on left (Hebrew RTL)
- **Better Accessibility**: Enhanced contrast and screen reader support
- **Consistent Styling**: Unified design across all platforms

## 🏗️ Architecture

```
Frontend (Expo/React Native)
├── Web Browser
├── iOS App
└── Android App
    ↓
Backend (Express.js + Prisma)
    ↓
Supabase Services
├── PostgreSQL Database
├── GoTrue Authentication
├── PostgREST API
├── Realtime Subscriptions
└── Storage
```

## 🛠️ Tech Stack

### Frontend
- **Expo/React Native** - Cross-platform framework
- **React Native Paper** - Material Design components
- **@react-native-community/datetimepicker** - Native date/time pickers
- **TypeScript** - Type safety
- **i18next** - Hebrew/English internationalization

### Backend
- **Express.js** - Web framework
- **Prisma** - Database ORM
- **Supabase** - Authentication and database services
- **Docker** - Containerization

### Database
- **PostgreSQL** - Primary database
- **Supabase GoTrue** - Authentication
- **Supabase Storage** - File management

## 📚 Documentation

For detailed documentation, see:
- **[Complete Documentation](doc/README.md)** - Full setup, API docs, and troubleshooting
- **[Implementation Steps](doc/IMPLEMENTATION_STEPS.md)** - Development roadmap
- **[Testing Guide](doc/TESTING_GUIDE.md)** - Testing procedures
- **[Migration Plan](doc/MIGRATION_PLAN.md)** - UI stack migration details

## 🔧 Development

### Frontend Development
```bash
cd frontend
npm start           # Start Expo dev server
npm run web         # Web browser
npm run android     # Android emulator
npm run ios         # iOS simulator
```

### Backend Development
```bash
cd backend
npm run dev         # Start with hot reload
npm test            # Run tests
npm run build       # Production build
```

### Database Management
```bash
cd backend
npx prisma studio   # Database GUI
npx prisma migrate  # Run migrations
npx prisma generate # Update client
```

## 🧪 Testing

```bash
# Frontend tests
cd frontend && npm test

# Backend tests
cd backend && npm test

# End-to-end tests
cd backend && npm run test:e2e
```

## 🚀 Deployment

### Production Build
```bash
# Backend
cd backend
npm run build
npm run start:prod

# Frontend
cd frontend
npm run build:web    # Web deployment
expo build:android   # Android APK
expo build:ios       # iOS build
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For issues and questions:
1. Check the [troubleshooting guide](doc/README.md#troubleshooting)
2. Review [common issues](doc/README.md#common-issues)
3. Open an issue on GitHub

---

**Built with ❤️ for community management**
