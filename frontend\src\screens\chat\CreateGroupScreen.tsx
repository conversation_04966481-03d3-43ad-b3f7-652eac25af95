import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useTranslation } from 'react-i18next';
import { getTextAlign } from '../../utils/rtl';
import { chatService } from '../../services/chat';
import { ChatStackParamList } from '../../navigation/types';
import Header from '../../components/Header';

type CreateGroupScreenNavigationProp = StackNavigationProp<ChatStackParamList, 'CreateGroup'>;

export default function CreateGroupScreen() {
  const { t } = useTranslation();
  const navigation = useNavigation<CreateGroupScreenNavigationProp>();
  const [groupName, setGroupName] = useState('');
  const [groupDescription, setGroupDescription] = useState('');
  const [loading, setLoading] = useState(false);

  const handleCreateGroup = async () => {
    if (!groupName.trim()) {
      Alert.alert('שגיאה', 'אנא הזן שם לקבוצה');
      return;
    }

    setLoading(true);
    try {
      const newGroup = {
        name: groupName.trim(),
        createdAt: new Date(),
        createdBy: 'currentUser', // TODO: Get from auth context
        members: ['currentUser'], // TODO: Add functionality to select members
        iconUrl: null,
        dataAiHint: groupDescription.trim() || null,
        lastMessage: null,
        lastMessageTimestamp: null,
      };

      const createdGroup = await chatService.createChatGroup(newGroup);
      console.log('✅ Group created successfully:', createdGroup);

      Alert.alert(
        'הצלחה',
        'הקבוצה נוצרה בהצלחה!',
        [
          {
            text: 'אישור',
            onPress: () => {
              navigation.goBack();
            },
          },
        ]
      );
    } catch (error) {
      console.error('❌ Error creating group:', error);
      Alert.alert('שגיאה', 'אירעה שגיאה ביצירת הקבוצה. אנא נסה שוב.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <Header
        title="💬 צור קבוצה חדשה"
        showBackButton={true}
      />

      <ScrollView style={styles.content} contentContainerStyle={styles.contentContainer}>
        <View style={styles.form}>
          <Text style={[styles.label, { textAlign: getTextAlign() }]}>
            שם הקבוצה *
          </Text>
          <TextInput
            style={[styles.input, { textAlign: getTextAlign() }]}
            placeholder="הזן שם לקבוצה..."
            value={groupName}
            onChangeText={setGroupName}
            maxLength={50}
            placeholderTextColor="#999"
          />

          <Text style={[styles.label, { textAlign: getTextAlign() }]}>
            תיאור הקבוצה (אופציונלי)
          </Text>
          <TextInput
            style={[styles.input, styles.textArea, { textAlign: getTextAlign() }]}
            placeholder="הזן תיאור קצר לקבוצה..."
            value={groupDescription}
            onChangeText={setGroupDescription}
            maxLength={200}
            multiline
            numberOfLines={4}
            placeholderTextColor="#999"
          />

          <View style={styles.infoBox}>
            <Text style={[styles.infoText, { textAlign: getTextAlign() }]}>
              💡 לאחר יצירת הקבוצה, תוכל להוסיף חברים ולהתחיל לשוחח
            </Text>
          </View>
        </View>
      </ScrollView>

      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[styles.createButton, (!groupName.trim() || loading) && styles.createButtonDisabled]}
          onPress={handleCreateGroup}
          disabled={!groupName.trim() || loading}
        >
          <Text style={styles.createButtonText}>
            {loading ? 'יוצר קבוצה...' : 'צור קבוצה'}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    padding: 20,
  },
  form: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
    marginTop: 16,
  },
  input: {
    borderWidth: 1,
    borderColor: '#e1e8ed',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    backgroundColor: '#f8f9fa',
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  infoBox: {
    backgroundColor: '#e3f2fd',
    borderRadius: 8,
    padding: 15,
    marginTop: 20,
    borderLeftWidth: 4,
    borderLeftColor: '#45b7d1',
  },
  infoText: {
    fontSize: 14,
    color: '#1976d2',
    lineHeight: 20,
  },
  buttonContainer: {
    padding: 20,
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: '#e1e8ed',
  },
  createButton: {
    backgroundColor: '#45b7d1',
    borderRadius: 25,
    paddingVertical: 15,
    alignItems: 'center',
    justifyContent: 'center',
  },
  createButtonDisabled: {
    backgroundColor: '#bdc3c7',
  },
  createButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});
