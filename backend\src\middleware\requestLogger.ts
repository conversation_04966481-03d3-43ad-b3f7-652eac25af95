import { Request, Response, NextFunction } from 'express';
import { getModuleLogger } from '../utils/logger';

const logger = getModuleLogger('RequestLogger');

// Add debug logging to verify log level
logger.debug('RequestLogger module initialized with level:', { level: logger.getLevel() });

export const requestLogger = (req: Request, res: Response, next: NextFunction) => {
    const timestamp = new Date().toISOString();
    const method = req.method;
    const url = req.originalUrl;
    const ip = req.ip;

    // Log the incoming request
    logger.info(`[${timestamp}] ${method} ${url} - IP: ${ip}`);

    // Log request body if it exists
    if (Object.keys(req.body).length > 0) {
        logger.debug(`[${timestamp}] Request Body: ${JSON.stringify(req.body)}`);
    }

    // Capture the response
    const originalSend = res.send;
    res.send = function (body) {
        const statusCode = res.statusCode;
        logger.info(`[${timestamp}] ${method} ${url} - Status: ${statusCode}`);
        return originalSend.call(this, body);
    };

    next();
}; 