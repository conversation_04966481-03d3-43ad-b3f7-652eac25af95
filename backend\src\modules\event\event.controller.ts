import { Request, Response } from 'express';
import { EventService } from './event.service';
import { getModuleLogger } from '../../utils/logger';
import { Prisma } from '@prisma/client';

const logger = getModuleLogger('Event');

export class EventController {
  constructor(private readonly eventService: EventService) {}

  async getEvents(_req: Request, res: Response) {
    try {
      logger.debug('GET /events - Request received');
      const events = await this.eventService.getEvents();
      logger.debug('Response body:', { count: events.length });
      res.json(events);
    } catch (error) {
      logger.error('Error fetching events:', { error });
      res.status(500).json({ message: 'Error fetching events', error });
    }
  }

  async createEvent(req: Request, res: Response) {
    try {
      logger.debug('POST /events - Request received:', { body: req.body });

      // Add required fields for database
      const eventData = {
        ...req.body,
        community_id: "1", // Static community ID as requested
        created_by: req.user?.id || "unknown" // User ID from authentication
      };

      const event = await this.eventService.createEvent(eventData);
      logger.debug('Response body:', event);
      res.status(201).json(event);
    } catch (error) {
      logger.error('Error creating event:', { error });
      res.status(500).json({ message: 'Error creating event', error });
    }
  }

  async getEvent(req: Request, res: Response) {
    try {
      const { id } = req.params;
      if (!id) {
        return res.status(400).json({ message: 'Event ID is required' });
      }
      logger.debug('GET /events/:id - Request received:', { id });
      const event = await this.eventService.getEvent(id);
      if (!event) {
        logger.debug('Event not found:', { id });
        return res.status(404).json({ message: 'Event not found' });
      }
      logger.debug('Response body:', event);
      return res.json(event);
    } catch (error) {
      logger.error('Error fetching event:', { error, id: req.params['id'] });
      return res.status(500).json({ message: 'Error fetching event', error });
    }
  }

  async updateEvent(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const userId = req.user?.id;
      if (!id) {
        return res.status(400).json({ message: 'Event ID is required' });
      }
      logger.debug('PUT /events/:id - Request received:', { id, body: req.body });
      // Check if event exists
      const event = await this.eventService.getEventById(id);
      if (!event) {
        logger.debug('Event not found:', { id });
        return res.status(404).json({ message: 'Event not found' });
      }
      // Check ownership
      if (event.created_by !== userId) {
        logger.debug('Not authorized to update event', { id, userId });
        return res.status(403).json({ message: 'Not authorized to update this event' });
      }
      const updatedEvent = await this.eventService.updateEvent(id, req.body);
      logger.debug('Response body:', updatedEvent);
      return res.json(updatedEvent);
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
        logger.debug('Event not found (Prisma error):', { id: req.params['id'] });
        return res.status(404).json({ message: 'Event not found' });
      }
      logger.error('Error updating event:', { error, id: req.params['id'] });
      return res.status(500).json({ message: 'Error updating event', error });
    }
  }

  async deleteEvent(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const userId = req.user?.id;
      if (!id) {
        return res.status(400).json({ message: 'Event ID is required' });
      }
      logger.debug('DELETE /events/:id - Request received:', { id });
      // Check if event exists
      const event = await this.eventService.getEventById(id);
      if (!event) {
        logger.debug('Event not found:', { id });
        return res.status(404).json({ message: 'Event not found' });
      }
      // Check ownership
      if (event.created_by !== userId) {
        logger.debug('Not authorized to delete event', { id, userId });
        return res.status(403).json({ message: 'Not authorized to delete this event' });
      }
      await this.eventService.deleteEvent(id);
      logger.debug('Event deleted successfully:', { id });
      return res.status(204).send();
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
        logger.debug('Event not found (Prisma error):', { id: req.params['id'] });
        return res.status(404).json({ message: 'Event not found' });
      }
      logger.error('Error deleting event:', { error, id: req.params['id'] });
      return res.status(500).json({ message: 'Error deleting event', error });
    }
  }
} 