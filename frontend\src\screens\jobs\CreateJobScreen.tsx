import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { useNavigation } from '@react-navigation/native';
import { getTextAlign } from '../../utils/rtl';
import { jobsService } from '../../services/jobs';
import Header from '../../components/Header';

export default function CreateJobScreen() {
  const { t } = useTranslation();
  const navigation = useNavigation();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    location: '',
    dateTimeInfo: '',
    agePreference: '',
    skills: '',
    jobType: 'request' as 'offer' | 'request',
  });

  const handleCreateJob = async () => {
    if (!formData.title || !formData.description || !formData.dateTimeInfo) {
      Alert.alert(t('common.error'), 'אנא מלא את כל השדות הנדרשים');
      return;
    }

    setLoading(true);
    try {
      console.log('🔄 Creating job:', formData);

      // Create job data
      const jobData = {
        title: formData.title,
        description: formData.description,
        location: formData.location || '',
        dateTimeInfo: formData.dateTimeInfo,
        agePreference: formData.agePreference || undefined,
        skills: formData.skills ? formData.skills.split(',').map(s => s.trim()) : [],
        jobType: formData.jobType,
      };

      const newJob = await jobsService.createJob(jobData);
      console.log('✅ Job created successfully:', newJob);

      Alert.alert(
        t('common.success'),
        'העבודה נוצרה בהצלחה!',
        [
          {
            text: 'אישור',
            onPress: () => navigation.goBack()
          }
        ]
      );
    } catch (error) {
      console.error('❌ Error creating job:', error);
      Alert.alert(
        t('common.error'),
        'שגיאה ביצירת העבודה. אנא נסה שוב.'
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <Header
        title="יצירת עבודה חדשה"
        showBackButton={true}
      />
      <ScrollView style={styles.content}>
        <Text style={[styles.title, { textAlign: getTextAlign() }]}>
          יצירת עבודה חדשה
        </Text>

        <View style={styles.form}>
          <TextInput
            style={[styles.input, { textAlign: getTextAlign() }]}
            placeholder="כותרת העבודה *"
            value={formData.title}
            onChangeText={(text) => setFormData({ ...formData, title: text })}
          />

          <TextInput
            style={[styles.textArea, { textAlign: getTextAlign() }]}
            placeholder="תיאור העבודה *"
            value={formData.description}
            onChangeText={(text) => setFormData({ ...formData, description: text })}
            multiline
            numberOfLines={4}
          />

          <TextInput
            style={[styles.input, { textAlign: getTextAlign() }]}
            placeholder="מיקום"
            value={formData.location}
            onChangeText={(text) => setFormData({ ...formData, location: text })}
          />

          <TextInput
            style={[styles.input, { textAlign: getTextAlign() }]}
            placeholder="זמן ותאריך *"
            value={formData.dateTimeInfo}
            onChangeText={(text) => setFormData({ ...formData, dateTimeInfo: text })}
          />

          <TextInput
            style={[styles.input, { textAlign: getTextAlign() }]}
            placeholder="העדפת גיל (לדוגמה: 16+, 18-65)"
            value={formData.agePreference}
            onChangeText={(text) => setFormData({ ...formData, agePreference: text })}
          />

          <TextInput
            style={[styles.input, { textAlign: getTextAlign() }]}
            placeholder="כישורים נדרשים (מופרדים בפסיק)"
            value={formData.skills}
            onChangeText={(text) => setFormData({ ...formData, skills: text })}
          />

          <View style={styles.typeContainer}>
            <Text style={[styles.typeLabel, { textAlign: getTextAlign() }]}>
              סוג העבודה:
            </Text>
            <View style={styles.typeButtons}>
              <TouchableOpacity
                style={[
                  styles.typeButton,
                  formData.jobType === 'request' && styles.typeButtonActive
                ]}
                onPress={() => setFormData({ ...formData, jobType: 'request' })}
              >
                <Text style={[
                  styles.typeButtonText,
                  formData.jobType === 'request' && styles.typeButtonTextActive
                ]}>
                  בקשה לעזרה
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.typeButton,
                  formData.jobType === 'offer' && styles.typeButtonActive
                ]}
                onPress={() => setFormData({ ...formData, jobType: 'offer' })}
              >
                <Text style={[
                  styles.typeButtonText,
                  formData.jobType === 'offer' && styles.typeButtonTextActive
                ]}>
                  הצעת עזרה
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          <TouchableOpacity
            style={[styles.button, loading && styles.buttonDisabled]}
            onPress={handleCreateJob}
            disabled={loading}
          >
            <Text style={styles.buttonText}>
              {loading ? 'יוצר עבודה...' : 'צור עבודה'}
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f7fa',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    color: '#333',
  },
  form: {
    width: '100%',
  },
  input: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 15,
    marginBottom: 15,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  textArea: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 15,
    marginBottom: 15,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#ddd',
    height: 100,
    textAlignVertical: 'top',
  },
  typeContainer: {
    marginBottom: 20,
  },
  typeLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  typeButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  typeButton: {
    flex: 1,
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 15,
    marginHorizontal: 5,
    borderWidth: 2,
    borderColor: '#ddd',
    alignItems: 'center',
  },
  typeButtonActive: {
    borderColor: '#007AFF',
    backgroundColor: '#f0f8ff',
  },
  typeButtonText: {
    fontSize: 14,
    color: '#666',
    fontWeight: 'bold',
  },
  typeButtonTextActive: {
    color: '#007AFF',
  },
  button: {
    backgroundColor: '#007AFF',
    borderRadius: 8,
    padding: 15,
    alignItems: 'center',
    marginTop: 20,
  },
  buttonDisabled: {
    backgroundColor: '#ccc',
    opacity: 0.6,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});
