import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useColorScheme } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { UIPreferences } from '../types';
import { useAuth } from './AuthContext';
import { userPreferencesService } from '../services/userPreferences';

interface ThemeContextType {
  preferences: UIPreferences;
  updatePreferences: (newPreferences: Partial<UIPreferences>) => Promise<void>;
  resetToDefaults: () => Promise<void>;
  isLoading: boolean;
}

const defaultPreferences: UIPreferences = {
  theme: 'light', // Changed from 'auto' to 'light' for easier testing
  colorScheme: 'default',
  fontSize: 'medium',
  spacing: 'medium',
  highContrast: false,
};

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

interface ThemeProviderProps {
  children: ReactNode;
}

export function ThemeProvider({ children }: ThemeProviderProps) {
  const [preferences, setPreferences] = useState<UIPreferences>(defaultPreferences);
  const [isLoading, setIsLoading] = useState(true);
  const { user } = useAuth();
  const systemColorScheme = useColorScheme();

  // Load preferences on mount and when user changes
  useEffect(() => {
    loadPreferences();
  }, [user]);

  const loadPreferences = async () => {
    try {

      setIsLoading(true);

      if (user?.id) {
        // Try to load from backend first
        try {
          const serverPreferences = await userPreferencesService.getUserPreferences(user.id);
          if (serverPreferences) {

            setPreferences(serverPreferences);
            // Also save to local storage as backup
            await AsyncStorage.setItem('ui_preferences', JSON.stringify(serverPreferences));
            return;
          }
        } catch (error) {
          console.warn('Failed to load preferences from server, falling back to local storage:', error);
        }
      }

      // Fallback to local storage
      const localPreferences = await AsyncStorage.getItem('ui_preferences');
      if (localPreferences) {
        const parsed = JSON.parse(localPreferences);
        const mergedPreferences = { ...defaultPreferences, ...parsed };

        setPreferences(mergedPreferences);
      } else {

        setPreferences(defaultPreferences);
      }
    } catch (error) {
      console.error('Error loading preferences:', error);
      setPreferences(defaultPreferences);
    } finally {
      setIsLoading(false);
    }
  };

  const updatePreferences = async (newPreferences: Partial<UIPreferences>) => {
    try {
      const updatedPreferences = { ...preferences, ...newPreferences };
      console.log('🎨 ThemeContext: Updating preferences from', preferences, 'to', updatedPreferences);

      setPreferences(updatedPreferences);

      // Save to local storage immediately
      await AsyncStorage.setItem('ui_preferences', JSON.stringify(updatedPreferences));
      console.log('🎨 ThemeContext: Saved to local storage');

      // Save to backend if user is logged in
      if (user?.id) {
        try {
          await userPreferencesService.updateUserPreferences(user.id, updatedPreferences);
          console.log('🎨 ThemeContext: Saved to backend');
        } catch (error) {
          console.warn('Failed to save preferences to server:', error);
          // Don't throw error - local storage is already updated
        }
      }
    } catch (error) {
      console.error('Error updating preferences:', error);
      throw error;
    }
  };

  const resetToDefaults = async () => {
    await updatePreferences(defaultPreferences);
  };

  const value: ThemeContextType = {
    preferences,
    updatePreferences,
    resetToDefaults,
    isLoading,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useThemePreferences() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useThemePreferences must be used within a ThemeProvider');
  }
  return context;
}

// Helper function to get the effective theme based on preferences and system
export function getEffectiveTheme(preferences: UIPreferences, systemColorScheme: 'light' | 'dark' | null | undefined): 'light' | 'dark' {
  if (preferences.theme === 'auto') {
    return systemColorScheme === 'dark' ? 'dark' : 'light';
  }
  return preferences.theme === 'dark' ? 'dark' : 'light';
}
