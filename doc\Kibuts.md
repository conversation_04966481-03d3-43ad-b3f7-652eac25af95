

Here's a comprehensive **Product Requirements Document (PRD)** for your **Kibbutz Community App**. It is structured to cover **functional**, **technical**, and **design** aspects with recommendations and expanded features that align with your community’s needs.

---

# **📄 Product Requirements Document (PRD)**

**Project Title**: Kibbutz Community App  
 **Target Audience**: 350 Families (\~1050 children \+ \~700 adults)  
 **Language**: Hebrew  
 **Platforms**: iOS, Android (initial), Web (optional future phase)  
 **Versions**:

* Adult App

* Kids App (lightweight, age-restricted access to features)

---

## **1\. 📌 Objective**

To build a mobile application designed specifically for a Kibbutz community in Israel to **improve internal communication**, **organize local initiatives**, **match youth with residents for informal work**, **facilitate group deals**, and **promote local participation in surveys and events**.

---

## **2\. 🧑‍🤝‍🧑 User Roles**

| Role | Permissions |
| ----- | ----- |
| **Admin** | Full access: create/edit/delete content in all modules, manage users |
| **Adult User** | Full access to all modules except Admin-only areas |
| **Youth User (Kid)** | Restricted access to: Chat (safe-mode), Jobs, Events |

---

## **3\. 🔐 Authentication & Access Control**

### **3.1 Login & Registration**

* **One-time Login after Installation**, persistent login until logout

* **SMS-based verification** or **email \+ password**

* **Role assignment** at registration (admin-controlled if needed)

* **Kids Registration**: DOB required → filtered access

### **3.2 Access Control Matrix**

| Module | Admin | Adult | Kid |
| ----- | ----- | ----- | ----- |
| Events | C/R/U/D | R | R |
| Tenant Events | C/R/U/D | C/R/U | R |
| Chat | Full | Full | R/Send (safe-mode) |
| Jobs | Post/Approve | Post/Apply | Apply |
| Commercial Unions | Create/Moderate | Create/Join | \- |
| Surveys | Create/View | Vote/View | \- |

---

## **4\. 🌐 Language & Localization**

* **Primary Language**: Hebrew (RTL support mandatory)

* Future Proof: Internationalization (i18n) support for additional languages

---

## **5\. 📱 Functional Requirements by Module**

---

### **5.1 Events**

#### **a) Community Events**

* Created by **Admins**

* Includes title, description, date, location, optional attachments/photos

* Push notification support

#### **b) Tenant Events**

* Created by any user

* Admins can moderate or feature popular events

* RSVP support (attending/not attending)

* Comments & reactions (likes, emojis)

---

### **5.2 Chat (Facebook-like)**

* **Group & 1-on-1 Chat**

* **Public Groups** (joinable) and **Private Chats** (invitation-only)

* Group naming, icons, participant list

* Optional message moderation/reporting system

* Chat search and emoji support

* **Kids**: join only whitelisted groups or with parent approval (future enhancement: parental controls)

---

### **5.3 Jobs (Work Opportunities)**

#### **Use Case: Residents post needs, Youth offer help**

* **Job Post Fields**:

  * Title, Description, Location, Date & Time, Age Preference (optional), Skills (checkbox)

* Youth & Adults can **Apply/Accept** jobs

* Notification system for new jobs matching profile

* Post History & Status (Open, In Progress, Completed)

* No payment handling in-app

#### **Optional Enhancement:**

* Rating System (post-job)

* Chat between poster and applicant

---

### **5.4 Commercial Unions (Group Deals)**

* Users can **create/join groups** for specific services/products

* Group creator acts as **point-of-contact**

* Discussion thread per group

* Fields: Group Name, Category (TV, Internet, etc.), Description, Target Size

* Notification to members when group reaches threshold

* Admins can validate and highlight groups

#### **Optional:**

* Admin interface to **promote deals** externally

* Export group list for vendor negotiation

---

### **5.5 Surveys**

* Created by **Admins**

* Single or multi-question formats

* Answer types: Multiple choice, Yes/No, Rating scale

* Anonymous voting (optional toggle)

* Results visualized in graphs (bar, pie)

* Participation limited to adults (age filter)

#### **Admin Features:**

* Schedule opening/closing dates

* Mark "important" surveys

* Duplicate previous surveys

---

## **6\. ⚙️ Non-Functional Requirements**

### **6.1 Security**

* Secure token-based authentication (JWT or OAuth2)

* HTTPS encryption

* Basic user report/block mechanisms

* Backend validation on all forms

### **6.2 Performance**

* App loads under 2s on average mobile device

* Realtime updates for chat and job notification via WebSockets or Firebase

### **6.3 Scalability**

* Initially designed for \~1,000 concurrent users

* Use scalable backend (e.g., Node.js or Java Spring Boot) \+ Firebase/Firestore/PostgreSQL

### **6.4 Maintainability**

* Modular microservice backend

* Admin dashboard as a separate web interface (future phase)

---

## **7\. 📱 UI/UX Guidelines**

### **7.1 Hebrew-First UX**

* RTL layout

* Hebrew strings for all labels, buttons, etc.

### **7.2 Accessibility**

* Large tap targets

* Support voice over

* Adjustable text sizes

### **7.3 Design Concepts**

* Friendly, clean UI (community tone)

* Section-based navigation bar (Events, Chat, Jobs, Deals, Surveys)

* Badge icons for new notifications

---

## **8\. 🔔 Notifications & Alerts**

* Push notifications for:

  * New Event

  * Chat Message

  * Job Request

  * Survey

  * Group Reached Target (Commercial)

* In-app alerts

* Configurable in profile (opt-in/out per module)

---

## **9\. 🔧 Technical Stack Suggestion**

### **9.1 Frontend (Mobile)**

* React Native (cross-platform)

* Hebrew RTL support libraries

* Expo or Capacitor for build/deployment

### **9.2 Backend**

* Firebase (Authentication, Firestore, Realtime Chat, Push)

* or Node.js \+ PostgreSQL \+ Redis \+ WebSockets

### **9.3 Admin Interface**

* React \+ Material UI (Web App)

* Role-based controls for managing events/surveys

---

## **10\. 📈 Future Features (Optional Phase 2\)**

* **Parental Controls** for kids

* **Lost & Found** section

* **Shared Items** / Lending System (books, tools)

* **Ride Sharing**

* **Voting System for Admins or Committees**

* **AI-powered Job Matcher** (based on past activity)

---

## **11\. 📅 Timeline Estimate (MVP)**

| Phase | Features | Duration |
| ----- | ----- | ----- |
| Phase 1 | Login, Events, Chat | 4–6 weeks |
| Phase 2 | Jobs, Commercial, Survey | 4 weeks |
| Phase 3 | Admin Panel, Polish & QA | 3 weeks |

---

## **12\. 📋 Summary**

This app can become the **central digital hub** for your Kibbutz community, empowering residents to **self-organize**, **connect**, and **collaborate**. The modular approach allows for gradual rollout and growth over time.

Would you like a visual wireframe or flowchart next?

