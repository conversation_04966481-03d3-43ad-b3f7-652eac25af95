import { Platform } from 'react-native';

/**
 * Creates web-compatible container styles for screens
 * @param headerHeight - Height of the header in pixels (default: 60)
 * @returns Style object with web-specific properties
 */
export const createWebCompatibleContainerStyle = (headerHeight: number = 60) => ({
  flex: 1,
  backgroundColor: '#f5f7fa',
  ...(Platform.OS === 'web' && {
    height: '100vh' as any,
    maxHeight: '100vh' as any,
    overflow: 'hidden' as any,
  }),
});

/**
 * Creates web-compatible scroll content styles
 * @param padding - Padding for the content (default: 20)
 * @param headerHeight - Height of the header in pixels (default: 60)
 * @returns Style object with web-specific properties
 */
export const createWebCompatibleScrollStyle = (padding: number = 20, headerHeight: number = 60) => ({
  flex: 1,
  padding,
  ...(Platform.OS === 'web' && {
    height: `calc(100vh - ${headerHeight}px)` as any,
    overflowY: 'auto' as any,
    overflowX: 'hidden' as any,
  }),
});

/**
 * Creates web-compatible scroll content container styles
 * @param paddingBottom - Bottom padding for the content (default: 40)
 * @returns Style object with web-specific properties
 */
export const createWebCompatibleScrollContentStyle = (paddingBottom: number = 40) => ({
  paddingBottom,
  ...(Platform.OS !== 'web' && {
    minHeight: '100%' as any,
    flexGrow: 1,
  }),
});
