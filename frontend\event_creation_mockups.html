<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Event Creation & Detail Mockups - Village Community App</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        
        .export-note {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .mockups-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .phone-mockup {
            background: #1a1a1a;
            border-radius: 25px;
            padding: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            max-width: 350px;
            margin: 0 auto;
            position: relative;
        }
        
        .screen-label {
            position: absolute;
            top: -10px;
            left: 50%;
            transform: translateX(-50%);
            background: #667eea;
            color: white;
            padding: 5px 15px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            z-index: 10;
        }
        
        .screen {
            background: white;
            border-radius: 15px;
            height: 600px;
            overflow: hidden;
            position: relative;
        }
        
        .status-bar {
            background: #000;
            color: white;
            padding: 8px 15px;
            font-size: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .app-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .header-title {
            font-weight: bold;
            font-size: 18px;
        }
        
        .header-action {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 14px;
            cursor: pointer;
        }
        
        .back-button {
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            cursor: pointer;
            padding: 5px;
        }
        
        .content {
            padding: 20px;
            height: calc(100% - 120px);
            overflow-y: auto;
        }
        
        /* Form Styles */
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            color: #2c3e50;
            font-weight: 600;
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .form-input, .form-textarea, .form-select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #ecf0f1;
            border-radius: 10px;
            font-size: 14px;
            color: #2c3e50;
            background: white;
            transition: border-color 0.3s;
        }
        
        .form-input:focus, .form-textarea:focus, .form-select:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .form-textarea {
            resize: vertical;
            min-height: 80px;
        }
        
        .form-row {
            display: flex;
            gap: 15px;
        }
        
        .form-row .form-group {
            flex: 1;
        }
        
        .date-time-group {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .date-time-group .form-input {
            flex: 1;
        }
        
        .create-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            width: 100%;
            cursor: pointer;
            margin-top: 20px;
            transition: transform 0.2s;
        }
        
        .create-button:hover {
            transform: translateY(-2px);
        }
        
        /* Event Detail Card Styles */
        .event-hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
            position: relative;
        }
        
        .event-date-large {
            background: rgba(255,255,255,0.2);
            border-radius: 15px;
            padding: 15px;
            display: inline-block;
            margin-bottom: 15px;
        }
        
        .event-day-large {
            font-size: 36px;
            font-weight: bold;
            line-height: 1;
        }
        
        .event-month-large {
            font-size: 14px;
            text-transform: uppercase;
            margin-top: 5px;
        }
        
        .event-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
        }
        
        .event-subtitle {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .event-details {
            padding: 25px 20px;
        }
        
        .detail-item {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .detail-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 18px;
        }
        
        .detail-text h4 {
            color: #2c3e50;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .detail-text p {
            color: #7f8c8d;
            font-size: 13px;
            line-height: 1.4;
        }
        
        .event-description {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
        }
        
        .event-description h4 {
            color: #2c3e50;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .event-description p {
            color: #7f8c8d;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .action-buttons {
            display: flex;
            gap: 10px;
            padding: 20px;
            border-top: 1px solid #ecf0f1;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 10px;
            font-size: 14px;
            font-weight: 600;
            flex: 1;
            cursor: pointer;
        }
        
        .btn-secondary {
            background: #ecf0f1;
            color: #7f8c8d;
            border: none;
            padding: 12px 20px;
            border-radius: 10px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
        }
        
        .btn-share {
            background: #45b7d1;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 10px;
            font-size: 16px;
            cursor: pointer;
            min-width: 50px;
        }
        
        /* Attendees section */
        .attendees-section {
            padding: 20px;
            border-top: 1px solid #ecf0f1;
        }
        
        .attendees-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .attendees-header h4 {
            color: #2c3e50;
            font-size: 16px;
            font-weight: 600;
        }
        
        .attendee-count {
            color: #7f8c8d;
            font-size: 12px;
        }
        
        .attendees-list {
            display: flex;
            gap: 8px;
            overflow-x: auto;
            padding-bottom: 5px;
        }
        
        .attendee-avatar {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background: linear-gradient(45deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 12px;
            flex-shrink: 0;
        }
        
        /* Bottom Navigation */
        .bottom-nav {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid #ecf0f1;
            display: flex;
            padding: 10px 0;
        }
        
        .nav-item {
            flex: 1;
            text-align: center;
            padding: 8px;
            font-size: 24px;
            cursor: pointer;
            transition: transform 0.2s;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
        }
        
        .nav-item:hover {
            transform: scale(1.1);
        }
        
        .nav-item.active {
            color: #667eea;
        }
        
        .nav-label {
            font-size: 10px;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📅 Event Creation & Detail Mockups</h1>
        
        <div class="export-note">
            <h3>✨ New Event Mockups</h3>
            <p>Event creation form and detailed event card designs for your Village Community App</p>
            <p>Consistent with your existing design system and ready to export</p>
        </div>
        
        <div class="mockups-grid">
            <!-- Create Event Screen -->
            <div class="phone-mockup">
                <div class="screen-label">CREATE EVENT</div>
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>🔋 100%</span>
                    </div>
                    <div class="app-header">
                        <button class="back-button">←</button>
                        <div class="header-title">Create Event</div>
                        <button class="header-action">Save</button>
                    </div>
                    <div class="content">
                        <div class="form-group">
                            <label class="form-label">Event Title *</label>
                            <input type="text" class="form-input" placeholder="e.g. Summer BBQ Party" value="">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">Description</label>
                            <textarea class="form-textarea" placeholder="Tell everyone about your event..."></textarea>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">Date *</label>
                                <input type="date" class="form-input" value="2025-06-15">
                            </div>
                            <div class="form-group">
                                <label class="form-label">Time *</label>
                                <input type="time" class="form-input" value="14:00">
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">Location *</label>
                            <input type="text" class="form-input" placeholder="e.g. Central Park, Community Center" value="">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">Category</label>
                            <select class="form-select">
                                <option>Social Gathering</option>
                                <option>Sports</option>
                                <option>Educational</option>
                                <option>Community Service</option>
                                <option>Entertainment</option>
                                <option>Family Event</option>
                            </select>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">Max Attendees</label>
                                <input type="number" class="form-input" placeholder="Leave empty for unlimited" value="">
                            </div>
                            <div class="form-group">
                                <label class="form-label">Age Group</label>
                                <select class="form-select">
                                    <option>All Ages</option>
                                    <option>Kids (0-12)</option>
                                    <option>Teens (13-17)</option>
                                    <option>Adults (18+)</option>
                                    <option>Seniors (65+)</option>
                                </select>
                            </div>
                        </div>
                        
                        <button class="create-button">Create Event</button>
                    </div>
                    <div class="bottom-nav">
                        <div class="nav-item">
                            <div>🏠</div>
                            <div class="nav-label">Home</div>
                        </div>
                        <div class="nav-item">
                            <div>👥</div>
                            <div class="nav-label">People</div>
                        </div>
                        <div class="nav-item">
                            <div>💬</div>
                            <div class="nav-label">Chat</div>
                        </div>
                        <div class="nav-item">
                            <div>⚙️</div>
                            <div class="nav-label">More</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Event Detail Screen -->
            <div class="phone-mockup">
                <div class="screen-label">EVENT DETAIL</div>
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>🔋 100%</span>
                    </div>
                    <div class="app-header">
                        <button class="back-button">←</button>
                        <div class="header-title">Event Details</div>
                        <button class="header-action">⋯</button>
                    </div>
                    <div class="content">
                        <div class="event-hero">
                            <div class="event-date-large">
                                <div class="event-day-large">15</div>
                                <div class="event-month-large">JUN</div>
                            </div>
                            <div class="event-title">Village Summer Fair</div>
                            <div class="event-subtitle">Organized by Maria Rodriguez</div>
                        </div>
                        
                        <div class="event-details">
                            <div class="detail-item">
                                <div class="detail-icon">🕐</div>
                                <div class="detail-text">
                                    <h4>Time & Duration</h4>
                                    <p>Saturday, June 15 at 2:00 PM<br>Expected to last 4 hours</p>
                                </div>
                            </div>
                            
                            <div class="detail-item">
                                <div class="detail-icon">📍</div>
                                <div class="detail-text">
                                    <h4>Location</h4>
                                    <p>Central Park<br>Main entrance near the fountain</p>
                                </div>
                            </div>
                            
                            <div class="detail-item">
                                <div class="detail-icon">👥</div>
                                <div class="detail-text">
                                    <h4>Who's Coming</h4>
                                    <p>24 people attending<br>All ages welcome</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="event-description">
                            <h4>About This Event</h4>
                            <p>Join us for our annual Village Summer Fair! This family-friendly event features local vendors, delicious food, fun games for kids, live music, and community activities. It's a perfect opportunity to meet your neighbors and celebrate our wonderful community together.</p>
                        </div>
                        
                        <div class="attendees-section">
                            <div class="attendees-header">
                                <h4>Attendees</h4>
                                <span class="attendee-count">24 going</span>
                            </div>
                            <div class="attendees-list">
                                <div class="attendee-avatar">M</div>
                                <div class="attendee-avatar">J</div>
                                <div class="attendee-avatar">A</div>
                                <div class="attendee-avatar">S</div>
                                <div class="attendee-avatar">D</div>
                                <div class="attendee-avatar">L</div>
                                <div class="attendee-avatar">R</div>
                                <div class="attendee-avatar">+17</div>
                            </div>
                        </div>
                        
                        <div class="action-buttons">
                            <button class="btn-primary">I'm Going!</button>
                            <button class="btn-secondary">Maybe</button>
                            <button class="btn-share">📤</button>
                        </div>
                    </div>
                    <div class="bottom-nav">
                        <div class="nav-item">
                            <div>🏠</div>
                            <div class="nav-label">Home</div>
                        </div>
                        <div class="nav-item">
                            <div>👥</div>
                            <div class="nav-label">People</div>
                        </div>
                        <div class="nav-item">
                            <div>💬</div>
                            <div class="nav-label">Chat</div>
                        </div>
                        <div class="nav-item">
                            <div>⚙️</div>
                            <div class="nav-label">More</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Event Card Compact View -->
            <div class="phone-mockup">
                <div class="screen-label">EVENT CARD</div>
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>🔋 100%</span>
                    </div>
                    <div class="app-header">
                        <div class="header-title">📅 Upcoming Events</div>
                        <button class="header-action">+ Add</button>
                    </div>
                    <div class="content">
                        <!-- Featured Event Card -->
                        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 20px; padding: 25px; margin-bottom: 25px; color: white; position: relative; overflow: hidden;">
                            <div style="position: absolute; top: -20px; right: -20px; width: 100px; height: 100px; background: rgba(255,255,255,0.1); border-radius: 50%;"></div>
                            <div style="position: absolute; bottom: -30px; left: -30px; width: 80px; height: 80px; background: rgba(255,255,255,0.1); border-radius: 50%;"></div>
                            
                            <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 15px; position: relative; z-index: 2;">
                                <div>
                                    <div style="background: rgba(255,255,255,0.2); border-radius: 8px; padding: 8px 12px; display: inline-block; margin-bottom: 10px;">
                                        <div style="font-size: 18px; font-weight: bold; line-height: 1;">15</div>
                                        <div style="font-size: 10px; text-transform: uppercase;">JUN</div>
                                    </div>
                                </div>
                                <div style="text-align: right;">
                                    <div style="font-size: 12px; opacity: 0.8;">2:00 PM</div>
                                    <div style="font-size: 12px; opacity: 0.8;">Central Park</div>
                                </div>
                            </div>
                            
                            <div style="position: relative; z-index: 2;">
                                <h3 style="font-size: 20px; font-weight: bold; margin-bottom: 8px;">Village Summer Fair</h3>
                                <p style="font-size: 13px; opacity: 0.9; margin-bottom: 15px;">Annual community fair with games, food, and local vendors. Perfect for families!</p>
                                
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div style="display: flex; align-items: center; gap: 5px;">
                                        <div style="width: 20px; height: 20px; border-radius: 50%; background: rgba(255,255,255,0.3); display: flex; align-items: center; justify-content: center; font-size: 8px;">M</div>
                                        <div style="width: 20px; height: 20px; border-radius: 50%; background: rgba(255,255,255,0.3); display: flex; align-items: center; justify-content: center; font-size: 8px;">J</div>
                                        <div style="width: 20px; height: 20px; border-radius: 50%; background: rgba(255,255,255,0.3); display: flex; align-items: center; justify-content: center; font-size: 8px;">A</div>
                                        <span style="font-size: 11px; opacity: 0.8; margin-left: 5px;">+21 going</span>
                                    </div>
                                    <button style="background: rgba(255,255,255,0.2); border: none; color: white; padding: 6px 12px; border-radius: 20px; font-size: 12px; cursor: pointer;">Join Event</button>
                                </div>
                            </div>
                        </div>

                        <!-- Regular Event Cards -->
                        <div style="background: white; border-radius: 15px; padding: 20px; margin-bottom: 15px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); display: flex; align-items: center; transition: transform 0.2s; cursor: pointer;">
                            <div style="background: #4ecdc4; color: white; border-radius: 8px; padding: 8px; text-align: center; margin-right: 15px; min-width: 50px;">
                                <div style="font-size: 18px; font-weight: bold; line-height: 1;">18</div>
                                <div style="font-size: 10px; text-transform: uppercase;">JUN</div>
                            </div>
                            <div style="flex: 1;">
                                <h3 style="font-size: 16px; margin-bottom: 5px; color: #2c3e50;">Garden Workshop</h3>
                                <p style="font-size: 12px; color: #7f8c8d; line-height: 1.4; margin-bottom: 8px;">Learn sustainable gardening techniques</p>
                                <div style="display: flex; align-items: center; gap: 15px;">
                                    <span style="font-size: 11px; color: #bdc3c7;">🕐 10:00 AM</span>
                                    <span style="font-size: 11px; color: #bdc3c7;">📍 Garden Center</span>
                                    <span style="font-size: 11px; color: #bdc3c7;">👥 8 going</span>
                                </div>
                            </div>
                        </div>

                        <div style="background: white; border-radius: 15px; padding: 20px; margin-bottom: 15px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); display: flex; align-items: center; transition: transform 0.2s; cursor: pointer;">
                            <div style="background: #ff6b6b; color: white; border-radius: 8px; padding: 8px; text-align: center; margin-right: 15px; min-width: 50px;">
                                <div style="font-size: 18px; font-weight: bold; line-height: 1;">22</div>
                                <div style="font-size: 10px; text-transform: uppercase;">JUN</div>
                            </div>
                            <div style="flex: 1;">
                                <h3 style="font-size: 16px; margin-bottom: 5px; color: #2c3e50;">Soccer Tournament</h3>
                                <p style="font-size: 12px; color: #7f8c8d; line-height: 1.4; margin-bottom: 8px;">Local kids soccer tournament</p>
                                <div style="display: flex; align-items: center; gap: 15px;">
                                    <span style="font-size: 11px; color: #bdc3c7;">🕐 9:00 AM</span>
                                    <span style="font-size: 11px; color: #bdc3c7;">📍 Sports Field</span>
                                    <span style="font-size: 11px; color: #bdc3c7;">👥 15 going</span>
                                </div>
                            </div>
                        </div>

                        <div style="background: white; border-radius: 15px; padding: 20px; margin-bottom: 15px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); display: flex; align-items: center; transition: transform 0.2s; cursor: pointer;">
                            <div style="background: #f9ca24; color: white; border-radius: 8px; padding: 8px; text-align: center; margin-right: 15px; min-width: 50px;">
                                <div style="font-size: 18px; font-weight: bold; line-height: 1;">25</div>
                                <div style="font-size: 10px; text-transform: uppercase;">JUN</div>
                            </div>
                            <div style="flex: 1;">
                                <h3 style="font-size: 16px; margin-bottom: 5px; color: #2c3e50;">Movie Night</h3>
                                <p style="font-size: 12px; color: #7f8c8d; line-height: 1.4; margin-bottom: 8px;">Outdoor movie screening under stars</p>
                                <div style="display: flex; align-items: center; gap: 15px;">
                                    <span style="font-size: 11px; color: #bdc3c7;">🕐 8:00 PM</span>
                                    <span style="font-size: 11px; color: #bdc3c7;">📍 Town Square</span>
                                    <span style="font-size: 11px; color: #bdc3c7;">👥 32 going</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bottom-nav">
                        <div class="nav-item">
                            <div>🏠</div>
                            <div class="nav-label">Home</div>
                        </div>
                        <div class="nav-item">
                            <div>👥</div>
                            <div class="nav-label">People</div>
                        </div>
                        <div class="nav-item">
                            <div>💬</div>
                            <div class="nav-label">Chat</div>
                        </div>
                        <div class="nav-item">
                            <div>⚙️</div>
                            <div class="nav-label">More</div>
                        </div>
                    