import React from 'react';
import {
  View,
  Text,
  StyleSheet,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { getTextAlign } from '../../utils/rtl';
import Header from '../../components/Header';

export default function EditEventScreen() {
  const { t } = useTranslation();

  return (
    <View style={styles.container}>
      <Header
        title={t('events.editEvent')}
        showBackButton={true}
      />
      <View style={styles.content}>
        <Text style={[styles.title, { textAlign: getTextAlign() }]}>
          {t('events.editEvent')}
        </Text>
        <Text style={[styles.subtitle, { textAlign: getTextAlign() }]}>
          עריכת אירוע - בפיתוח
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f7fa',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
  },
});
