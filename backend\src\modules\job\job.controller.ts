import { Request, Response } from 'express';
import { JobService } from './job.service';
import { getModuleLogger } from '../../utils/logger';

const logger = getModuleLogger('Job');

export class JobController {
  constructor(private readonly jobService: JobService) {}

  async getJobs(_req: Request, res: Response) {
    try {
      logger.debug('GET /jobs - Request received');
      const jobs = await this.jobService.getJobs();
      logger.debug('Response body:', { count: jobs.length });
      return res.json(jobs);
    } catch (error) {
      logger.error('Error fetching jobs:', { error });
      return res.status(500).json({ message: 'Error fetching jobs', error });
    }
  }

  async createJob(req: Request, res: Response) {
    try {
      logger.debug('POST /jobs - Request received:', { body: req.body });

      // Add required fields for database
      const jobData = {
        ...req.body,
        community_id: "1", // Static community ID as requested
        posted_by_id: req.user?.id || "unknown", // User ID from authentication
        posted_by_name: req.user?.email || "Unknown User" // User name from authentication
      };

      const job = await this.jobService.createJob(jobData);
      logger.debug('Response body:', job);
      res.status(201).json(job);
    } catch (error) {
      logger.error('Error creating job:', { error });
      res.status(500).json({ message: 'Error creating job', error });
    }
  }

  async getJob(req: Request, res: Response) {
    try {
      const id = req.params['id'];
      if (!id) {
        return res.status(400).json({ message: 'Job ID is required' });
      }
      logger.debug('GET /jobs/:id - Request received:', { id });
      const job = await this.jobService.getJob(id as string);
      if (!job) {
        logger.debug('Job not found:', { id });
        return res.status(404).json({ message: 'Job not found' });
      }
      logger.debug('Response body:', job);
      return res.json(job);
    } catch (error) {
      logger.error('Error fetching job:', { error, id: req.params['id'] });
      return res.status(500).json({ message: 'Error fetching job', error });
    }
  }

  async updateJob(req: Request, res: Response) {
    try {
      const id = req.params['id'];
      if (!id) {
        return res.status(400).json({ message: 'Job ID is required' });
      }
      logger.debug('PUT /jobs/:id - Request received:', { id, body: req.body });
      const updatedJob = await this.jobService.updateJob(id as string, req.body);
      if (!updatedJob) {
        logger.debug('Job not found:', { id });
        return res.status(404).json({ message: 'Job not found' });
      }
      logger.debug('Response body:', updatedJob);
      return res.json(updatedJob);
    } catch (error: any) {
      if (error.code === 'P2025') {
        logger.debug('Job not found (Prisma P2025):', { id: req.params['id'] });
        return res.status(404).json({ message: 'Job not found' });
      }
      logger.error('Error updating job:', { error, id: req.params['id'] });
      return res.status(500).json({ message: 'Error updating job', error });
    }
  }

  async deleteJob(req: Request, res: Response) {
    try {
      const idParam = req.params['id'];
      if (typeof idParam !== 'string' || !idParam) {
        return res.status(400).json({ message: 'Job ID is required and must be a string' });
      }
      const id: string = idParam;
      logger.debug('DELETE /jobs/:id - Request received:', { id });
      await this.jobService.deleteJob(id);
      logger.debug('Job deleted successfully:', { id });
      return res.status(204).send();
    } catch (error: any) {
      if (error.code === 'P2025') {
        logger.debug('Job not found (Prisma P2025):', { id: req.params['id'] });
        return res.status(404).json({ message: 'Job not found' });
      }
      logger.error('Error deleting job:', { error, id: req.params['id'] });
      return res.status(500).json({ message: 'Error deleting job', error });
    }
  }
} 