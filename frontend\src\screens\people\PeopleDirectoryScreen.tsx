import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  StatusBar,
  Platform,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { useNavigation } from '@react-navigation/native';
import { useAuth } from '../../contexts/AuthContext';
import { getTextAlign } from '../../utils/rtl';
import Header from '../../components/Header';

interface Resident {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  user_type: string;
  role: string;
  is_active: boolean;
}

export default function PeopleDirectoryScreen() {
  const { t } = useTranslation();
  const { user, token } = useAuth();
  const [residents, setResidents] = useState<Resident[]>([]);
  const [filteredResidents, setFilteredResidents] = useState<Resident[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [sortOrder, setSortOrder] = useState<'A-Z' | 'Z-A'>('A-Z');

  useEffect(() => {
    fetchResidents();
  }, []);

  useEffect(() => {
    filterResidents();
  }, [searchQuery, residents, sortOrder]);

  const fetchResidents = async () => {
    try {
      setLoading(true);
      const response = await fetch('http://localhost:3002/apiusers', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          // Filter only active residents
          const activeResidents = data.data.filter((resident: Resident) => 
            resident.is_active && resident.user_type === 'RESIDENT'
          );
          setResidents(activeResidents);
        }
      } else {
        Alert.alert(t('common.error'), 'לא ניתן לטעון את רשימת התושבים');
      }
    } catch (error) {
      console.error('Error fetching residents:', error);
      Alert.alert(t('common.error'), 'בעיה בחיבור לשרת');
    } finally {
      setLoading(false);
    }
  };

  const filterResidents = () => {
    let filtered = residents.filter(resident => {
      const fullName = `${resident.first_name} ${resident.last_name}`.toLowerCase();
      const email = resident.email.toLowerCase();
      const query = searchQuery.toLowerCase();
      
      return fullName.includes(query) || email.includes(query);
    });

    // Sort residents
    filtered.sort((a, b) => {
      const nameA = `${a.first_name} ${a.last_name}`;
      const nameB = `${b.first_name} ${b.last_name}`;
      
      if (sortOrder === 'A-Z') {
        return nameA.localeCompare(nameB, 'he');
      } else {
        return nameB.localeCompare(nameA, 'he');
      }
    });

    setFilteredResidents(filtered);
  };

  const toggleSortOrder = () => {
    setSortOrder(prev => prev === 'A-Z' ? 'Z-A' : 'A-Z');
  };

  const getStatusText = (resident: Resident) => {
    // This is a placeholder - in a real app you might have last seen data
    const statuses = ['מחובר', 'לפני שעתיים', 'לפני יום', 'לפני 3 ימים'];
    return statuses[Math.floor(Math.random() * statuses.length)];
  };

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  const getRoleText = (role: string) => {
    switch (role) {
      case 'ADMIN':
        return 'מנהל';
      case 'USER':
        return 'תושב';
      default:
        return 'תושב';
    }
  };

  const handleContactResident = (resident: Resident) => {
    if (resident.phone) {
      Alert.alert(
        'פרטי קשר',
        `${resident.first_name} ${resident.last_name}\nטלפון: ${resident.phone}\nאימייל: ${resident.email}`,
        [
          { text: 'ביטול', style: 'cancel' },
          { text: 'התקשר', onPress: () => {/* Handle phone call */} },
          { text: 'שלח אימייל', onPress: () => {/* Handle email */} },
        ]
      );
    } else {
      Alert.alert(
        'פרטי קשר',
        `${resident.first_name} ${resident.last_name}\nאימייל: ${resident.email}`,
        [
          { text: 'ביטול', style: 'cancel' },
          { text: 'שלח אימייל', onPress: () => {/* Handle email */} },
        ]
      );
    }
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <ActivityIndicator size="large" color="#667eea" />
        <Text style={[styles.loadingText, { textAlign: getTextAlign() }]}>
          טוען רשימת תושבים...
        </Text>
      </View>
    );
  }

  const sortButton = (
    <TouchableOpacity style={styles.sortButton} onPress={toggleSortOrder}>
      <Text style={styles.sortButtonText}>{sortOrder}</Text>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <Header
        title="👥 מדריך הכפר"
        showBackButton={true}
        rightComponent={sortButton}
      />

      {/* Content */}
      <View style={styles.content}>
        <TextInput
          style={styles.searchBar}
          placeholder="🔍 חפש תושבים..."
          placeholderTextColor="#7f8c8d"
          value={searchQuery}
          onChangeText={setSearchQuery}
        />

        <ScrollView showsVerticalScrollIndicator={false}>
          {filteredResidents.map((resident) => (
            <TouchableOpacity
              key={resident.id}
              style={styles.residentCard}
              onPress={() => handleContactResident(resident)}
            >
              <View style={styles.avatar}>
                <Text style={styles.avatarText}>
                  {getInitials(resident.first_name, resident.last_name)}
                </Text>
              </View>
              <View style={styles.residentInfo}>
                <Text style={[styles.residentName, { textAlign: getTextAlign() }]}>
                  {resident.first_name} {resident.last_name}
                </Text>
                <Text style={[styles.residentDetails, { textAlign: getTextAlign() }]}>
                  {getRoleText(resident.role)} • {resident.email}
                </Text>
              </View>
              <Text style={[styles.statusText, { textAlign: getTextAlign() }]}>
                {getStatusText(resident)}
              </Text>
            </TouchableOpacity>
          ))}

          {filteredResidents.length === 0 && (
            <View style={styles.emptyState}>
              <Text style={[styles.emptyStateText, { textAlign: getTextAlign() }]}>
                {searchQuery ? 'לא נמצאו תושבים התואמים לחיפוש' : 'אין תושבים רשומים'}
              </Text>
            </View>
          )}
        </ScrollView>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f7fa',
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },

  sortButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    padding: 8,
    borderRadius: 8,
  },
  sortButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  searchBar: {
    backgroundColor: '#f8f9fa',
    borderRadius: 25,
    padding: 12,
    marginBottom: 20,
    fontSize: 14,
    textAlign: 'right',
  },
  residentCard: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderRadius: 15,
    padding: 15,
    marginBottom: 10,
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  avatar: {
    width: 45,
    height: 45,
    borderRadius: 22.5,
    backgroundColor: '#667eea',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  avatarText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  residentInfo: {
    flex: 1,
  },
  residentName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 4,
  },
  residentDetails: {
    fontSize: 12,
    color: '#7f8c8d',
  },
  statusText: {
    fontSize: 12,
    color: '#27ae60',
    fontWeight: '500',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#7f8c8d',
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  emptyStateText: {
    fontSize: 16,
    color: '#7f8c8d',
  },
});


