# RBAC (Role-Based Access Control) Implementation

## Overview

This document describes the implementation of Role-Based Access Control (RBAC) for the Kibuttz backend API. The RBAC system provides fine-grained access control based on user roles while maintaining flexibility for future requirements.

## Current Configuration

**As requested, all roles (ADMIN, MODERATOR, USER) currently have access to all API endpoints.** This allows for immediate deployment while providing the infrastructure to restrict access in the future.

## Architecture

### 1. RBAC Configuration (`src/config/rbac.ts`)

- **Purpose**: Centralized configuration for role-based access rules
- **Features**:
  - Declarative rule definitions
  - Wildcard path matching
  - HTTP method-specific rules (optional)
  - Helper functions for access checking

### 2. RBAC Middleware (`src/middleware/rbacMiddleware.ts`)

- **Purpose**: Enforces access control rules
- **Features**:
  - Automatic rule matching
  - Detailed logging for security auditing
  - Flexible role-based restrictions
  - Factory functions for specific role requirements

### 3. Enhanced Authentication (`src/middleware/auth.ts`)

- **Purpose**: Extended to fetch user roles from database
- **Features**:
  - Database role lookup
  - Enhanced user object with role information
  - Support for both Supabase and test environments

## File Structure

```
backend/src/
├── config/
│   └── rbac.ts                 # RBAC rules and helper functions
├── middleware/
│   ├── auth.ts                 # Enhanced authentication with role fetching
│   ├── mockAuth.ts             # Updated mock auth for testing
│   └── rbacMiddleware.ts       # RBAC enforcement middleware
├── routes/
│   └── index.ts                # Updated to apply RBAC middleware
└── __tests__/
    └── rbac.test.ts            # RBAC system tests
```

## Current RBAC Rules

All API endpoints currently allow access to all authenticated users:

- `/users*` - User management (ADMIN, MODERATOR, USER)
- `/events*` - Event management (ADMIN, MODERATOR, USER)
- `/food*` - Food management (ADMIN, MODERATOR, USER)
- `/jobs*` - Job management (ADMIN, MODERATOR, USER)
- `/surveys*` - Survey management (ADMIN, MODERATOR, USER)
- `/deals*` - Deal management (ADMIN, MODERATOR, USER)
- `/chat*` - Chat functionality (ADMIN, MODERATOR, USER)

## Future-Ready Rules

The system includes pre-configured rules for future use:

- `/admin/*` - Admin-only endpoints (ADMIN)
- `/moderator/*` - Moderator and admin endpoints (ADMIN, MODERATOR)

## Usage Examples

### Restricting Access to Specific Roles

To restrict an endpoint to admin-only access in the future:

```typescript
// In rbac.ts, update the rule:
{
  path: '/users/delete*',
  roles: ['ADMIN'],
  description: 'User deletion - admin only'
}
```

### Using Factory Functions

For route-specific restrictions:

```typescript
// In a router file:
import { requireAdmin, requireModeratorOrAdmin } from '../middleware/rbacMiddleware';

router.delete('/admin/users/:id', authenticateToken, requireAdmin(), deleteUser);
router.post('/moderate/content', authenticateToken, requireModeratorOrAdmin(), moderateContent);
```

## Security Features

1. **Default Deny**: Unknown paths are denied by default
2. **Audit Logging**: All access attempts are logged with user and endpoint details
3. **Role Validation**: User roles are fetched from database, not trusted from tokens
4. **Type Safety**: TypeScript interfaces ensure proper role handling

## Testing

The RBAC system includes comprehensive tests (`src/__tests__/rbac.test.ts`):

- Authentication requirement verification
- Role-based access testing
- RBAC configuration validation
- Helper function testing

Run tests with:
```bash
npm test -- rbac.test.ts
```

## Migration Path

To restrict access to specific endpoints:

1. Update the relevant rule in `src/config/rbac.ts`
2. Modify the `roles` array to include only allowed roles
3. Test the changes using the provided test suite
4. Deploy with confidence

## Monitoring and Debugging

The RBAC system provides detailed logging:

- **Access Granted**: User ID, role, endpoint, method
- **Access Denied**: User ID, role, required roles, endpoint
- **Configuration Issues**: Missing roles, invalid paths

Logs are available through the Winston logging system at the `RBACMiddleware` module level.

## Performance Considerations

- **Database Queries**: User roles are fetched once per request during authentication
- **Rule Matching**: Simple regex-based matching for fast path resolution
- **Caching**: Consider implementing role caching for high-traffic scenarios

## Next Steps

1. **Monitor Usage**: Review access logs to understand usage patterns
2. **Gradual Restriction**: Implement role restrictions based on business requirements
3. **Advanced Features**: Consider implementing resource-based permissions if needed
4. **Performance Optimization**: Add role caching if database queries become a bottleneck
