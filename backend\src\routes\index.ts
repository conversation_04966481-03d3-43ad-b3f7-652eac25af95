import { Router } from 'express';
import { getModuleLogger } from '../utils/logger';
import { authenticateToken } from '../middleware/auth';
import { rbacMiddleware } from '../middleware/rbacMiddleware';
import authRouter from '../modules/auth/router';
import userRouter from '../modules/user/router';
import eventRouter from '../modules/event/event.router';
import foodRouter from '../modules/food/food.router';
import jobRouter from '../modules/job/job.router';
import surveyRouter from '../modules/survey/survey.router';
import dealRouter from '../modules/deal/deal.router';
import chatRouter from '../modules/chat/chat.router';
import debugRouter from '../modules/debug/router';

const logger = getModuleLogger('Router');

// Add debug logging to verify log level
logger.debug('Router module initialized with level:', { level: logger.getLevel() });

const router = Router();

// Log all requests to the router
router.use((req, _res, next) => {
  logger.debug('Router received request:', {
    method: req.method,
    url: req.url,
    path: req.path,
    params: req.query
  });
  next();
});

// Public routes (no authentication required)
router.use('/auth', authRouter);
logger.info('Auth routes registered at /api/auth (public)');

// Protected routes (authentication + RBAC required)
router.use('/users', authenticateToken, rbacMiddleware, userRouter);
logger.info('User routes registered at /api/users (protected with RBAC)');

router.use('/events', authenticateToken, rbacMiddleware, eventRouter);
logger.info('Event routes registered at /api/events (protected with RBAC)');

router.use('/food', authenticateToken, rbacMiddleware, foodRouter);
logger.info('Food routes registered at /api/food (protected with RBAC)');

router.use('/jobs', authenticateToken, rbacMiddleware, jobRouter);
logger.info('Job routes registered at /api/jobs (protected with RBAC)');

router.use('/surveys', authenticateToken, rbacMiddleware, surveyRouter);
logger.info('Survey routes registered at /api/surveys (protected with RBAC)');

router.use('/deals', authenticateToken, rbacMiddleware, dealRouter);
logger.info('Deal routes registered at /api/deals (protected with RBAC)');

router.use('/chat', authenticateToken, rbacMiddleware, chatRouter);
logger.info('Chat routes registered at /api/chat (protected with RBAC)');

router.use('/debug', authenticateToken, rbacMiddleware, debugRouter);
logger.info('Debug routes registered at /api/debug (protected with RBAC - admin only)');

export default router;
