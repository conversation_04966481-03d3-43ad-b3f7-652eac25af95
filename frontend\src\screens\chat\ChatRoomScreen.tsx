import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useRoute, useNavigation } from '@react-navigation/native';
import { RouteProp } from '@react-navigation/native';
import { useTranslation } from 'react-i18next';
import { getTextAlign } from '../../utils/rtl';
import { chatService, ChatMessage } from '../../services/chat';
import { ChatStackParamList } from '../../navigation/types';
import Header from '../../components/Header';

type ChatRoomScreenRouteProp = RouteProp<ChatStackParamList, 'ChatRoom'>;

const mockMessages = [
  {
    id: '1',
    text: 'שלום לכולם! מתי האסיפה הבאה?',
    senderId: 'user1',
    senderName: 'דני כהן',
    timestamp: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
    isOwn: false,
    groupId: '1',
    avatarUrl: 'D',
    avatarDataAiHint: null,
  },
  {
    id: '2',
    text: 'היי דני, האסיפה תהיה ביום רביעי בשעה 19:00',
    senderId: 'current',
    senderName: 'אני',
    timestamp: new Date(Date.now() - 28 * 60 * 1000), // 28 minutes ago
    isOwn: true,
    groupId: '1',
    avatarUrl: 'א',
    avatarDataAiHint: null,
  },
  {
    id: '3',
    text: 'תודה רבה!',
    senderId: 'user1',
    senderName: 'דני כהן',
    timestamp: new Date(Date.now() - 27 * 60 * 1000), // 27 minutes ago
    isOwn: false,
    groupId: '1',
    avatarUrl: 'D',
    avatarDataAiHint: null,
  },
  {
    id: '4',
    text: 'גם אני אגיע. יש צורך להביא משהו?',
    senderId: 'user2',
    senderName: 'מריה רודריגז',
    timestamp: new Date(Date.now() - 25 * 60 * 1000), // 25 minutes ago
    isOwn: false,
    groupId: '1',
    avatarUrl: 'M',
    avatarDataAiHint: null,
  },
  {
    id: '5',
    text: 'לא צריך להביא כלום, רק את עצמכם 😊',
    senderId: 'current',
    senderName: 'אני',
    timestamp: new Date(Date.now() - 20 * 60 * 1000), // 20 minutes ago
    isOwn: true,
    groupId: '1',
    avatarUrl: 'א',
    avatarDataAiHint: null,
  },
];

export default function ChatRoomScreen() {
  const { t } = useTranslation();
  const route = useRoute<ChatRoomScreenRouteProp>();
  const navigation = useNavigation();
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [loading, setLoading] = useState(true);
  const [groupName, setGroupName] = useState('צ\'אט');

  const { roomId } = route.params;

  useEffect(() => {
    loadMessages();
    loadGroupInfo();
  }, [roomId]);

  const loadMessages = async () => {
    try {
      console.log('🔄 Loading messages for room:', roomId);
      const fetchedMessages = await chatService.getChatMessages(roomId);
      setMessages(fetchedMessages);
      console.log('✅ Messages loaded successfully:', fetchedMessages.length);
    } catch (error) {
      console.error('❌ Error loading messages:', error);
      // Fallback to mock data if API fails
      setMessages(mockMessages);
      console.log('⚠️ Using mock data due to API failure');
    } finally {
      setLoading(false);
    }
  };

  const loadGroupInfo = async () => {
    try {
      const group = await chatService.getChatGroup(roomId);
      setGroupName(group.name);
    } catch (error) {
      console.error('❌ Error loading group info:', error);
      setGroupName('קבוצת הורים'); // Fallback name
    }
  };

  const formatTime = (timestamp: Date | string) => {
    const date = typeof timestamp === 'string' ? new Date(timestamp) : timestamp;
    return date.toLocaleTimeString('he-IL', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
    });
  };

  const renderMessage = ({ item }: { item: any }) => {
    const timeDisplay = formatTime(item.timestamp);

    return (
      <View style={[
        styles.messageContainer,
        item.isOwn ? styles.ownMessage : styles.otherMessage
      ]}>
        {!item.isOwn && (
          <View style={styles.messageHeader}>
            <View style={styles.senderAvatar}>
              <Text style={styles.senderAvatarText}>{item.avatarUrl || item.senderName[0]}</Text>
            </View>
            <Text style={styles.senderName}>{item.senderName}</Text>
          </View>
        )}
        <Text style={[
          styles.messageText,
          { textAlign: getTextAlign() },
          item.isOwn ? styles.ownMessageText : styles.otherMessageText
        ]}>
          {item.text}
        </Text>
        <Text style={[
          styles.messageTime,
          item.isOwn ? styles.ownMessageTime : styles.otherMessageTime
        ]}>
          {timeDisplay}
        </Text>
      </View>
    );
  };

  const sendMessage = async () => {
    if (message.trim()) {
      try {
        const newMessage = {
          groupId: roomId,
          senderId: 'currentUser', // TODO: Get from auth context
          senderName: 'אני',
          text: message.trim(),
          avatarUrl: 'א',
          avatarDataAiHint: null,
        };

        await chatService.sendMessage(newMessage);
        setMessage('');
        // Reload messages to show the new one
        loadMessages();
      } catch (error) {
        console.error('❌ Error sending message:', error);
        // For now, just clear the input even if sending fails
        setMessage('');
      }
    }
  };

  if (loading) {
    return (
      <View style={styles.centerContainer}>
        <Text>טוען הודעות...</Text>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <Header
        title={`💬 ${groupName}`}
        showBackButton={true}
      />

      <FlatList
        data={messages}
        renderItem={renderMessage}
        keyExtractor={(item) => item.id}
        style={styles.messagesList}
        contentContainerStyle={styles.messagesContainer}
        inverted
        showsVerticalScrollIndicator={false}
      />

      <View style={styles.inputContainer}>
        <TextInput
          style={[styles.textInput, { textAlign: getTextAlign() }]}
          placeholder={t('chat.typeMessage')}
          value={message}
          onChangeText={setMessage}
          multiline
          maxLength={500}
        />
        <TouchableOpacity
          style={[styles.sendButton, !message.trim() && styles.sendButtonDisabled]}
          onPress={sendMessage}
          disabled={!message.trim()}
        >
          <Text style={styles.sendButtonText}>{t('chat.send')}</Text>
        </TouchableOpacity>
      </View>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  messagesList: {
    flex: 1,
    paddingHorizontal: 15,
  },
  messagesContainer: {
    paddingVertical: 10,
  },
  messageContainer: {
    maxWidth: '80%',
    marginVertical: 4,
    padding: 12,
    borderRadius: 18,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  ownMessage: {
    alignSelf: 'flex-end',
    backgroundColor: '#45b7d1',
    marginLeft: '20%',
  },
  otherMessage: {
    alignSelf: 'flex-start',
    backgroundColor: 'white',
    marginRight: '20%',
  },
  messageHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  senderAvatar: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#45b7d1',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  senderAvatarText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  senderName: {
    fontSize: 12,
    color: '#666',
    fontWeight: '600',
  },
  messageText: {
    fontSize: 16,
    lineHeight: 20,
    marginBottom: 4,
  },
  ownMessageText: {
    color: 'white',
  },
  otherMessageText: {
    color: '#333',
  },
  messageTime: {
    fontSize: 11,
    alignSelf: 'flex-end',
  },
  ownMessageTime: {
    color: 'rgba(255, 255, 255, 0.8)',
  },
  otherMessageTime: {
    color: '#999',
  },
  inputContainer: {
    flexDirection: 'row',
    padding: 15,
    backgroundColor: 'white',
    alignItems: 'flex-end',
    borderTopWidth: 1,
    borderTopColor: '#e1e8ed',
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#e1e8ed',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 10,
    marginRight: 10,
    maxHeight: 100,
    fontSize: 16,
    backgroundColor: '#f8f9fa',
  },
  sendButton: {
    backgroundColor: '#45b7d1',
    borderRadius: 20,
    paddingHorizontal: 20,
    paddingVertical: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButtonDisabled: {
    backgroundColor: '#bdc3c7',
  },
  sendButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 14,
  },
});
