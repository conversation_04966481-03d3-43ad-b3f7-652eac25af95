import { Router } from 'express';
import { getModuleLogger } from '../../utils/logger';

const router = Router();
const logger = getModuleLogger('Common');

// Add debug logging to verify log level
logger.debug('Common module initialized with level:', { level: logger.getLevel() });

router.get('/hello', (req, res) => {
  try {
    logger.debug('Hello endpoint called', { 
      timestamp: new Date().toISOString(),
      method: req.method,
      path: req.path
    });
    
    res.status(200).json({ message: 'Hello from Natoon Home API!' });
    
    logger.debug('Hello endpoint response sent successfully', {
      statusCode: 200,
      timestamp: new Date().toISOString()
    });

    logger.info(`${req.method} ${req.path} - Status: 200`);
  } catch (error) {
    logger.error('Error in hello endpoint', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString()
    });
    
    res.status(500).json({ 
      success: false, 
      error: 'Internal server error' 
    });
  }
});

export default router; 