import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import { Picker } from '@react-native-picker/picker';
import { useTranslation } from 'react-i18next';
import { apiService } from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';
import { getTextAlign } from '../../utils/rtl';

interface TableEntry {
  [key: string]: any;
}

interface DebugData {
  tables: string[];
  selectedTable: string | null;
  tableEntries: TableEntry[];
  totalEntries: number;
  loading: boolean;
  error: string | null;
}

interface DebugMenuItem {
  id: string;
  label: string;
  icon: string;
}

type DebugSection = 'tables' | 'logs' | 'cache' | 'settings';

export default function DebugScreen() {
  const { t } = useTranslation();
  const { user } = useAuth();

  // Debug logging for DebugScreen
  console.log('🔧 DebugScreen - User data:', {
    hasUser: !!user,
    userId: user?.id,
    userRole: user?.role,
    isAdmin: user?.role === 'ADMIN'
  });

  const [activeSection, setActiveSection] = useState<DebugSection>('tables');
  const [debugData, setDebugData] = useState<DebugData>({
    tables: [],
    selectedTable: null,
    tableEntries: [],
    totalEntries: 0,
    loading: false,
    error: null,
  });

  // Check if user is admin
  const isAdmin = user?.role === 'ADMIN';

  // Debug menu items
  const debugMenuItems: DebugMenuItem[] = [
    { id: 'tables', label: t('debug.tables'), icon: '🗃️' },
    { id: 'logs', label: t('debug.logs'), icon: '📋' },
    { id: 'cache', label: t('debug.cache'), icon: '💾' },
    { id: 'settings', label: t('debug.settings'), icon: '⚙️' },
  ];

  useEffect(() => {
    if (isAdmin) {
      loadTables();
    }
  }, [isAdmin]);

  const loadTables = async () => {
    setDebugData(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      const response = await apiService.getDebugTables();
      if (response.error) {
        throw new Error(response.error);
      }
      
      setDebugData(prev => ({
        ...prev,
        tables: response.data?.tables || [],
        loading: false,
      }));
    } catch (error) {
      console.error('Error loading tables:', error);
      setDebugData(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Unknown error',
        loading: false,
      }));
    }
  };

  const loadTableEntries = async (tableName: string) => {
    setDebugData(prev => ({ 
      ...prev, 
      loading: true, 
      error: null,
      selectedTable: tableName,
      tableEntries: [],
      totalEntries: 0,
    }));
    
    try {
      const response = await apiService.getDebugTableEntries(tableName, 100);
      if (response.error) {
        throw new Error(response.error);
      }
      
      setDebugData(prev => ({
        ...prev,
        tableEntries: response.data?.entries || [],
        totalEntries: response.data?.total || 0,
        loading: false,
      }));
    } catch (error) {
      console.error('Error loading table entries:', error);
      setDebugData(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Unknown error',
        loading: false,
      }));
    }
  };

  const handleTableSelect = (tableName: string) => {
    if (tableName && tableName !== debugData.selectedTable) {
      loadTableEntries(tableName);
    }
  };

  const refreshData = () => {
    if (debugData.selectedTable) {
      loadTableEntries(debugData.selectedTable);
    } else {
      loadTables();
    }
  };

  const renderTableEntry = (entry: TableEntry, index: number) => {
    const values = Object.values(entry);

    return (
      <View key={index} style={styles.dataRow}>
        <Text style={styles.dataCell}>{index + 1}</Text>
        {values.map((value, valueIndex) => (
          <Text key={valueIndex} style={styles.dataCell}>
            {value !== null && value !== undefined ? String(value) : 'null'}
          </Text>
        ))}
      </View>
    );
  };

  const renderTablesSection = () => {
    return (
      <View style={styles.contentSection}>
        {/* Table Selector */}
        <View style={styles.selectorContainer}>
          <Text style={[styles.selectorLabel, { textAlign: getTextAlign() }]}>
            {t('debug.selectTable')}
          </Text>
          <View style={styles.pickerContainer}>
            <Picker
              selectedValue={debugData.selectedTable || ''}
              onValueChange={handleTableSelect}
              style={styles.picker}
              enabled={!debugData.loading}
            >
              <Picker.Item label={t('debug.selectTable')} value="" />
              {debugData.tables.map((table) => (
                <Picker.Item key={table} label={table} value={table} />
              ))}
            </Picker>
          </View>
        </View>

        {/* Loading Indicator */}
        {debugData.loading && (
          <View style={styles.centerContent}>
            <ActivityIndicator size="large" color="#667eea" />
            <Text style={[styles.bodyText, { marginTop: 16, textAlign: 'center' }]}>
              {t('debug.loading')}
            </Text>
          </View>
        )}

        {/* Error Display */}
        {debugData.error && (
          <View style={styles.errorContainer}>
            <Text style={[styles.errorText, { textAlign: 'center' }]}>
              {t('debug.error')}: {debugData.error}
            </Text>
          </View>
        )}

        {/* Table Entries */}
        {!debugData.loading && !debugData.error && debugData.selectedTable && (
          <View style={styles.entriesContainer}>
            <View style={styles.entriesHeader}>
              <Text style={[styles.entriesHeaderTitle, { textAlign: getTextAlign() }]}>
                {t('debug.tableEntries')}: {debugData.selectedTable}
              </Text>
              <Text style={styles.entriesHeaderSubtitle}>
                {t('debug.totalEntries')}: {debugData.totalEntries}
              </Text>
            </View>

            <ScrollView style={styles.entriesList} showsVerticalScrollIndicator={false}>
              {debugData.tableEntries.length > 0 ? (
                <>
                  {renderTableHeader()}
                  {debugData.tableEntries.map((entry, index) => renderTableEntry(entry, index))}
                </>
              ) : (
                <View style={styles.centerContent}>
                  <Text style={[styles.bodyText, { textAlign: 'center' }]}>
                    {t('debug.noEntries')}
                  </Text>
                </View>
              )}
            </ScrollView>
          </View>
        )}

        {/* No Tables Message */}
        {!debugData.loading && !debugData.error && debugData.tables.length === 0 && (
          <View style={styles.centerContent}>
            <Text style={[styles.bodyText, { textAlign: 'center' }]}>
              {t('debug.noTables')}
            </Text>
          </View>
        )}
      </View>
    );
  };

  const renderActiveSection = () => {
    switch (activeSection) {
      case 'tables':
        return renderTablesSection();
      case 'logs':
        return (
          <View style={styles.contentSection}>
            <View style={styles.centerContent}>
              <Text style={styles.bodyText}>{t('debug.logsComingSoon')}</Text>
            </View>
          </View>
        );
      case 'cache':
        return (
          <View style={styles.contentSection}>
            <View style={styles.centerContent}>
              <Text style={styles.bodyText}>{t('debug.cacheComingSoon')}</Text>
            </View>
          </View>
        );
      case 'settings':
        return (
          <View style={styles.contentSection}>
            <View style={styles.centerContent}>
              <Text style={styles.bodyText}>{t('debug.settingsComingSoon')}</Text>
            </View>
          </View>
        );
      default:
        return renderTablesSection();
    }
  };

  const renderSidebar = () => {
    return (
      <View style={styles.sidebar}>
        <Text style={styles.sidebarTitle}>{t('debug.management')}</Text>
        {debugMenuItems.map((item) => (
          <TouchableOpacity
            key={item.id}
            style={[
              styles.sidebarItem,
              activeSection === item.id && styles.sidebarItemActive
            ]}
            onPress={() => setActiveSection(item.id as DebugSection)}
          >
            <Text style={styles.sidebarIcon}>{item.icon}</Text>
            <Text style={[
              styles.sidebarLabel,
              activeSection === item.id && styles.sidebarLabelActive
            ]}>
              {item.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  const renderTableHeader = () => {
    if (!debugData.selectedTable || debugData.tableEntries.length === 0) return null;

    const firstEntry = debugData.tableEntries[0];
    const headers = Object.keys(firstEntry);

    return (
      <View style={styles.headerRow}>
        <Text style={styles.headerCell}>#</Text>
        {headers.map((header, index) => (
          <Text key={index} style={styles.headerCell}>
            {header}
          </Text>
        ))}
      </View>
    );
  };

  if (!isAdmin) {
    return (
      <View style={styles.container}>
        <View style={styles.centerContent}>
          <Text style={[styles.errorText, { textAlign: 'center' }]}>
            {t('common.error')}
          </Text>
          <Text style={[styles.bodyText, { textAlign: 'center', marginTop: 16 }]}>
            Access denied. Admin privileges required.
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={[styles.headerTitle, { textAlign: getTextAlign() }]}>
          {t('debug.title')}
        </Text>
        <TouchableOpacity
          style={styles.refreshButton}
          onPress={refreshData}
          disabled={debugData.loading}
        >
          <Text style={styles.refreshButtonText}>
            {t('debug.refreshData')}
          </Text>
        </TouchableOpacity>
      </View>

      {/* Main Content with Sidebar */}
      <View style={styles.mainContent}>
        {renderActiveSection()}
        {renderSidebar()}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  refreshButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    backgroundColor: '#667eea',
  },
  refreshButtonText: {
    color: '#fff',
    fontWeight: '500',
  },
  // Main Content Layout
  mainContent: {
    flex: 1,
    flexDirection: 'row',
  },
  // Sidebar Styles
  sidebar: {
    width: 200,
    backgroundColor: '#2c3e50',
    paddingVertical: 16,
    borderLeftWidth: 1,
    borderLeftColor: '#34495e',
  },
  sidebarTitle: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    paddingHorizontal: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#34495e',
    marginBottom: 8,
  },
  sidebarItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginHorizontal: 8,
    borderRadius: 8,
  },
  sidebarItemActive: {
    backgroundColor: '#667eea',
  },
  sidebarIcon: {
    fontSize: 18,
    marginRight: 12,
    width: 24,
    textAlign: 'center',
  },
  sidebarLabel: {
    color: '#bdc3c7',
    fontSize: 14,
    fontWeight: '500',
  },
  sidebarLabelActive: {
    color: '#fff',
    fontWeight: '600',
  },
  // Content Section
  contentSection: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  selectorContainer: {
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  selectorLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2c3e50',
    marginBottom: 12,
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    overflow: 'hidden',
    backgroundColor: '#fff',
  },
  picker: {
    height: 50,
    color: '#2c3e50',
  },
  centerContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  errorContainer: {
    margin: 16,
    padding: 16,
    borderRadius: 8,
    backgroundColor: '#ffebee',
  },
  errorText: {
    color: '#c62828',
    fontSize: 16,
    fontWeight: '600',
  },
  bodyText: {
    color: '#2c3e50',
    fontSize: 14,
  },
  entriesContainer: {
    flex: 1,
  },
  entriesHeader: {
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  entriesHeaderTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2c3e50',
  },
  entriesHeaderSubtitle: {
    fontSize: 14,
    color: '#7f8c8d',
    marginTop: 4,
  },
  entriesList: {
    flex: 1,
    padding: 16,
  },
  // Table Header Styles
  headerRow: {
    flexDirection: 'row',
    backgroundColor: '#667eea',
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderRadius: 8,
    marginBottom: 2,
  },
  headerCell: {
    flex: 1,
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 12,
    textAlign: 'center',
    paddingHorizontal: 4,
  },
  // Table Data Styles
  dataRow: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    paddingVertical: 8,
    paddingHorizontal: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    minHeight: 40,
    alignItems: 'center',
  },
  dataCell: {
    flex: 1,
    color: '#2c3e50',
    fontSize: 11,
    textAlign: 'center',
    paddingHorizontal: 4,
  },
  // Legacy styles (keeping for compatibility)
  entryContainer: {
    marginBottom: 16,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    backgroundColor: '#fff',
  },
  entryIndex: {
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#667eea',
    fontSize: 14,
  },
  entryRow: {
    flexDirection: 'row',
    marginBottom: 4,
    flexWrap: 'wrap',
  },
  entryKey: {
    fontWeight: 'bold',
    minWidth: 100,
    marginRight: 8,
    color: '#7f8c8d',
    fontSize: 12,
  },
  entryValue: {
    flex: 1,
    color: '#2c3e50',
    fontSize: 14,
  },
});
