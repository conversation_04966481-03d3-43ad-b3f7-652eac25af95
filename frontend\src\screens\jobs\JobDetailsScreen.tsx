import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { getTextAlign } from '../../utils/rtl';
import Header from '../../components/Header';
import WebCompatibleScrollView from '../../components/WebCompatibleScrollView';
import { createWebCompatibleContainerStyle, createWebCompatibleScrollContentStyle } from '../../utils/webStyles';

export default function JobDetailsScreen() {
  const { t } = useTranslation();

  return (
    <View style={styles.container}>
      <Header
        title="פרטי העבודה"
        showBackButton={true}
      />
      <WebCompatibleScrollView style={styles.content} contentContainerStyle={styles.scrollContent}>
        <Text style={[styles.title, { textAlign: getTextAlign() }]}>
          פרטי העבודה
        </Text>
        <Text style={[styles.subtitle, { textAlign: getTextAlign() }]}>
          פרטי העבודה יוצגו כאן
        </Text>

        <TouchableOpacity style={styles.button}>
          <Text style={[styles.buttonText, { textAlign: getTextAlign() }]}>הגש מועמדות</Text>
        </TouchableOpacity>
      </WebCompatibleScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: createWebCompatibleContainerStyle(),
  content: {
    padding: 20,
  },
  scrollContent: createWebCompatibleScrollContentStyle(),
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 20,
  },
  button: {
    backgroundColor: '#007AFF',
    borderRadius: 8,
    padding: 15,
    alignItems: 'center',
    marginTop: 20,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});
