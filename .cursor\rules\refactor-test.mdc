---
description: 
globs: 
alwaysApply: false
---
Goal:
Refactor all API tests to properly use Supabase for authentication and enforce realistic end-to-end flows.

Rules:

    Supabase Auth:
    Supabase is already running and used for user authentication (sign-up, sign-in, sign-out, delete).

    Per-Test User Lifecycle:

        Every test must run in the context of a user.

        The test must explicitly create the required user(s) at the beginning of the test. (1st register in Supa Auth and than create it in the backend - sam as the front end do)

        The test must delete the user(s) at the end of the test.

        No global, shared, or pre-existing users may be used across tests.

    Token Handling:

        On successful registration, the API returns an access token.

        The test must use this token to perform actions on behalf of the user.

    Multi-user Tests:

        For tests involving multiple users, each user must be registered independently and deleted at the end.

        Each user’s access token must be managed and used explicitly in the test.

    Wrapper Implementation:

        Create a test utility wrapper with the following API:

            signUp()

            signIn()

            signOut()

            deleteUser()

            getUserToken()

        All tests shall use this wrapper to handle user context consistently and enforce the per-test user lifecycle.

    Public API Only:

        All API tests must exercise the public API only (no internal calls, no test-only endpoints).

    Remove Legacy Code:

        Remove any leftover auth mocks or test scaffolding that bypass authentication.

        All authentication must go through Supabase.

Notes:

    Each test must be fully isolated: it creates its own user(s), manages tokens, and cleans up all resources.

    The wrapper should support setup/teardown hooks to simplify user lifecycle management per test.


    Tests must not rely on users created by other tests or test runners.