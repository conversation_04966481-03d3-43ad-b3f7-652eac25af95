# Testing Guide for Kibbutz Community App

## Prerequisites

1. **Node.js**: Make sure you have Node.js 18+ installed
2. **Expo CLI**: Install globally with `npm install -g @expo/cli`
3. **Expo Go App**: Install on your phone for testing

## Step 1: Install Dependencies

Navigate to the KibbutzApp directory and run:

```bash
cd KibbutzApp
npm install
```

If you encounter dependency conflicts, try:
```bash
npm install --legacy-peer-deps
```

Or use Expo's install command:
```bash
npx expo install --fix
```

## Step 2: Fix Common Issues

### Issue 1: Missing Assets
Create placeholder assets in the `assets` folder:

```bash
mkdir assets
# Add placeholder images (you can use any PNG files for now)
```

### Issue 2: Firebase Configuration
Update `src/config/firebase.ts` with your actual Firebase config:

```typescript
const firebaseConfig = {
  apiKey: "your-api-key",
  authDomain: "your-project.firebaseapp.com",
  projectId: "your-project-id",
  storageBucket: "your-project.appspot.com",
  messagingSenderId: "123456789",
  appId: "your-app-id"
};
```

### Issue 3: Expo Localization
Add to package.json dependencies:
```json
"expo-localization": "~15.0.3"
```

## Step 3: Start the Development Server

```bash
npm start
```

Or:
```bash
npx expo start
```

This will open the Expo DevTools in your browser.

## Step 4: Test on Device/Simulator

### Option A: Physical Device
1. Install "Expo Go" app on your phone
2. Scan the QR code from the terminal/browser
3. The app should load on your device

### Option B: iOS Simulator (Mac only)
1. Press `i` in the terminal
2. iOS Simulator will open with the app

### Option C: Android Emulator
1. Start Android Studio and create an emulator
2. Press `a` in the terminal
3. The app will load in the emulator

### Option D: Web Browser
1. Press `w` in the terminal
2. The app will open in your browser (limited functionality)

## Step 5: Test App Features

### Authentication Flow
1. App should start with login screen
2. Try registering a new user
3. Test login with created credentials
4. Test forgot password functionality

### Navigation
1. After login, you should see bottom tabs
2. Navigate between different modules
3. Test stack navigation within modules

### Hebrew RTL Support
1. Check if Hebrew text displays correctly
2. Verify right-to-left text alignment
3. Test form inputs with Hebrew text

## Common Issues and Solutions

### Issue: Metro bundler fails
**Solution**: Clear cache and restart
```bash
npx expo start --clear
```

### Issue: Firebase errors
**Solution**: 
1. Check Firebase configuration
2. Ensure Firebase project is set up correctly
3. Check internet connection

### Issue: Navigation errors
**Solution**: 
1. Check if all screen components are properly exported
2. Verify navigation types match actual screens

### Issue: i18n not working
**Solution**:
1. Check if translation files exist
2. Verify i18n initialization in App.tsx

### Issue: Dependency conflicts
**Solution**:
```bash
rm -rf node_modules package-lock.json
npm install --legacy-peer-deps
```

## Development Workflow

1. **Make Changes**: Edit files in `src/` directory
2. **Hot Reload**: Changes should appear automatically
3. **Debug**: Use React Native Debugger or browser dev tools
4. **Test**: Test on multiple devices/screen sizes

## Next Steps After Basic Testing

1. **Set up Firebase**: Follow FIREBASE_SETUP_GUIDE.md
2. **Test Authentication**: Register and login users
3. **Test Data Flow**: Create events, send messages
4. **Add Real Data**: Replace mock data with Firebase calls
5. **Implement Real-time Features**: Chat, notifications

## Troubleshooting Commands

```bash
# Clear all caches
npx expo start --clear

# Reset Metro bundler
npx expo start --reset-cache

# Check Expo doctor for issues
npx expo doctor

# Update Expo SDK
npx expo install --fix

# Check for outdated packages
npm outdated
```

## Performance Tips

1. **Enable Hermes**: For better performance on Android
2. **Optimize Images**: Use appropriate image sizes
3. **Lazy Loading**: Implement for large lists
4. **Bundle Analysis**: Use `npx expo export` to analyze bundle size

## Ready for Production

Once testing is complete:
1. Build for app stores with `eas build`
2. Set up proper Firebase security rules
3. Configure push notifications
4. Add analytics and crash reporting
5. Implement proper error boundaries
