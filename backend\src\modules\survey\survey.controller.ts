import { Request, Response } from 'express';
import { SurveyService } from './survey.service';

export class SurveyController {
  constructor(private readonly surveyService: SurveyService) {}

  async getSurveys(_req: Request, res: Response) {
    try {
      const surveys = await this.surveyService.getSurveys();
      return res.json(surveys);
    } catch (error) {
      return res.status(500).json({ message: 'Error fetching surveys', error });
    }
  }

  async createSurvey(req: Request, res: Response) {
    try {
      const survey = await this.surveyService.createSurvey(req.body);
      res.status(201).json(survey);
    } catch (error) {
      res.status(500).json({ message: 'Error creating survey', error });
    }
  }

  async getSurvey(req: Request, res: Response) {
    try {
      const id = req.params['id'];
      if (!id) {
        return res.status(400).json({ message: 'Survey ID is required' });
      }
      const survey = await this.surveyService.getSurvey(id as string);
      if (!survey) {
        return res.status(404).json({ message: 'Survey not found' });
      }
      return res.json(survey);
    } catch (error) {
      return res.status(500).json({ message: 'Error fetching survey', error });
    }
  }

  async updateSurvey(req: Request, res: Response) {
    try {
      const id = req.params['id'];
      if (!id) {
        return res.status(400).json({ message: 'Survey ID is required' });
      }
      const updatedSurvey = await this.surveyService.updateSurvey(id as string, req.body);
      if (!updatedSurvey) {
        return res.status(404).json({ message: 'Survey not found' });
      }
      return res.json(updatedSurvey);
    } catch (error) {
      return res.status(500).json({ message: 'Error updating survey', error });
    }
  }

  async deleteSurvey(req: Request, res: Response) {
    try {
      const id = req.params['id'];
      if (!id) {
        return res.status(400).json({ message: 'Survey ID is required' });
      }
      await this.surveyService.deleteSurvey(id as string);
      return res.status(204).send();
    } catch (error) {
      return res.status(500).json({ message: 'Error deleting survey', error });
    }
  }
} 