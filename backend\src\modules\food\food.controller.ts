import { Request, Response } from 'express';
import { FoodService } from './food.service';
import { getModuleLogger } from '../../utils/logger';
import { FoodItem } from './food.types';

const logger = getModuleLogger('Food');

export class FoodController {
  private foodService: FoodService;

  constructor() {
    this.foodService = new FoodService();
  }

  private transformFoodItemData(data: any, userId: string): Omit<FoodItem, 'id'> {
    return {
      name: data.name,
      description: data.description,
      price: data.price,
      community_id: data.community_id || 'default',
      seller_name: data.seller_name || 'Anonymous',
      seller_id: userId,
      contact_info: data.contact_info || '',
      pickup_details: data.pickupDetails || null,
      inline_photo_id: data.inlinePhotoId || null,
      data_ai_hint: data.dataAiHint || null,
      created_at: new Date()
    };
  }

  private toCamelCase(foodItem: FoodItem): any {
    return {
      id: foodItem.id,
      name: foodItem.name,
      description: foodItem.description,
      price: foodItem.price,
      sellerName: foodItem.seller_name,
      sellerId: foodItem.seller_id,
      contactInfo: foodItem.contact_info,
      pickupDetails: foodItem.pickup_details,
      inlinePhotoId: foodItem.inline_photo_id,
      dataAiHint: foodItem.data_ai_hint,
      createdAt: foodItem.created_at,
      communityId: foodItem.community_id
    };
  }

  async getFoodItems(_req: Request, res: Response): Promise<Response> {
    try {
      logger.debug('GET /food/items - Request received');
      const foodItems = await this.foodService.getFoodItems();
      logger.debug('Response body:', { count: foodItems.length });
      return res.json(foodItems.map(this.toCamelCase));
    } catch (error) {
      logger.error('Error fetching food items:', { error });
      return res.status(500).json({ message: 'Error fetching food items', error });
    }
  }

  async createFoodItem(req: Request, res: Response): Promise<Response> {
    try {
      logger.debug('POST /food/items - Request received:', { body: req.body });
      if (!req.user?.id) {
        return res.status(401).json({ message: 'User not authenticated' });
      }
      const foodItemData = this.transformFoodItemData(req.body, req.user.id);
      const foodItem = await this.foodService.createFoodItem(foodItemData);
      logger.debug('Response body:', foodItem);
      return res.status(201).json(this.toCamelCase(foodItem));
    } catch (error) {
      logger.error('Error creating food item:', { error });
      return res.status(500).json({ message: 'Error creating food item', error });
    }
  }

  async getFoodItem(req: Request, res: Response): Promise<Response> {
    try {
      const { id } = req.params;
      if (!id) {
        return res.status(400).json({ message: 'Food item ID is required' });
      }
      logger.debug('GET /food/items/:id - Request received:', { id });
      const foodItem = await this.foodService.getFoodItem(id);
      if (!foodItem) {
        logger.debug('Food item not found:', { id });
        return res.status(404).json({ message: 'Food item not found' });
      }
      logger.debug('Response body:', foodItem);
      return res.json(this.toCamelCase(foodItem));
    } catch (error) {
      logger.error('Error fetching food item:', { error, id: req.params['id'] });
      return res.status(500).json({ message: 'Error fetching food item', error });
    }
  }

  async updateFoodItem(req: Request, res: Response): Promise<Response> {
    try {
      const { id } = req.params;
      if (!id) {
        return res.status(400).json({ error: 'Food item ID is required' });
      }

      // Map camelCase fields to snake_case for update
      const updateData = { ...req.body };
      if (updateData.pickupDetails !== undefined) {
        updateData.pickup_details = updateData.pickupDetails;
        delete updateData.pickupDetails;
      }
      if (updateData.inlinePhotoId !== undefined) {
        updateData.inline_photo_id = updateData.inlinePhotoId;
        delete updateData.inlinePhotoId;
      }
      if (updateData.dataAiHint !== undefined) {
        updateData.data_ai_hint = updateData.dataAiHint;
        delete updateData.dataAiHint;
      }

      const foodItem = await this.foodService.updateFoodItem(id, updateData);
      if (!foodItem) {
        return res.status(404).json({ message: 'Food item not found' });
      }

      return res.json(this.toCamelCase(foodItem));
    } catch (error) {
      console.error('Error updating food item:', error);
      return res.status(500).json({ error: 'Failed to update food item' });
    }
  }

  async deleteFoodItem(req: Request, res: Response): Promise<Response> {
    try {
      const { id } = req.params;
      if (!id) {
        return res.status(400).json({ error: 'Food item ID is required' });
      }

      const deleted = await this.foodService.deleteFoodItem(id);
      if (!deleted) {
        return res.status(404).json({ error: 'Food item not found' });
      }

      return res.status(204).send();
    } catch (error) {
      console.error('Error deleting food item:', error);
      return res.status(500).json({ error: 'Failed to delete food item' });
    }
  }
} 