import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

// Default to Hebrew for now
const deviceLanguage = 'he';

// Don't use AsyncStorage plugin on web - it might cause issues
const isWeb = typeof window !== 'undefined';

import he from './locales/he.json';
import en from './locales/en.json';

const resources = {
  he: {
    translation: he,
  },
  en: {
    translation: en,
  },
};

// Initialize i18n differently for web vs mobile
const i18nConfig = {
  compatibilityJSON: 'v3',
  resources,
  lng: deviceLanguage,
  fallbackLng: 'he',
  debug: false, // Disable debug for now
  interpolation: {
    escapeValue: false,
  },
  react: {
    useSuspense: false,
  },
};

if (isWeb) {
  // Web version - no AsyncStorage
  i18n.use(initReactI18next).init(i18nConfig);
} else {
  // Mobile version - with AsyncStorage
  try {
    const AsyncStoragePlugin = require('i18next-react-native-async-storage');
    i18n.use(AsyncStoragePlugin()).use(initReactI18next).init(i18nConfig);
  } catch (error) {
    console.log('AsyncStorage not available, using basic i18n');
    i18n.use(initReactI18next).init(i18nConfig);
  }
}

export default i18n;
