import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  Platform,
  TextInput,
} from 'react-native';
import DateTimePicker from '@react-native-community/datetimepicker';

// Try to import CalendarPicker, fallback to null if not available
let CalendarPicker: any = null;
try {
  const CalendarPickerModule = require('react-native-calendar-picker');
  CalendarPicker = CalendarPickerModule.default || CalendarPickerModule;
  console.log('CalendarPicker loaded successfully');
} catch (error) {
  console.log('CalendarPicker not available, using fallback:', error);
}

interface CrossPlatformCalendarProps {
  visible: boolean;
  onClose: () => void;
  onDateSelect: (date: Date) => void;
  selectedDate?: Date;
  minimumDate?: Date;
  title?: string;
}

export const CrossPlatformCalendar: React.FC<CrossPlatformCalendarProps> = ({
  visible,
  onClose,
  onDateSelect,
  selectedDate,
  minimumDate,
  title = 'בחר תאריך',
}) => {
  const [tempSelectedDate, setTempSelectedDate] = useState<Date | null>(
    selectedDate || null
  );

  const handleDateChange = (date: any) => {
    if (date) {
      const selectedDate = date instanceof Date ? date : new Date(date);
      setTempSelectedDate(selectedDate);
    }
  };

  const handleConfirm = () => {
    if (tempSelectedDate) {
      onDateSelect(tempSelectedDate);
    }
    onClose();
  };

  const handleCancel = () => {
    setTempSelectedDate(selectedDate || null);
    onClose();
  };

  // For web and cross-platform compatibility, use CalendarPicker or fallback
  const renderCalendarPicker = () => {
    console.log('Rendering calendar picker, CalendarPicker available:', !!CalendarPicker);
    if (CalendarPicker) {
      console.log('Using CalendarPicker component');
      return (
        <View style={styles.calendarContainer}>
          <CalendarPicker
            onDateChange={handleDateChange}
            selectedDayColor="#667eea"
            selectedDayTextColor="#FFFFFF"
            todayBackgroundColor="#f0f8ff"
            todayTextStyle={{ color: '#667eea' }}
            textStyle={{
              fontFamily: Platform.OS === 'ios' ? 'Helvetica' : 'Roboto',
              color: '#2c3e50',
            }}
            minDate={minimumDate}
            selectedStartDate={selectedDate}
            width={300}
            height={350}
            scaleFactor={375}
            enableDateChange={true}
            // RTL support
            enableSwipeMonths={true}
            // Custom styling for Hebrew
            monthTitleStyle={{
              color: '#2c3e50',
              fontWeight: 'bold',
              fontSize: 16,
            }}
            yearTitleStyle={{
              color: '#2c3e50',
              fontWeight: 'bold',
              fontSize: 16,
            }}
            dayLabelsWrapper={{
              borderTopWidth: 0,
              borderBottomWidth: 0,
            }}
            customDatesStyles={[]}
            allowRangeSelection={false}
            allowBackwardRangeSelect={false}
            previousTitle="◀"
            nextTitle="▶"
            previousTitleStyle={{
              color: '#667eea',
              fontSize: 18,
            }}
            nextTitleStyle={{
              color: '#667eea',
              fontSize: 18,
            }}
          />
        </View>
      );
    }

    // Fallback for when CalendarPicker is not available (e.g., web)
    console.log('Using fallback text input for date selection');
    return (
      <View style={styles.fallbackContainer}>
        <Text style={styles.fallbackLabel}>בחר תאריך:</Text>
        <TextInput
          style={styles.fallbackInput}
          placeholder="YYYY-MM-DD (לדוגמה: 2024-12-25)"
          value={tempSelectedDate ? tempSelectedDate.toISOString().split('T')[0] : ''}
          onChangeText={(text) => {
            if (text) {
              const date = new Date(text);
              if (!isNaN(date.getTime())) {
                handleDateChange(date);
              }
            }
          }}
        />
      </View>
    );
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <Text style={styles.modalTitle}>{title}</Text>
          
          {renderCalendarPicker()}
          
          <View style={styles.modalButtons}>
            <TouchableOpacity
              style={styles.modalButton}
              onPress={handleConfirm}
            >
              <Text style={styles.modalButtonText}>אישור</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.modalButton, styles.modalCancelButton]}
              onPress={handleCancel}
            >
              <Text style={styles.modalCancelButtonText}>ביטול</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

// Time picker component for cross-platform time selection
interface CrossPlatformTimePickerProps {
  visible: boolean;
  onClose: () => void;
  onTimeSelect: (time: Date) => void;
  selectedTime?: Date;
  title?: string;
}

export const CrossPlatformTimePicker: React.FC<CrossPlatformTimePickerProps> = ({
  visible,
  onClose,
  onTimeSelect,
  selectedTime,
  title = 'בחר שעה',
}) => {
  const [tempSelectedTime, setTempSelectedTime] = useState<Date>(
    selectedTime || new Date()
  );

  const handleTimeChange = (_event: any, time?: Date) => {
    if (Platform.OS === 'android') {
      // Android automatically closes the picker
      if (time) {
        onTimeSelect(time);
      }
      onClose();
    } else if (time) {
      setTempSelectedTime(time);
    }
  };

  const handleConfirm = () => {
    onTimeSelect(tempSelectedTime);
    onClose();
  };

  const handleCancel = () => {
    setTempSelectedTime(selectedTime || new Date());
    onClose();
  };

  if (Platform.OS === 'android') {
    // Android uses native picker directly
    return visible ? (
      <DateTimePicker
        value={tempSelectedTime}
        mode="time"
        display="default"
        onChange={handleTimeChange}
      />
    ) : null;
  }

  // iOS and Web use modal with picker
  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <Text style={styles.modalTitle}>{title}</Text>
          
          <DateTimePicker
            value={tempSelectedTime}
            mode="time"
            display={Platform.OS === 'ios' ? 'spinner' : 'default'}
            onChange={handleTimeChange}
            style={styles.timePicker}
          />
          
          <View style={styles.modalButtons}>
            <TouchableOpacity
              style={styles.modalButton}
              onPress={handleConfirm}
            >
              <Text style={styles.modalButtonText}>אישור</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.modalButton, styles.modalCancelButton]}
              onPress={handleCancel}
            >
              <Text style={styles.modalCancelButtonText}>ביטול</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 20,
    width: '90%',
    maxWidth: 350,
    alignItems: 'center',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#2c3e50',
  },
  calendarContainer: {
    marginBottom: 20,
  },
  fallbackContainer: {
    marginBottom: 20,
    alignItems: 'center',
  },
  fallbackLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 10,
    textAlign: 'center',
  },
  fallbackInput: {
    borderWidth: 2,
    borderColor: '#ecf0f1',
    borderRadius: 10,
    padding: 12,
    fontSize: 14,
    textAlign: 'center',
    backgroundColor: 'white',
    width: 250,
  },
  timePicker: {
    width: '100%',
    marginBottom: 20,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
  },
  modalButton: {
    backgroundColor: '#667eea',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    minWidth: 80,
  },
  modalButtonText: {
    color: 'white',
    textAlign: 'center',
    fontWeight: '600',
    fontSize: 16,
  },
  modalCancelButton: {
    backgroundColor: '#ecf0f1',
  },
  modalCancelButtonText: {
    color: '#7f8c8d',
    textAlign: 'center',
    fontWeight: '600',
    fontSize: 16,
  },
});
