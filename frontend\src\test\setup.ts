import '@testing-library/jest-native/extend-expect';
import { configure } from '@testing-library/react-native';
import type { View } from 'react-native';

// Configure testing library
configure({
  // @ts-ignore - testIDAttribute is a valid property but types are not up to date
  testIDAttribute: 'testID',
});

// Mock react-native-paper theme
jest.mock('react-native-paper', () => {
  const React = require('react');
  const { View } = require('react-native');
  const theme = {
    colors: {
      primary: '#6200ee',
      background: '#ffffff',
      surface: '#ffffff',
      error: '#b00020',
      onPrimary: '#ffffff',
      onBackground: '#000000',
      onSurface: '#000000',
      onError: '#ffffff',
    },
  };

  return {
    ...jest.requireActual('react-native-paper'),
    useTheme: () => theme,
    // @ts-ignore - Provider is a valid component but types are not up to date
    Provider: ({ children }: { children: React.ReactNode }) => React.createElement(View, null, children),
  };
});

// Mock react-native-reanimated
jest.mock('react-native-reanimated', () => {
  const Reanimated = require('react-native-reanimated/mock');
  Reanimated.default.call = () => {};
  return Reanimated;
});

// Mock react-native-gesture-handler
jest.mock('react-native-gesture-handler', () => {
  const View = require('react-native').View;
  return {
    Swipeable: View,
    DrawerLayout: View,
    State: {},
    ScrollView: View,
    Slider: View,
    Switch: View,
    TextInput: View,
    ToolbarAndroid: View,
    ViewPagerAndroid: View,
    DrawerLayoutAndroid: View,
    WebView: View,
    NativeViewGestureHandler: View,
    TapGestureHandler: View,
    FlingGestureHandler: View,
    ForceTouchGestureHandler: View,
    LongPressGestureHandler: View,
    PanGestureHandler: View,
    PinchGestureHandler: View,
    RotationGestureHandler: View,
    /* Buttons */
    RawButton: View,
    BaseButton: View,
    RectButton: View,
    BorderlessButton: View,
    /* Other */
    FlatList: View,
    gestureHandlerRootHOC: jest.fn(),
    Directions: {},
  };
}); 