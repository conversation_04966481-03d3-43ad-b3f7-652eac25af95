import { Router } from 'express';
import { DealController } from './deal.controller';
import { DealService } from './deal.service';
import { authenticateToken } from '../../middleware/auth';

const router = Router();
const dealService = new DealService();
const dealController = new DealController(dealService);

// Public routes (no authentication required)
router.get('/', dealController.getDeals.bind(dealController)); // Get all deals
router.get('/:id', dealController.getDeal.bind(dealController)); // Get specific deal

// Protected routes (authentication required)
router.post('/', authenticateToken, dealController.createDeal.bind(dealController));
router.put('/:id', authenticateToken, dealController.updateDeal.bind(dealController));
router.delete('/:id', authenticateToken, dealController.deleteDeal.bind(dealController));

export default router; 