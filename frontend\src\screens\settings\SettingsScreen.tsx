import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ScrollView,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from 'react-native-paper';
import { getTextAlign } from '../../utils/rtl';
import Header from '../../components/Header';
import WebCompatibleScrollView from '../../components/WebCompatibleScrollView';
import { createWebCompatibleContainerStyle, createWebCompatibleScrollContentStyle } from '../../utils/webStyles';
import { useThemePreferences } from '../../contexts/ThemeContext';
import { useAuth } from '../../contexts/AuthContext';
import { ThemeSelector, ThemeToggle, ThemePreview } from '../../components/ThemeSelector';
import { useThemeStyles } from '../../hooks/useThemeStyles';
import { UIPreferences } from '../../types';

export default function SettingsScreen() {
  const { t } = useTranslation();
  const theme = useTheme();
  const { user } = useAuth();
  const { preferences, updatePreferences, resetToDefaults, isLoading } = useThemePreferences();
  const { textStyles, spacing } = useThemeStyles();
  const [saving, setSaving] = useState(false);

  console.log('🎨 SettingsScreen: Current preferences:', preferences);
  console.log('🎨 SettingsScreen: Current theme colors:', theme.colors.primary, theme.colors.secondary);

  const handleThemeChange = async (newTheme: UIPreferences['theme']) => {
    try {
      console.log('🎨 SettingsScreen: Changing theme to', newTheme);
      setSaving(true);
      await updatePreferences({ theme: newTheme });
      console.log('🎨 SettingsScreen: Theme changed successfully');
    } catch (error) {
      console.error('🎨 SettingsScreen: Error changing theme:', error);
      Alert.alert('שגיאה', 'לא ניתן לשמור את ההגדרות');
    } finally {
      setSaving(false);
    }
  };

  const handleColorSchemeChange = async (newColorScheme: UIPreferences['colorScheme']) => {
    try {
      console.log('🎨 SettingsScreen: Changing color scheme to', newColorScheme);
      setSaving(true);
      await updatePreferences({ colorScheme: newColorScheme });
      console.log('🎨 SettingsScreen: Color scheme changed successfully');
    } catch (error) {
      console.error('🎨 SettingsScreen: Error changing color scheme:', error);
      Alert.alert('שגיאה', 'לא ניתן לשמור את ההגדרות');
    } finally {
      setSaving(false);
    }
  };

  const handleFontSizeChange = async (newFontSize: UIPreferences['fontSize']) => {
    try {
      setSaving(true);
      await updatePreferences({ fontSize: newFontSize });
    } catch (error) {
      Alert.alert('שגיאה', 'לא ניתן לשמור את ההגדרות');
    } finally {
      setSaving(false);
    }
  };

  const handleSpacingChange = async (newSpacing: UIPreferences['spacing']) => {
    try {
      setSaving(true);
      await updatePreferences({ spacing: newSpacing });
    } catch (error) {
      Alert.alert('שגיאה', 'לא ניתן לשמור את ההגדרות');
    } finally {
      setSaving(false);
    }
  };

  const handleHighContrastToggle = async () => {
    try {
      setSaving(true);
      await updatePreferences({ highContrast: !preferences.highContrast });
    } catch (error) {
      Alert.alert('שגיאה', 'לא ניתן לשמור את ההגדרות');
    } finally {
      setSaving(false);
    }
  };

  const handleResetToDefaults = () => {
    Alert.alert(
      'איפוס הגדרות',
      'האם אתה בטוח שברצונך לאפס את כל הגדרות התצוגה לברירת המחדל?',
      [
        {
          text: 'ביטול',
          style: 'cancel',
        },
        {
          text: 'איפוס',
          style: 'destructive',
          onPress: async () => {
            try {
              setSaving(true);
              await resetToDefaults();
              Alert.alert('הצלחה', 'ההגדרות אופסו בהצלחה');
            } catch (error) {
              Alert.alert('שגיאה', 'לא ניתן לאפס את ההגדרות');
            } finally {
              setSaving(false);
            }
          },
        },
      ]
    );
  };



  if (isLoading) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <Header title="⚙️ הגדרות תצוגה" showBackButton={true} />
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, { color: theme.colors.onBackground }]}>
            טוען הגדרות...
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header title="⚙️ הגדרות תצוגה" showBackButton={true} />

      <WebCompatibleScrollView
        style={styles.content}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Debug Info */}
        <View style={[styles.debugContainer, { backgroundColor: theme.colors.surface, borderColor: theme.colors.outline, padding: spacing.md }]}>
          <Text style={[textStyles.h6, { marginBottom: spacing.sm }]}>🔍 Debug Info</Text>
          <Text style={[textStyles.body2, { marginBottom: spacing.xs }]}>
            Theme: {preferences.theme} | Colors: {preferences.colorScheme} | Font: {preferences.fontSize}
          </Text>
          <Text style={[textStyles.body2, { marginBottom: spacing.xs }]}>
            Primary Color: {theme.colors.primary}
          </Text>
          <Text style={[textStyles.body2, { marginBottom: spacing.xs }]}>
            Saving: {saving ? 'Yes' : 'No'} | User: {user?.id ? 'Logged in' : 'Not logged in'}
          </Text>

          {/* Font Size Demo */}
          <View style={{ marginTop: spacing.sm, padding: spacing.sm, backgroundColor: theme.colors.primaryContainer, borderRadius: 8 }}>
            <Text style={[textStyles.caption, { marginBottom: spacing.xs }]}>Font Size Demo:</Text>
            <Text style={textStyles.h6}>H6 Header Text</Text>
            <Text style={textStyles.body1}>Body1 Regular Text</Text>
            <Text style={textStyles.body2}>Body2 Small Text</Text>
            <Text style={textStyles.caption}>Caption Tiny Text</Text>
          </View>

          <TouchableOpacity
            style={[styles.testButton, { backgroundColor: theme.colors.primary, marginTop: spacing.sm, padding: spacing.sm }]}
            onPress={() => {
              console.log('🎨 Test button: Forcing blue theme');
              handleColorSchemeChange('blue');
            }}
          >
            <Text style={[textStyles.button, { color: theme.colors.onPrimary, textAlign: 'center' }]}>
              Test: Force Blue Theme
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.testButton, { backgroundColor: theme.colors.secondary, marginTop: spacing.xs, padding: spacing.sm }]}
            onPress={() => {
              console.log('🎨 Test button: Forcing large font');
              handleFontSizeChange('large');
            }}
          >
            <Text style={[textStyles.button, { color: theme.colors.onSecondary, textAlign: 'center' }]}>
              Test: Force Large Font
            </Text>
          </TouchableOpacity>
        </View>

        {/* Theme Preview */}
        <View style={{ marginBottom: spacing.lg }}>
          <ThemePreview preferences={preferences} />
        </View>

        {/* Theme Section */}
        <View style={{ marginBottom: spacing.lg }}>
          <ThemeSelector
          title="🌙 ערכת נושא"
          options={[
            { key: 'light', label: 'בהיר', value: 'light' },
            { key: 'dark', label: 'כהה', value: 'dark' },
            { key: 'auto', label: 'אוטומטי', value: 'auto' },
          ]}
          selectedValue={preferences.theme}
          onSelect={handleThemeChange}
          disabled={saving}
        />
        </View>

        {/* Color Scheme Section */}
        <View style={{ marginBottom: spacing.lg }}>
          <ThemeSelector
          title="🎨 ערכת צבעים"
          options={[
            { key: 'default', label: 'ברירת מחדל', value: 'default' },
            { key: 'blue', label: 'כחול', value: 'blue' },
            { key: 'green', label: 'ירוק', value: 'green' },
            { key: 'purple', label: 'סגול', value: 'purple' },
            { key: 'orange', label: 'כתום', value: 'orange' },
          ]}
          selectedValue={preferences.colorScheme}
          onSelect={handleColorSchemeChange}
          disabled={saving}
        />
        </View>

        {/* Font Size Section */}
        <View style={{ marginBottom: spacing.lg }}>
          <ThemeSelector
          title="📝 גודל גופן"
          options={[
            { key: 'small', label: 'קטן', value: 'small' },
            { key: 'medium', label: 'בינוני', value: 'medium' },
            { key: 'large', label: 'גדול', value: 'large' },
            { key: 'extraLarge', label: 'גדול מאוד', value: 'extraLarge' },
          ]}
          selectedValue={preferences.fontSize}
          onSelect={handleFontSizeChange}
          disabled={saving}
        />
        </View>

        {/* Spacing Section */}
        <View style={{ marginBottom: spacing.lg }}>
          <ThemeSelector
          title="📏 מרווחים"
          options={[
            { key: 'compact', label: 'צפוף', value: 'compact' },
            { key: 'medium', label: 'בינוני', value: 'medium' },
            { key: 'comfortable', label: 'נוח', value: 'comfortable' },
          ]}
          selectedValue={preferences.spacing}
          onSelect={handleSpacingChange}
          disabled={saving}
        />
        </View>

        {/* High Contrast Section */}
        <View style={{ marginBottom: spacing.lg }}>
          <ThemeToggle
          title="🔍 ניגודיות גבוהה"
          value={preferences.highContrast}
          onToggle={handleHighContrastToggle}
          disabled={saving}
        />
        </View>

        {/* Manual Save Button for Testing */}
        <View style={{ marginBottom: spacing.lg }}>
          <TouchableOpacity
            style={[
              styles.resetButton,
              { borderColor: theme.colors.primary, backgroundColor: theme.colors.primary, padding: spacing.md },
            ]}
            onPress={async () => {
              try {
                setSaving(true);
                console.log('🎨 Manual save: Current preferences:', preferences);
                await updatePreferences(preferences); // Force save current preferences
                Alert.alert('הצלחה', 'ההגדרות נשמרו בהצלחה');
              } catch (error) {
                console.error('🎨 Manual save error:', error);
                Alert.alert('שגיאה', 'לא ניתן לשמור את ההגדרות');
              } finally {
                setSaving(false);
              }
            }}
            disabled={saving}
          >
            <Text style={[textStyles.button, { color: theme.colors.onPrimary, textAlign: 'center' }]}>
              💾 שמור הגדרות ידנית
            </Text>
          </TouchableOpacity>
        </View>

        {/* Reset Button */}
        <View style={{ marginBottom: spacing.xl }}>
          <TouchableOpacity
            style={[
              styles.resetButton,
              { borderColor: theme.colors.error, padding: spacing.md },
            ]}
            onPress={handleResetToDefaults}
            disabled={saving}
          >
            <Text style={[textStyles.button, { color: theme.colors.error, textAlign: 'center' }]}>
              🔄 איפוס לברירת מחדל
            </Text>
          </TouchableOpacity>
        </View>

        {saving && (
          <View style={styles.savingContainer}>
            <Text style={[styles.savingText, { color: theme.colors.onBackground }]}>
              שומר הגדרות...
            </Text>
          </View>
        )}
      </WebCompatibleScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: createWebCompatibleContainerStyle(),
  content: {
    flex: 1,
    padding: 20,
  },
  scrollContent: createWebCompatibleScrollContentStyle(),
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
  },

  resetButton: {
    padding: 16,
    borderRadius: 8,
    borderWidth: 2,
    alignItems: 'center',
  },
  resetButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  savingContainer: {
    alignItems: 'center',
    marginTop: 20,
  },
  savingText: {
    fontSize: 14,
    fontStyle: 'italic',
  },
  debugContainer: {
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 20,
  },
  debugTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  debugText: {
    fontSize: 12,
    marginBottom: 4,
  },
  testButton: {
    padding: 8,
    borderRadius: 4,
    marginTop: 8,
  },
  testButtonText: {
    fontSize: 12,
    fontWeight: 'bold',
    textAlign: 'center',
  },
});
