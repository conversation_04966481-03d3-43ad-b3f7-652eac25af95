<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Village Community App - Complete Mockup Set</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        
        .export-note {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .mockups-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .phone-mockup {
            background: #1a1a1a;
            border-radius: 25px;
            padding: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            max-width: 350px;
            margin: 0 auto;
            position: relative;
        }
        
        .screen-label {
            position: absolute;
            top: -10px;
            left: 50%;
            transform: translateX(-50%);
            background: #667eea;
            color: white;
            padding: 5px 15px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            z-index: 10;
        }
        
        .screen {
            background: white;
            border-radius: 15px;
            height: 600px;
            overflow: hidden;
            position: relative;
        }
        
        .status-bar {
            background: #000;
            color: white;
            padding: 8px 15px;
            font-size: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .app-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .header-title {
            font-weight: bold;
            font-size: 18px;
        }
        
        .header-action {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 14px;
        }
        
        .content {
            padding: 20px;
            height: calc(100% - 120px);
            overflow-y: auto;
        }
        
        /* Common Card Styles */
        .feature-card, .event-card, .job-card, .chat-card, .market-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            transition: transform 0.2s;
            cursor: pointer;
        }
        
        .feature-card:hover, .event-card:hover, .job-card:hover, .chat-card:hover, .market-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }
        
        .feature-icon, .card-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            margin-right: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            flex-shrink: 0;
        }
        
        /* Icon Colors */
        .events-icon { background: #ff6b6b; }
        .jobs-icon { background: #4ecdc4; }
        .chat-icon { background: #45b7d1; }
        .market-icon { background: #f9ca24; }
        .news-icon { background: #6c5ce7; }
        .profile-icon { background: #a55eea; }
        
        .feature-text, .card-text {
            flex: 1;
        }
        
        .feature-text h3, .card-text h3 {
            font-size: 16px;
            margin-bottom: 5px;
            color: #2c3e50;
        }
        
        .feature-text p, .card-text p {
            font-size: 12px;
            color: #7f8c8d;
            line-height: 1.4;
        }
        
        .card-meta {
            font-size: 11px;
            color: #bdc3c7;
            margin-left: 10px;
            text-align: right;
        }
        
        /* Bottom Navigation */
        .bottom-nav {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid #ecf0f1;
            display: flex;
            padding: 10px 0;
        }
        
        .nav-item {
            flex: 1;
            text-align: center;
            padding: 8px;
            font-size: 24px;
            cursor: pointer;
            transition: transform 0.2s;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
        }
        
        .nav-item:hover {
            transform: scale(1.1);
        }
        
        .nav-item.active {
            color: #667eea;
        }
        
        .nav-label {
            font-size: 10px;
            font-weight: 500;
        }
        
        /* Search Bar */
        .search-bar {
            background: #f8f9fa;
            border: none;
            border-radius: 25px;
            padding: 12px 20px;
            width: 100%;
            margin-bottom: 20px;
            font-size: 14px;
        }
        
        /* Chat specific */
        .chat-list {
            height: calc(100% - 60px);
        }
        
        .chat-item {
            display: flex;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #f1f3f4;
        }
        
        .avatar {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            background: linear-gradient(45deg, #667eea, #764ba2);
            margin-right: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 16px;
        }
        
        .chat-content {
            flex: 1;
        }
        
        .chat-name {
            font-weight: 600;
            font-size: 14px;
            margin-bottom: 4px;
            color: #2c3e50;
        }
        
        .chat-preview {
            font-size: 12px;
            color: #7f8c8d;
        }
        
        .chat-time {
            font-size: 11px;
            color: #bdc3c7;
        }
        
        .unread-badge {
            background: #e74c3c;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: bold;
            margin-left: 8px;
        }
        
        /* Event specific */
        .event-date {
            background: #667eea;
            color: white;
            border-radius: 8px;
            padding: 8px;
            text-align: center;
            margin-right: 15px;
            min-width: 50px;
        }
        
        .event-day {
            font-size: 18px;
            font-weight: bold;
            line-height: 1;
        }
        
        .event-month {
            font-size: 10px;
            text-transform: uppercase;
        }
        
        /* Market specific */
        .price-tag {
            background: #27ae60;
            color: white;
            border-radius: 6px;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: bold;
            margin-left: auto;
        }
        
        /* Add/Create buttons */
        .fab {
            position: absolute;
            bottom: 80px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .fab:hover {
            transform: scale(1.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏘️ Village Community App - Complete Mockup Set</h1>
        
        <div class="export-note">
            <h3>📱 Ready-to-Export Mockups</h3>
            <p>Right-click any mockup and select "Save Image As" or use browser screenshot tools to export individual screens.</p>
            <p>Each screen is designed at mobile resolution (350px width) and can be scaled for your needs.</p>
        </div>
        
        <div class="mockups-grid">
            <!-- Home Screen -->
            <div class="phone-mockup">
                <div class="screen-label">HOME</div>
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>🔋 100%</span>
                    </div>
                    <div class="app-header">
                        <div class="header-title">🏘️ Village Connect</div>
                        <button class="header-action">👤</button>
                    </div>
                    <div class="content">
                        <input type="text" class="search-bar" placeholder="🔍 Search community...">
                        
                        <div class="feature-card">
                            <div class="feature-icon events-icon">📅</div>
                            <div class="feature-text">
                                <h3>Community Events</h3>
                                <p>3 upcoming events this week</p>
                            </div>
                        </div>
                        <div class="feature-card">
                            <div class="feature-icon jobs-icon">💼</div>
                            <div class="feature-text">
                                <h3>Local Jobs</h3>
                                <p>12 new job postings</p>
                            </div>
                        </div>
                        <div class="feature-card">
                            <div class="feature-icon chat-icon">💬</div>
                            <div class="feature-text">
                                <h3>Village Chat</h3>
                                <p>5 unread messages</p>
                            </div>
                        </div>
                        <div class="feature-card">
                            <div class="feature-icon market-icon">🛒</div>
                            <div class="feature-text">
                                <h3>Local Market</h3>
                                <p>Fresh produce available</p>
                            </div>
                        </div>
                        <div class="feature-card">
                            <div class="feature-icon news-icon">📰</div>
                            <div class="feature-text">
                                <h3>Village News</h3>
                                <p>2 new announcements</p>
                            </div>
                        </div>
                    </div>
                    <div class="bottom-nav">
                        <div class="nav-item active">
                            <div>🏠</div>
                            <div class="nav-label">Home</div>
                        </div>
                        <div class="nav-item">
                            <div>👥</div>
                            <div class="nav-label">People</div>
                        </div>
                        <div class="nav-item">
                            <div>💬</div>
                            <div class="nav-label">Chat</div>
                        </div>
                        <div class="nav-item">
                            <div>⚙️</div>
                            <div class="nav-label">More</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- People/Directory Screen -->
            <div class="phone-mockup">
                <div class="screen-label">PEOPLE</div>
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>🔋 100%</span>
                    </div>
                    <div class="app-header">
                        <div class="header-title">👥 Village Directory</div>
                        <button class="header-action">A-Z</button>
                    </div>
                    <div class="content">
                        <input type="text" class="search-bar" placeholder="🔍 Search residents...">
                        
                        <div class="chat-item">
                            <div class="avatar">M</div>
                            <div class="chat-content">
                                <div class="chat-name">Maria Rodriguez</div>
                                <div class="chat-preview">Gardener • Oak Street 15</div>
                            </div>
                            <div class="chat-time">Online</div>
                        </div>

                        <div class="chat-item">
                            <div class="avatar">J</div>
                            <div class="chat-content">
                                <div class="chat-name">John Smith</div>
                                <div class="chat-preview">Handyman • Pine Avenue 42</div>
                            </div>
                            <div class="chat-time">2h ago</div>
                        </div>

                        <div class="chat-item">
                            <div class="avatar">A</div>
                            <div class="chat-content">
                                <div class="chat-name">Anna Wilson</div>
                                <div class="chat-preview">Teacher • Maple Road 8</div>
                            </div>
                            <div class="chat-time">Online</div>
                        </div>

                        <div class="chat-item">
                            <div class="avatar">S</div>
                            <div class="chat-content">
                                <div class="chat-name">Sarah Johnson</div>
                                <div class="chat-preview">Nurse • Cedar Lane 23</div>
                            </div>
                            <div class="chat-time">1d ago</div>
                        </div>

                        <div class="chat-item">
                            <div class="avatar">D</div>
                            <div class="chat-content">
                                <div class="chat-name">David Chen</div>
                                <div class="chat-preview">Shop Owner • Main Street 67</div>
                            </div>
                            <div class="chat-time">Online</div>
                        </div>

                        <div class="chat-item">
                            <div class="avatar">L</div>
                            <div class="chat-content">
                                <div class="chat-name">Lisa Thompson</div>
                                <div class="chat-preview">Librarian • Birch Court 12</div>
                            </div>
                            <div class="chat-time">5h ago</div>
                        </div>

                        <div class="chat-item">
                            <div class="avatar">R</div>
                            <div class="chat-content">
                                <div class="chat-name">Robert Davis</div>
                                <div class="chat-preview">Retired • Elm Street 89</div>
                            </div>
                            <div class="chat-time">3d ago</div>
                        </div>
                    </div>
                    <div class="bottom-nav">
                        <div class="nav-item">
                            <div>🏠</div>
                            <div class="nav-label">Home</div>
                        </div>
                        <div class="nav-item active">
                            <div>👥</div>
                            <div class="nav-label">People</div>
                        </div>
                        <div class="nav-item">
                            <div>💬</div>
                            <div class="nav-label">Chat</div>
                        </div>
                        <div class="nav-item">
                            <div>⚙️</div>
                            <div class="nav-label">More</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Profile Screen -->
            <div class="phone-mockup">
                <div class="screen-label">PROFILE</div>
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>🔋 100%</span>
                    </div>
                    <div class="app-header">
                        <div class="header-title">👤 My Profile</div>
                        <button class="header-action">Edit</button>
                    </div>
                    <div class="content">
                        <div style="text-align: center; margin-bottom: 30px;">
                            <div style="width: 80px; height: 80px; border-radius: 50%; background: linear-gradient(45deg, #667eea, #764ba2); margin: 0 auto 15px; display: flex; align-items: center; justify-content: center; color: white; font-size: 32px; font-weight: bold;">U</div>
                            <h2 style="color: #2c3e50; margin-bottom: 5px;">Your Name</h2>
                            <p style="color: #7f8c8d; font-size: 14px;">Resident • Joined March 2024</p>
                        </div>

                        <div class="feature-card">
                            <div class="feature-icon profile-icon">📍</div>
                            <div class="feature-text">
                                <h3>Address</h3>
                                <p>123 Main Street, Village Center</p>
                            </div>
                        </div>

                        <div class="feature-card">
                            <div class="feature-icon profile-icon">📞</div>
                            <div class="feature-text">
                                <h3>Contact</h3>
                                <p>+1 (555) 123-4567</p>
                            </div>
                        </div>

                        <div class="feature-card">
                            <div class="feature-icon profile-icon">💼</div>
                            <div class="feature-text">
                                <h3>Skills & Services</h3>
                                <p>Gardening, Tutoring, Pet Sitting</p>
                            </div>
                        </div>

                        <div class="feature-card">
                            <div class="feature-icon profile-icon">⭐</div>
                            <div class="feature-text">
                                <h3>Community Rating</h3>
                                <p>4.8/5 stars (24 reviews)</p>
                            </div>
                        </div>

                        <div class="feature-card">
                            <div class="feature-icon profile-icon">🏆</div>
                            <div class="feature-text">
                                <h3>Village Contributions</h3>
                                <p>12 events organized, 8 jobs completed</p>
                            </div>
                        </div>
                    </div>
                    <div class="bottom-nav">
                        <div class="nav-item">
                            <div>🏠</div>
                            <div class="nav-label">Home</div>
                        </div>
                        <div class="nav-item">
                            <div>👥</div>
                            <div class="nav-label">People</div>
                        </div>
                        <div class="nav-item">
                            <div>💬</div>
                            <div class="nav-label">Chat</div>
                        </div>
                        <div class="nav-item active">
                            <div>⚙️</div>
                            <div class="nav-label">More</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Settings Screen -->
            <div class="phone-mockup">
                <div class="screen-label">SETTINGS</div>
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>🔋 100%</span>
                    </div>
                    <div class="app-header">
                        <div class="header-title">⚙️ Settings</div>
                        <button class="header-action">✓</button>
                    </div>
                    <div class="content">
                        <div class="feature-card">
                            <div class="feature-icon profile-icon">🔔</div>
                            <div class="feature-text">
                                <h3>Notifications</h3>
                                <p>Manage push notifications and alerts</p>
                            </div>
                        </div>

                        <div class="feature-card">
                            <div class="feature-icon profile-icon">🔒</div>
                            <div class="feature-text">
                                <h3>Privacy & Safety</h3>
                                <p>Control who can see your information</p>
                            </div>
                        </div>

                        <div class="feature-card">
                            <div class="feature-icon profile-icon">👨‍👩‍👧‍👦</div>
                            <div class="feature-text">
                                <h3>Family Mode</h3>
                                <p>Child-friendly interface and content</p>
                            </div>
                        </div>

                        <div class="feature-card">
                            <div class="feature-icon profile-icon">🌙</div>
                            <div class="feature-text">
                                <h3>Dark Mode</h3>
                                <p>Switch to dark theme</p>
                            </div>
                        </div>

                        <div class="feature-card">
                            <div class="feature-icon profile-icon">🌍</div>
                            <div class="feature-text">
                                <h3>Language</h3>
                                <p>Change app language</p>
                            </div>
                        </div>

                        <div class="feature-card">
                            <div class="feature-icon profile-icon">📱</div>
                            <div class="feature-text">
                                <h3>App Version</h3>
                                <p>Village Connect v1.2.3</p>
                            </div>
                        </div>

                        <div class="feature-card">
                            <div class="feature-icon profile-icon">❓</div>
                            <div class="feature-text">
                                <h3>Help & Support</h3>
                                <p>Get help and contact support</p>
                            </div>
                        </div>

                        <div class="feature-card">
                            <div class="feature-icon profile-icon">🚪</div>
                            <div class="feature-text">
                                <h3>Sign Out</h3>
                                <p>Log out of your account</p>
                            </div>
                        </div>
                    </div>
                    <div class="bottom-nav">
                        <div class="nav-item">
                            <div>🏠</div>
                            <div class="nav-label">Home</div>
                        </div>
                        <div class="nav-item">
                            <div>👥</div>
                            <div class="nav-label">People</div>
                        </div>
                        <div class="nav-item">
                            <div>💬</div>
                            <div class="nav-label">Chat</div>
                        </div>
                        <div class="nav-item active">
                            <div>⚙️</div>
                            <div class="nav-label">More</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- News/Announcements Screen -->
            <div class="phone-mockup">
                <div class="screen-label">NEWS</div>
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>🔋 100%</span>
                    </div>
                    <div class="app-header">
                        <div class="header-title">📰 Village News</div>
                        <button class="header-action">📌</button>
                    </div>
                    <div class="content">
                        <input type="text" class="search-bar" placeholder="🔍 Search news...">
                        
                        <div class="feature-card" style="border-left: 4px solid #e74c3c;">
                            <div class="feature-icon news-icon">⚠️</div>
                            <div class="feature-text">
                                <h3>Water Main Maintenance</h3>
                                <p>Scheduled water service interruption on June 20th from 9 AM to 3 PM. Please plan accordingly.</p>
                            </div>
                            <div class="card-meta">
                                <div>Important</div>
                                <div>2h ago</div>
                            </div>
                        </div>

                        <div class="feature-card">
                            <div class="feature-icon news-icon">♻️</div>
                            <div class="feature-text">
                                <h3>New Recycling Schedule</h3>
                                <p>Starting next week, recycling pickup will be on Wednesdays instead of Thursdays.</p>
                            </div>
                            <div class="card-meta">
                                <div>Updated</div>
                                <div>1d ago</div>
                            </div>
                        </div>

                        <div class="feature-card">
                            <div class="feature-icon news-icon">🚧</div>
                            <div class="feature-text">
                                <h3>Road Construction Update</h3>
                                <p>Main Street construction is ahead of schedule. Expected completion: June 25th.</p>
                            </div>
                            <div class="card-meta">
                                <div>Progress</div>
                                <div>2d ago</div>
                            </div>
                        </div>

                        <div class="feature-card">
                            <div class="feature-icon news-icon">🏆</div>
                            <div class="feature-text">
                                <h3>Village Wins Green Award</h3>
                                <p>Our community has been recognized for outstanding environmental initiatives!</p>
                            </div>
                            <div class="card-meta">
                                <div>Achievement</div>
                                <div>3d ago</div>
                            </div>
                        </div>

                        <div class="feature-card">
                            <div class="feature-icon news-icon">📅</div>
                            <div class="feature-text">
                                <h3>Summer Program Registration</h3>
                                <p>Registration for children's summer activities starts Monday. Limited spots available.</p>
                            </div>
                            <div class="card-meta">
                                <div>Reminder</div>
                                <div>1w ago</div>
                            </div>
                        </div>
                    </div>
                    <div class="bottom-nav">
                        <div class="nav-item">
                            <div>🏠</div>
                            <div class="nav-label">Home</div>
                        </div>
                        <div class="nav-item">
                            <div>👥</div>
                            <div class="nav-label">People</div>
                        </div>
                        <div class="nav-item">
                            <div>💬</div>
                            <div class="nav-label">Chat</div>
                        </div>
                        <div class="nav-item">
                            <div>⚙️</div>
                            <div class="nav-label">More</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div style="background: white; padding: 30px; border-radius: 15px; margin-top: 40px;">
            <h2 style="color: #2c3e50; margin-bottom: 20px; text-align: center;">📋 Export Instructions</h2>
            <div style="max-width: 800px; margin: 0 auto; line-height: 1.6;">
                <h3 style="color: #667eea; margin-bottom: 15px;">How to Export These Mockups:</h3>
                
                <div style="background: #f8f9ff; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                    <h4 style="color: #2c3e50; margin-bottom: 10px;">Method 1: Screenshot Tool</h4>
                    <p>• Use your browser's screenshot tool or extension</p>
                    <p>• Each mockup is sized at 350px width for mobile proportion</p>
                    <p>• Capture individual screens or the entire page</p>
                </div>

                <div style="background: #f8f9ff; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                    <h4 style="color: #2c3e50; margin-bottom: 10px;">Method 2: Developer Tools</h4>
                    <p>• Right-click → Inspect Element → Screenshots tab</p>
                    <p>• Select "Capture node screenshot" for individual mockups</p>
                    <p>• Perfect for high-resolution exports</p>
                </div>

                <div style="background: #f8f9ff; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                    <h4 style="color: #2c3e50; margin-bottom: 10px;">Method 3: Print to PDF</h4>
                    <p>• File → Print → Save as PDF</p>
                    <p>• Great for presenting all screens at once</p>
                    <p>• Can extract individual images from PDF later</p>
                </div>

                <h3 style="color: #667eea; margin: 25px 0 15px;">🎨 Customization Notes:</h3>
                <p>• All colors, fonts, and spacing can be easily modified in the CSS</p>
                <p>• Icons can be replaced with your preferred icon set</p>
                <p>• Text content represents typical village community scenarios</p>
                <p>• Responsive design adapts to different screen sizes</p>

                <h3 style="color: #667eea; margin: 25px 0 15px;">📱 Implementation Tips:</h3>
                <p>• These mockups work perfectly as React Native component references</p>
                <p>• Color scheme: Primary (#667eea), Secondary (#764ba2), Accent colors for categories</p>
                <p>• Typography: System fonts with clear hierarchy</p>
                <p>• Interactive elements include hover states and proper touch targets</p>
            </div>
        </div>
    </div>
</body>
</html>

            <!-- Events Screen -->
            <div class="phone-mockup">
                <div class="screen-label">EVENTS</div>
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>🔋 100%</span>
                    </div>
                    <div class="app-header">
                        <div class="header-title">📅 Community Events</div>
                        <button class="header-action">+ Add</button>
                    </div>
                    <div class="content">
                        <input type="text" class="search-bar" placeholder="🔍 Search events...">
                        
                        <div class="event-card">
                            <div class="event-date">
                                <div class="event-day">15</div>
                                <div class="event-month">JUN</div>
                            </div>
                            <div class="card-text">
                                <h3>Village Summer Fair</h3>
                                <p>Annual community fair with games, food, and local vendors. Perfect for families!</p>
                            </div>
                            <div class="card-meta">
                                <div>2:00 PM</div>
                                <div>Central Park</div>
                            </div>
                        </div>

                        <div class="event-card">
                            <div class="event-date">
                                <div class="event-day">18</div>
                                <div class="event-month">JUN</div>
                            </div>
                            <div class="card-text">
                                <h3>Community Garden Workshop</h3>
                                <p>Learn sustainable gardening techniques with our local experts.</p>
                            </div>
                            <div class="card-meta">
                                <div>10:00 AM</div>
                                <div>Garden Center</div>
                            </div>
                        </div>

                        <div class="event-card">
                            <div class="event-date">
                                <div class="event-day">22</div>
                                <div class="event-month">JUN</div>
                            </div>
                            <div class="card-text">
                                <h3>Youth Soccer Tournament</h3>
                                <p>Local kids soccer tournament. Come cheer for our young athletes!</p>
                            </div>
                            <div class="card-meta">
                                <div>9:00 AM</div>
                                <div>Sports Field</div>
                            </div>
                        </div>

                        <div class="event-card">
                            <div class="event-date">
                                <div class="event-day">25</div>
                                <div class="event-month">JUN</div>
                            </div>
                            <div class="card-text">
                                <h3>Movie Night Under Stars</h3>
                                <p>Family-friendly outdoor movie screening. Bring blankets and snacks.</p>
                            </div>
                            <div class="card-meta">
                                <div>8:00 PM</div>
                                <div>Town Square</div>
                            </div>
                        </div>
                    </div>
                    <div class="fab">+</div>
                    <div class="bottom-nav">
                        <div class="nav-item">
                            <div>🏠</div>
                            <div class="nav-label">Home</div>
                        </div>
                        <div class="nav-item">
                            <div>👥</div>
                            <div class="nav-label">People</div>
                        </div>
                        <div class="nav-item">
                            <div>💬</div>
                            <div class="nav-label">Chat</div>
                        </div>
                        <div class="nav-item">
                            <div>⚙️</div>
                            <div class="nav-label">More</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Jobs Screen -->
            <div class="phone-mockup">
                <div class="screen-label">JOBS</div>
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>🔋 100%</span>
                    </div>
                    <div class="app-header">
                        <div class="header-title">💼 Local Jobs</div>
                        <button class="header-action">Post</button>
                    </div>
                    <div class="content">
                        <input type="text" class="search-bar" placeholder="🔍 Search jobs...">
                        
                        <div class="job-card">
                            <div class="card-icon jobs-icon">👷</div>
                            <div class="card-text">
                                <h3>Part-time Gardener</h3>
                                <p>Mrs. Johnson needs help maintaining her garden. 2-3 hours/week, flexible schedule.</p>
                            </div>
                            <div class="card-meta">
                                <div>$15/hr</div>
                                <div>2h ago</div>
                            </div>
                        </div>

                        <div class="job-card">
                            <div class="card-icon jobs-icon">🧹</div>
                            <div class="card-text">
                                <h3>House Cleaning Service</h3>
                                <p>Looking for reliable person to clean house twice a month. References required.</p>
                            </div>
                            <div class="card-meta">
                                <div>$80/visit</div>
                                <div>5h ago</div>
                            </div>
                        </div>

                        <div class="job-card">
                            <div class="card-icon jobs-icon">🚗</div>
                            <div class="card-text">
                                <h3>Delivery Driver</h3>
                                <p>Local grocery store needs part-time delivery driver. Must have valid license.</p>
                            </div>
                            <div class="card-meta">
                                <div>$12/hr</div>
                                <div>1d ago</div>
                            </div>
                        </div>

                        <div class="job-card">
                            <div class="card-icon jobs-icon">👨‍🏫</div>
                            <div class="card-text">
                                <h3>Math Tutor</h3>
                                <p>High school student needs help with algebra. 2 sessions per week after school.</p>
                            </div>
                            <div class="card-meta">
                                <div>$20/hr</div>
                                <div>2d ago</div>
                            </div>
                        </div>

                        <div class="job-card">
                            <div class="card-icon jobs-icon">🐕</div>
                            <div class="card-text">
                                <h3>Dog Walker</h3>
                                <p>Friendly golden retriever needs daily walks while owner is at work. Mon-Fri.</p>
                            </div>
                            <div class="card-meta">
                                <div>$10/walk</div>
                                <div>3d ago</div>
                            </div>
                        </div>
                    </div>
                    <div class="fab">+</div>
                    <div class="bottom-nav">
                        <div class="nav-item">
                            <div>🏠</div>
                            <div class="nav-label">Home</div>
                        </div>
                        <div class="nav-item">
                            <div>👥</div>
                            <div class="nav-label">People</div>
                        </div>
                        <div class="nav-item">
                            <div>💬</div>
                            <div class="nav-label">Chat</div>
                        </div>
                        <div class="nav-item">
                            <div>⚙️</div>
                            <div class="nav-label">More</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Chat Screen -->
            <div class="phone-mockup">
                <div class="screen-label">CHAT</div>
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>🔋 100%</span>
                    </div>
                    <div class="app-header">
                        <div class="header-title">💬 Village Chat</div>
                        <button class="header-action">New</button>
                    </div>
                    <div class="content">
                        <input type="text" class="search-bar" placeholder="🔍 Search conversations...">
                        
                        <div class="chat-list">
                            <div class="chat-item">
                                <div class="avatar">🏘️</div>
                                <div class="chat-content">
                                    <div class="chat-name">Village Announcements</div>
                                    <div class="chat-preview">New recycling schedule posted</div>
                                </div>
                                <div>
                                    <div class="chat-time">9:15 AM</div>
                                    <div class="unread-badge">2</div>
                                </div>
                            </div>

                            <div class="chat-item">
                                <div class="avatar">M</div>
                                <div class="chat-content">
                                    <div class="chat-name">Maria Rodriguez</div>
                                    <div class="chat-preview">Thanks for helping with the garden!</div>
                                </div>
                                <div class="chat-time">8:30 AM</div>
                            </div>

                            <div class="chat-item">
                                <div class="avatar">👨‍👩‍👧‍👦</div>
                                <div class="chat-content">
                                    <div class="chat-name">Parents Group</div>
                                    <div class="chat-preview">Sarah: Soccer practice moved to 4 PM</div>
                                </div>
                                <div>
                                    <div class="chat-time">7:45 AM</div>
                                    <div class="unread-badge">5</div>
                                </div>
                            </div>

                            <div class="chat-item">
                                <div class="avatar">J</div>
                                <div class="chat-content">
                                    <div class="chat-name">John Smith</div>
                                    <div class="chat-preview">The handyman job is still available</div>
                                </div>
                                <div class="chat-time">Yesterday</div>
                            </div>

                            <div class="chat-item">
                                <div class="avatar">🛒</div>
                                <div class="chat-content">
                                    <div class="chat-name">Local Market Updates</div>
                                    <div class="chat-preview">Fresh strawberries arrived today!</div>
                                </div>
                                <div>
                                    <div class="chat-time">Yesterday</div>
                                    <div class="unread-badge">1</div>
                                </div>
                            </div>

                            <div class="chat-item">
                                <div class="avatar">A</div>
                                <div class="chat-content">
                                    <div class="chat-name">Anna Wilson</div>
                                    <div class="chat-preview">Found your lost cat near the park</div>
                                </div>
                                <div class="chat-time">Yesterday</div>
                            </div>

                            <div class="chat-item">
                                <div class="avatar">🎪</div>
                                <div class="chat-content">
                                    <div class="chat-name">Event Planning</div>
                                    <div class="chat-preview">Mike: We need more volunteers for...</div>
                                </div>
                                <div class="chat-time">2 days ago</div>
                            </div>
                        </div>
                    </div>
                    <div class="bottom-nav">
                        <div class="nav-item">
                            <div>🏠</div>
                            <div class="nav-label">Home</div>
                        </div>
                        <div class="nav-item">
                            <div>👥</div>
                            <div class="nav-label">People</div>
                        </div>
                        <div class="nav-item active">
                            <div>💬</div>
                            <div class="nav-label">Chat</div>
                        </div>
                        <div class="nav-item">
                            <div>⚙️</div>
                            <div class="nav-label">More</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Market Screen -->
            <div class="phone-mockup">
                <div class="screen-label">MARKET</div>
                <div class="screen">
                    <div class="status-bar">
                        <span>9:41</span>
                        <span>🔋 100%</span>
                    </div>
                    <div class="app-header">
                        <div class="header-title">🛒 Local Market</div>
                        <button class="header-action">Sell</button>
                    </div>
                    <div class="content">
                        <input type="text" class="search-bar" placeholder="🔍 Search products...">
                        
                        <div class="market-card">
                            <div class="card-icon market-icon">🍅</div>
                            <div class="card-text">
                                <h3>Fresh Tomatoes</h3>
                                <p>Organic tomatoes from Maria's garden. Perfect for cooking!</p>
                            </div>
                            <div class="price-tag">$3/lb</div>
                        </div>

                        <div class="market-card">
                            <div class="card-icon market-icon">🚲</div>
                            <div class="card-text">
                                <h3>Kids Bicycle</h3>
                                <p>Great condition, blue mountain bike. Suitable for ages 8-12.</p>
                            </div>
                            <div class="price-tag">$75</div>
                        </div>

                        <div class="market-card">
                            <div class="card-icon market-icon">📚</div>
                            <div class="card-text">
                                <h3>School Textbooks</h3>
                                <p>High school math and science books. Previous year edition.</p>
                            </div>
                            <div class="price-tag">$25/set</div>
                        </div>

                        <div class="market-card">
                            <div class="card-icon market-icon">🪑</div>
                            <div class="card-text">
                                <h3>Dining Table Set</h3>
                                <p>Wooden table with 4 chairs. Moving sale, must go this week.</p>
                            </div>
                            <div class="price-tag">$150</div>
                        </div>

                        <div class="market-card">
                            <div class="card-icon market-icon">🍯</div>
                            <div class="card-text">
                                <h3>Local Honey</h3>
                                <p>Pure honey from village beehives. Various sizes available.</p>
                            </div>
                            <div class="price-tag">$8/jar</div>
                        </div>

                        <div class="market-card">
                            <div class="card-icon market-icon">👕</div>
                            <div class="card-text">
                                <h3>Kids Clothing</h3>
                                <p>Gently used children's clothes, sizes 4-8. Clean and ready to wear.</p>
                            </div>
                            <div class="price-tag">$2/item</div>
                        </div>
                    </div>
                    <div class="fab">+</div>
                    <div class="bottom-nav">
                        <div class="nav-item">
                            <div>🏠</div>
                            <div class="nav-label">Home</div>
                        </div>
                        <div class="nav-item">
                            <div>👥</div>
                            <div class="nav-label">People</div>
                        </div>
                        <div class="nav-item">
                            <div>💬</div>
                            <div class="nav-label">Chat</div>
                        </div>
                        <div class="nav-item">
                            <div>⚙️</div>
                            <div class="nav-label">More</div>
                        </div>
                    </div>
                </div>
            </div>