# Backend API Documentation

## Temporary Changes Notice

The following user management endpoints have been temporarily opened to all authenticated users (removing admin-only restrictions):

- `GET /api/users` - Get all users
- `POST /api/users` - Create new user
- `POST /api/users/profile` - Create user profile
- `DELETE /api/users/:id` - Delete user

### Security Note
These endpoints are currently accessible to any authenticated user. This is a temporary measure and will be reverted in the future to implement proper role-based access control (RBAC). The following security measures will be implemented:

1. Admin-only access for user management endpoints
2. Role-based permissions for different user operations
3. Proper validation of user roles and permissions
4. Audit logging for sensitive operations

### TODO
- [ ] Re-implement admin-only checks for user management endpoints
- [ ] Add proper role-based access control
- [ ] Implement audit logging for sensitive operations
- [ ] Add comprehensive permission checks
- [ ] Update API documentation with proper access requirements

## API Endpoints

### User Management
- `GET /api/users` - Get all users (temporarily open to all authenticated users)
- `GET /api/users/:id` - Get user by ID
- `POST /api/users` - Create new user (temporarily open to all authenticated users)
- `POST /api/users/profile` - Create user profile (temporarily open to all authenticated users)
- `PUT /api/users/:id` - Update user
- `DELETE /api/users/:id` - Delete user (temporarily open to all authenticated users)
- `GET /api/users/count` - Get total user count
- `GET /api/users/type-role` - Get users by type or role

### UI Preferences
- `PUT /api/users/:id/preferences` - Update user UI preferences
- `GET /api/users/:id/preferences` - Get user UI preferences

## Chat Module Behavior

### Group Chat and Message Rules

- Users can create group chats and add other users as members.
- Each member can send messages to the group; all current members can read all messages.
- If a user is deleted, they are removed from all group chats, but the group chat persists as long as at least one member remains.
- If the last member of a group chat is deleted, the group chat is automatically deleted as well.

### Example Test Scenario
1. Add 5 users (with dummy authentication).
2. One user creates a group and adds the other 4 users.
3. Each user sends one message; all other users can read it.
4. Delete all users.
5. The group chat remains as long as there is at least 1 member; if the last member is deleted, the group is deleted.

### Chat Module Rules

#### 1. Chat Group Access Rules
- Only group members can:
  - View group details
  - View group messages
  - Send messages to the group
- Only group creator/owner can:
  - Update group details (name, etc.)
  - Delete the group
  - Add/remove members

#### 2. Message Access Rules
- Only message sender can:
  - Edit their own messages
  - Delete their own messages
- Only group members can:
  - View messages in the group
  - Send new messages to the group
- Non-members should:
  - Get 403 (Forbidden) when trying to access group messages
  - Get 403 when trying to send messages to the group

#### 3. User Authentication Rules
- All API endpoints require valid Supabase authentication token
- Invalid/missing tokens should return 401 (Unauthorized)
- Valid tokens should be verified against Supabase

#### 4. Error Response Rules
- 401 (Unauthorized): Invalid/missing authentication token
- 403 (Forbidden): Valid token but insufficient permissions
- 404 (Not Found): Resource doesn't exist
- 200 (OK): Successful GET/PUT operations
- 201 (Created): Successful POST operations
- 204 (No Content): Successful DELETE operations

#### 5. Data Validation Rules
- Group creation requires:
  - Name
  - Community ID
  - Creator ID
  - At least one member (creator)
- Message creation requires:
  - Group ID
  - Sender ID
  - Text content
  - Community ID

#### 6. Cleanup Rules
- When a group is deleted:
  - All messages in the group should be deleted
  - Group membership should be removed
- When a user is deleted:
  - Their messages should be preserved
  - They should be removed from group memberships 