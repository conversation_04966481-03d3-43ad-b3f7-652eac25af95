import { I18nManager } from 'react-native';
import i18n from '../i18n';

export const isRTL = () => {
  return i18n.language === 'he' || I18nManager.isRTL;
};

export const getTextAlign = (): 'left' | 'right' | 'center' => {
  return isRTL() ? 'right' : 'left';
};

export const getFlexDirection = (): 'row' | 'row-reverse' => {
  return isRTL() ? 'row-reverse' : 'row';
};

export const getWritingDirection = (): 'ltr' | 'rtl' => {
  return isRTL() ? 'rtl' : 'ltr';
};

export const forceRTL = () => {
  if (!I18nManager.isRTL) {
    I18nManager.forceRTL(true);
    // Note: This requires app restart to take effect
  }
};

export const forceLTR = () => {
  if (I18nManager.isRTL) {
    I18nManager.forceRTL(false);
    // Note: This requires app restart to take effect
  }
};
