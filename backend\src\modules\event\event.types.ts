export interface Event {
  id: string;
  community_id: string;
  title: string;
  description: string;
  date: Date;
  location: string;
  type: 'COMMUNITY' | 'TENANT'; // Match database enum
  icon?: string | null;
  image_url?: string | null;
  data_ai_hint?: string | null;
  created_at: Date | null;
  attendees?: number | null;
  created_by: string;
}

// Frontend-compatible interface for API responses
export interface EventResponse {
  id: string;
  title: string;
  description: string;
  date: Date;
  location: string;
  type: 'community' | 'tenant'; // Frontend uses lowercase
  icon?: string;
  imageUrl?: string;
  dataAiHint?: string;
  createdAt: Date;
  attendees?: number;
  createdBy: string;
  rsvp: any[]; // Empty array for now, will be populated when RSVP feature is implemented
  comments: any[]; // Empty array for now, will be populated when comments feature is implemented
  reactions: any[]; // Empty array for now, will be populated when reactions feature is implemented
}