import { SurveyStatus } from '../../generated/prisma';

export interface SurveyQuestion {
  id: string;
  text: string;
  type: 'text' | 'single-choice' | 'multiple-choice';
  options?: string[];
}

export interface Survey {
  id: string;
  title: string;
  description: string;
  questions: any; // Use any for JSON value
  status: SurveyStatus;
  createdBy: string;
  createdAt: Date | null;
  communityId: string;
  participantsCount: number | null;
  closingDate: Date | null;
  imageUrl: string | null;
  dataAiHint: string | null;
} 