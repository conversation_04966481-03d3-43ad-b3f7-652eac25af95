{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "baseUrl": ".", "paths": {"@components/*": ["src/components/*"], "@screens/*": ["src/screens/*"], "@navigation/*": ["src/navigation/*"], "@theme/*": ["src/theme/*"], "@utils/*": ["src/utils/*"], "@hooks/*": ["src/hooks/*"], "@services/*": ["src/services/*"], "@contexts/*": ["src/contexts/*"], "@i18n/*": ["src/i18n/*"], "@assets/*": ["src/assets/*"], "@config/*": ["src/config/*"], "@types/*": ["src/types/*"]}}, "include": ["**/*.ts", "**/*.tsx"], "exclude": ["node_modules"]}