// Jobs Service - Handles job operations
import { apiService } from './api';
import { supabase } from '../lib/supabase';
import { Job } from '../types';

export interface CreateJobData {
  title: string;
  description: string;
  location?: string;
  dateTimeInfo: string;
  agePreference?: string;
  skills: string[];
  jobType: 'offer' | 'request';
  inlinePhotoId?: string;
  dataAiHint?: string;
}

class JobsService {
  // Option 1: Use Custom Backend (Recommended for business logic)
  async getJobsFromBackend(): Promise<Job[]> {
    try {
      console.log('🔄 Fetching jobs from custom backend...');
      const response = await apiService.getJobs();
      
      if (response.error) {
        throw new Error(response.error);
      }
      
      // Transform the data to ensure dates are Date objects and add missing fields
      const jobs = (response.data || []).map((job: any) => ({
        ...job,
        createdAt: new Date(job.createdAt),
        applications: job.applications || []
      }));
      
      return jobs;
    } catch (error) {
      console.error('❌ Error fetching jobs from backend:', error);
      throw error;
    }
  }

  async createJobViaBackend(jobData: CreateJobData): Promise<Job> {
    try {
      console.log('🔄 Creating job via custom backend...');
      const response = await apiService.createJob(jobData);
      
      if (response.error) {
        throw new Error(response.error);
      }
      
      // Transform the response to ensure dates are Date objects and add missing fields
      const job = {
        ...response.data,
        createdAt: new Date(response.data.createdAt),
        applications: response.data.applications || []
      };
      
      return job;
    } catch (error) {
      console.error('❌ Error creating job via backend:', error);
      throw error;
    }
  }

  // Option 2: Direct PostgREST (For simple CRUD operations)
  async getJobsFromSupabase(): Promise<Job[]> {
    try {
      console.log('🔄 Fetching jobs from Supabase...');
      const { data, error } = await supabase
        .from('Job')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        throw new Error(error.message);
      }

      return data || [];
    } catch (error) {
      console.error('❌ Error fetching jobs from Supabase:', error);
      throw error;
    }
  }

  async createJobViaSupabase(jobData: CreateJobData): Promise<Job> {
    try {
      console.log('🔄 Creating job via Supabase...');

      // Skip Supabase direct creation since we don't have user context here
      // This should only be used via backend API with proper authentication
      throw new Error('Direct Supabase creation not supported - use backend API');
    } catch (error) {
      console.error('❌ Error creating job via Supabase:', error);
      throw error;
    }
  }

  // Use backend API with token authentication
  async getJobs(): Promise<Job[]> {
    try {
      return await this.getJobsFromBackend();
    } catch (error) {
      console.error('❌ Failed to fetch jobs from backend:', error);
      throw new Error('Failed to fetch jobs');
    }
  }

  async createJob(jobData: CreateJobData): Promise<Job> {
    try {
      // Use backend API with token authentication
      return await this.createJobViaBackend(jobData);
    } catch (error) {
      console.error('❌ Failed to create job via backend:', error);
      throw new Error('Failed to create job');
    }
  }

  // Real-time subscriptions (only available via Supabase)
  subscribeToJobs(callback: (jobs: Job[]) => void) {
    console.log('🔄 Setting up real-time job subscription...');
    
    const subscription = supabase
      .channel('jobs')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'Job' },
        (payload) => {
          console.log('📡 Real-time job update:', payload);
          // Refetch jobs when changes occur
          this.getJobs().then(callback).catch(console.error);
        }
      )
      .subscribe();

    return () => {
      console.log('🔄 Unsubscribing from jobs...');
      subscription.unsubscribe();
    };
  }
}

export const jobsService = new JobsService();
export default jobsService;
