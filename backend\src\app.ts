import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import { createServer } from 'http';
import { Server } from 'socket.io';
import dotenv from 'dotenv';
import { initializeDatabase } from './config/dbInit';
import { getModuleLogger, subscribeToLogs } from './utils/logger';
import { requestLogger } from './middleware/requestLogger';
import { errorHandler } from './middleware/errorHandler';
import apiRouter from './routes';
import authRouter from './modules/auth/router';

// Load environment variables
dotenv.config();

const logger = getModuleLogger('App');

// Add debug logging to verify log level
logger.debug('App module initialized with level:', { level: logger.getLevel() });

// Initialize database
initializeDatabase()
  .then(() => {
    logger.info('Database initialization completed');
  })
  .catch((error: Error) => {
    logger.error('Database initialization failed:', error);
    process.exit(1);
  });

// Create Express app
const app = express();
const httpServer = createServer(app);
const io = new Server(httpServer, {
  cors: {
    origin: [
      'http://localhost:3000',
      'http://localhost:8081',
      'http://********:3000',
      'http://********:8081',
      'http://*********:3000',
      'http://*********:8081',
      // Allow any localhost origin for development
      /^http:\/\/localhost:\d+$/,
      /^http:\/\/10\.0\.2\.2:\d+$/,
      /^http:\/\/10\.0\.0\.16:\d+$/,
      // Allow Expo development origins
      /^http:\/\/.*\.exp\.direct:\d+$/,
      /^http:\/\/.*\.ngrok\.io$/
    ],
    methods: ['GET', 'POST'],
  },
});

// Rate limiting - relaxed for development
const limiter = rateLimit({
  windowMs: parseInt(process.env['RATE_LIMIT_WINDOW_MS'] || '60000'), // 1 minute default
  max: parseInt(process.env['RATE_LIMIT_MAX_REQUESTS'] || '1000'), // 1000 requests default for development
  message: 'Too many requests from this IP, please try again later.',
  skip: (req) => {
    // Skip rate limiting in development for localhost
    if (process.env['NODE_ENV'] === 'development') {
      const isLocalhost = req.ip === '127.0.0.1' || req.ip === '::1' || req.ip?.startsWith('::ffff:127.0.0.1');
      if (isLocalhost) {
        return true; // Skip rate limiting for localhost in development
      }
    }
    return false;
  }
});

// Middleware
app.use(helmet());
app.use(cors({
  origin: [
    'http://localhost:3000',
    'http://localhost:8081',
    'http://********:3000',
    'http://********:8081',
    'http://*********:3000',
    'http://*********:8081',
    // Allow any localhost origin for development
    /^http:\/\/localhost:\d+$/,
    /^http:\/\/10\.0\.2\.2:\d+$/,
    /^http:\/\/10\.0\.0\.16:\d+$/,
    // Allow Expo development origins
    /^http:\/\/.*\.exp\.direct:\d+$/,
    /^http:\/\/.*\.ngrok\.io$/
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
}));
app.use(compression());
app.use(express.json());
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(limiter);
app.use(requestLogger);

// Log all incoming requests before routing
app.use((req, _res, next) => {
  logger.debug('Request before routing:', {
    method: req.method,
    url: req.url,
    originalUrl: req.originalUrl,
    baseUrl: req.baseUrl,
    path: req.path
  });
  next();
});

// API Routes
app.use('/api', apiRouter);



// Legacy routes (for backward compatibility)
app.use('/auth', (req, _res, next) => {
  logger.warn('Legacy auth route accessed, consider migrating to /api/auth', {
    originalUrl: req.originalUrl
  });
  next();
});
app.use('/auth', authRouter);

// Real-time log streaming endpoint (SSE)
app.get('/logs/stream', (req, res) => {
  res.setHeader('Content-Type', 'text/event-stream');
  res.setHeader('Cache-Control', 'no-cache');
  res.setHeader('Connection', 'keep-alive');
  res.flushHeaders();

  const sendLog = (log: string) => {
    res.write(`data: ${log}\n\n`);
  };
  const unsubscribe = subscribeToLogs(sendLog);

  req.on('close', () => {
    unsubscribe();
    res.end();
  });
});

// Health check endpoint
app.get('/health', (_req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env['NODE_ENV'] || 'development',
  });
});

// API info endpoint
app.get('/api', (_req, res) => {
  res.json({
    message: 'Kibbutz Community API',
    version: '1.0.0',
    endpoints: {
      health: '/health',
      users: '/api/users',
      events: '/api/events',
      food: '/api/food',
      jobs: '/api/jobs',
      chat: '/api/chat',
      surveys: '/api/surveys',
      deals: '/api/deals',
      auth: '/api/auth',
    },
  });
});

// Root endpoint
app.get('/', (_req, res) => {
  res.json({ message: 'Welcome to Kibbutz Community API' });
});

// Log any requests that don't match our routes
app.use((req, _res, next) => {
  logger.info('Unmatched request:', {
    method: req.method,
    url: req.url,
    originalUrl: req.originalUrl,
    baseUrl: req.baseUrl,
    path: req.path
  });
  next();
});

// Socket.IO connection handling
io.on('connection', (socket) => {
  logger.info(`Client connected: ${socket.id}`);

  socket.on('disconnect', () => {
    logger.info(`Client disconnected: ${socket.id}`);
  });
});

// 404 handler
app.use('*', (req, res) => {
  logger.warn('Route not found:', { route: req.originalUrl, method: req.method });
  res.status(404).json({
    error: 'Not Found',
    message: `Route ${req.originalUrl} not found`,
  });
});

// Error handling middleware
app.use(errorHandler);

// Start server only if not imported (for tests)
const PORT = process.env['PORT'] || 3002;
if (require.main === module) {
  const initializeApp = async () => {
    // Initialize configuration and register all environment variables
    const { initializeConfig } = await import('./config/init');
    await initializeConfig();
    logger.info('Kibbutz Backend API configuration completed');
  };

  initializeApp()
    .then(() => {
      const server = httpServer.listen(PORT, () => {
        logger.info('🚀 Kibbutz Backend API started successfully', {
          port: PORT,
          environment: process.env['NODE_ENV'] || 'development',
          healthCheck: `http://localhost:${PORT}/health`,
          apiDocs: `http://localhost:${PORT}/api`
        });

        // Generate .env.example after all modules have registered their variables
        const { generateEnvExample } = require('./utils/env');
        generateEnvExample();
      });

      // Graceful shutdown
      const shutdown = async () => {
        logger.info('Shutting down gracefully...');

        // Close server first
        server.close(() => {
          logger.info('Server closed');
        });

        // Then disconnect from database
        const prisma = require('./config/prismaClient').default;
        await prisma.$disconnect();
        logger.info('Database disconnected');

        process.exit(0);
      };

      process.on('SIGINT', shutdown);
      process.on('SIGTERM', shutdown);
    })
    .catch((error: Error) => {
      logger.error('Failed to initialize application:', { error: error.message, stack: error.stack });
      process.exit(1);
    });
}

export { app, httpServer, io };
