import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import request from 'supertest';
import { app } from '../../../app';
import { SupabaseAuthWrapper } from '../../../__tests__/supabaseAuth';

describe('Job API', () => {
  let auth: SupabaseAuthWrapper;
  let testJob: any;

  beforeEach(async () => {
    // Create new auth wrapper for each test
    auth = new SupabaseAuthWrapper();
    // Sign up a new user for this test
    const { user, token } = await auth.signUp();
    expect(user).toBeDefined();
    expect(token).toBeDefined();
    expect(user.id).toBeDefined();
    expect(user.email).toBeDefined();
    
    // Create a test job via the public API
    const newJob = {
      title: 'Test Job',
      description: 'Test Description',
      status: 'open',
      jobType: 'offer',
      location: 'Test Location',
      dateTimeInfo: new Date().toISOString(),
      agePreference: '18+',
      skills: ['test'],
      inlinePhotoId: 'photo123',
      dataAiHint: 'Test hint'
    };
    const response = await request(app)
      .post('/api/jobs')
      .set('Authorization', `Bearer ${auth.getUserToken()}`)
      .send(newJob);
    expect(response.status).toBe(201);
    testJob = response.body;
  });

  afterEach(async () => {
    try {
      // Delete the test job via the public API (if it still exists)
      if (testJob && testJob.id) {
        await request(app)
          .delete(`/api/jobs/${testJob.id}`)
          .set('Authorization', `Bearer ${auth.getUserToken()}`);
      }
      // Delete the test user
      await auth.deleteUser();
    } catch (error) {
      console.error('Error in afterEach cleanup:', error);
    }
  });

  describe('Authentication', () => {
    it('should require valid Supabase token for protected routes', async () => {
      const response = await request(app)
        .get('/api/jobs')
        .set('Authorization', 'Bearer invalid-token');
      expect(response.status).toBe(401);
    });

    it('should accept valid Supabase token for protected routes', async () => {
      const response = await request(app)
        .get('/api/jobs')
        .set('Authorization', `Bearer ${auth.getUserToken()}`);
      expect(response.status).toBe(200);
    });
  });

  describe('Public Routes', () => {
    it('should get all jobs (unauthenticated should fail)', async () => {
      const response = await request(app)
        .get('/api/jobs');
      expect(response.status).toBe(401);
    });

    it('should get job by ID (unauthenticated should fail)', async () => {
      const response = await request(app)
        .get(`/api/jobs/${testJob.id}`);
      expect(response.status).toBe(401);
    });

    it('should return 404 for non-existent job (unauthenticated)', async () => {
      const response = await request(app)
        .get('/api/jobs/non-existent-id');
      expect(response.status).toBe(401);
    });
  });

  describe('Protected Routes', () => {
    it('should create a new job', async () => {
      const newJob = {
        title: 'New Job',
        description: 'New Description',
        status: 'open',
        jobType: 'request',
        location: 'New Location',
        dateTimeInfo: new Date().toISOString(),
        agePreference: '21+',
        skills: ['new skill'],
        inlinePhotoId: 'photo123',
        dataAiHint: 'Test hint'
      };

      const response = await request(app)
        .post('/api/jobs')
        .set('Authorization', `Bearer ${auth.getUserToken()}`)
        .send(newJob);

      expect(response.status).toBe(201);
      expect(response.body.title).toBe(newJob.title);
      expect(response.body.description).toBe(newJob.description);
      expect(response.body.status).toBe(newJob.status);
      expect(response.body.jobType).toBe(newJob.jobType);
      expect(response.body.location).toBe(newJob.location);
      expect(response.body.dateTimeInfo).toBe(newJob.dateTimeInfo);
      expect(response.body.agePreference).toBe(newJob.agePreference);
      expect(response.body.skills).toEqual(newJob.skills);
      expect(response.body.inlinePhotoId).toBe(newJob.inlinePhotoId);
      expect(response.body.dataAiHint).toBe(newJob.dataAiHint);
      
      // Clean up
      await request(app)
        .delete(`/api/jobs/${response.body.id}`)
        .set('Authorization', `Bearer ${auth.getUserToken()}`);
    });

    it('should not create job without authentication', async () => {
      const newJob = {
        title: 'New Job',
        description: 'New Description',
        status: 'open',
        jobType: 'request'
      };

      const response = await request(app)
        .post('/api/jobs')
        .send(newJob);

      expect(response.status).toBe(401);
    });

    it('should update a job', async () => {
      const updateData = {
        title: 'Updated Job',
        description: 'Updated Description',
        status: 'in_progress',
        jobType: 'offer',
        location: 'Updated Location',
        agePreference: '25+',
        skills: ['updated skill']
      };

      const response = await request(app)
        .put(`/api/jobs/${testJob.id}`)
        .set('Authorization', `Bearer ${auth.getUserToken()}`)
        .send(updateData);

      expect(response.status).toBe(200);
      expect(response.body.title).toBe(updateData.title);
      expect(response.body.description).toBe(updateData.description);
      expect(response.body.status).toBe(updateData.status);
      expect(response.body.jobType).toBe(updateData.jobType);
      expect(response.body.location).toBe(updateData.location);
      expect(response.body.agePreference).toBe(updateData.agePreference);
      expect(response.body.skills).toEqual(updateData.skills);
    });

    it('should not update job without authentication', async () => {
      const updateData = {
        title: 'Updated Job',
        description: 'Updated Description',
        status: 'in_progress',
        jobType: 'offer'
      };

      const response = await request(app)
        .put(`/api/jobs/${testJob.id}`)
        .send(updateData);

      expect(response.status).toBe(401);
    });

    it('should not update non-existent job', async () => {
      const updateData = {
        title: 'Updated Job',
        description: 'Updated Description',
        status: 'in_progress',
        jobType: 'offer'
      };

      const response = await request(app)
        .put('/api/jobs/non-existent-id')
        .set('Authorization', `Bearer ${auth.getUserToken()}`)
        .send(updateData);

      expect(response.status).toBe(404);
      expect(response.body.message).toBe('Job not found');
    });

    it('should delete a job', async () => {
      // Create a new job to delete
      const newJob = {
        title: 'Delete Me',
        description: 'To be deleted',
        status: 'open',
        jobType: 'request',
        location: 'Delete Location',
        dateTimeInfo: new Date().toISOString(),
        agePreference: '18+',
        skills: ['delete']
      };
      const createRes = await request(app)
        .post('/api/jobs')
        .set('Authorization', `Bearer ${auth.getUserToken()}`)
        .send(newJob);
      expect(createRes.status).toBe(201);
      const jobId = createRes.body.id;

      const response = await request(app)
        .delete(`/api/jobs/${jobId}`)
        .set('Authorization', `Bearer ${auth.getUserToken()}`);

      expect(response.status).toBe(204);

      // Verify job is deleted
      const getResponse = await request(app)
        .get(`/api/jobs/${jobId}`)
        .set('Authorization', `Bearer ${auth.getUserToken()}`);

      expect(getResponse.status).toBe(404);
    });

    it('should not delete job without authentication', async () => {
      const response = await request(app)
        .delete(`/api/jobs/${testJob.id}`);

      expect(response.status).toBe(401);
    });

    it('should not delete non-existent job', async () => {
      const response = await request(app)
        .delete('/api/jobs/non-existent-id')
        .set('Authorization', `Bearer ${auth.getUserToken()}`);

      expect(response.status).toBe(404);
    });
  });
});
