import { Platform } from 'react-native';

export const platformConfig = {
  isWeb: Platform.OS === 'web',
  isIOS: Platform.OS === 'ios',
  isAndroid: Platform.OS === 'android',
  
  // Platform-specific styles
  styles: {
    shadow: Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
      },
      android: {
        elevation: 5,
      },
      web: {
        boxShadow: '0px 2px 3.84px rgba(0, 0, 0, 0.25)',
      },
    }),
  },

  // Platform-specific features
  features: {
    // Web-specific features
    web: {
      isPWA: false,
      isSSR: false,
      isBrowser: true,
    },
    // iOS-specific features
    ios: {
      hasNotch: false, // You might want to detect this dynamically
      hasHomeIndicator: false,
    },
    // Android-specific features
    android: {
      hasNotch: false,
      hasNavigationBar: true,
    },
  },

  // Platform-specific permissions
  permissions: {
    web: ['geolocation', 'notifications', 'camera', 'microphone'],
    ios: ['camera', 'photoLibrary', 'location', 'notifications'],
    android: ['camera', 'storage', 'location', 'notifications'],
  },

  // Platform-specific navigation
  navigation: {
    web: {
      useHashRouter: false,
      baseUrl: '/',
    },
    native: {
      animationEnabled: true,
      gestureEnabled: true,
    },
  },
}; 