import React, { createContext, useContext, useEffect, useState, useRef, ReactNode } from 'react';
import { User as SupabaseUser } from '@supabase/supabase-js';
import { User } from '../types';
import {
  signIn,
  signOut,
  resetPassword,
  onAuthStateChange
} from '../lib/supabase';
import { AuthService } from '../services/auth';
import { apiService } from '../services/api';

export interface LoginData {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  name: string;
  phone?: string;
  dateOfBirth?: Date;
  userType: 'ADULT' | 'YOUTH' | 'CHILD' | 'EXTERNAL';
  role?: string;
}

interface AuthContextType {
  user: User | null;
  supabaseUser: SupabaseUser | null;
  loading: boolean;
  login: (data: LoginData) => Promise<void>;
  register: (data: RegisterData) => Promise<void>;
  logout: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [supabaseUser, setSupabaseUser] = useState<SupabaseUser | null>(null);
  const [loading, setLoading] = useState(true);
  const isRegisteringRef = useRef(false);

  // Helper function to force clear auth state with timeout
  const forceSignOut = async (timeoutMs: number = 5000) => {
    console.log('🔄 Force signOut with timeout:', timeoutMs + 'ms');

    const signOutPromise = signOut();
    const timeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error('SignOut timeout')), timeoutMs)
    );

    try {
      const result = await Promise.race([signOutPromise, timeoutPromise]);
      console.log('✅ SignOut completed within timeout');
      return result;
    } catch (error) {
      console.log('⏰ SignOut timed out or failed, forcing local cleanup');
      throw error;
    } finally {
      // Always clear local state regardless of signOut success/failure
      console.log('🔄 Clearing local auth state...');
      apiService.setToken(null);
      setSupabaseUser(null);
      setUser(null);
    }
  };

  useEffect(() => {
    // Check for existing session on mount
    const checkSession = async () => {
      console.log('🔍 Skipping session check - relying on auth state change');
      console.log('🧹 Clearing any existing auth data...');

      // Skip the hanging session check and just clear everything
      // Auth state change will handle authentication
      setSupabaseUser(null);
      setUser(null);
      setLoading(false);
    };

    checkSession();

    // Listen for auth state changes
    const { data: { subscription } } = onAuthStateChange(async (event, session) => {
      console.log('Auth state changed:', event, session?.user?.email);

      try {
        if (session?.user && session?.access_token) {
          console.log('🎫 Got token from auth state change');
          setSupabaseUser(session.user);

          // Set token in API service for all future requests
          apiService.setToken(session.access_token);

          // Load user profile using API service
          try {
            const response = await apiService.getUserProfile(session.user.id);
            if (response.data) {
              console.log('✅ User profile loaded via API service');
              console.log('👤 User profile data:', {
                id: response.data.id,
                email: response.data.email,
                role: response.data.role,
                user_type: response.data.user_type,
                first_name: response.data.first_name,
                last_name: response.data.last_name
              });
              setUser(response.data);
            } else {
              console.log('❌ User profile not found in database - creating new profile');

              // User exists in Supabase but not in our database
              // This can happen if the database was reset/deleted
              // Create the user profile in our database
              try {
                console.log('🔄 Creating user profile in backend database...');

                // Extract user data from Supabase user
                const userData = {
                  id: session.user.id,
                  email: session.user.email,
                  firstName: session.user.user_metadata?.name || session.user.user_metadata?.first_name || 'User',
                  lastName: session.user.user_metadata?.last_name || '',
                  phone: session.user.user_metadata?.phone || '',
                  userType: session.user.user_metadata?.user_type || 'ADULT', // Get user_type from metadata
                  role: 'USER', // Default role, first user will be upgraded to ADMIN by backend
                  language: 'he' // Hebrew language
                };

                const createResponse = await apiService.createUserProfile(userData);

                if (createResponse.data) {
                  console.log('✅ User profile created successfully');
                  setUser(createResponse.data);
                } else {
                  console.error('❌ Failed to create user profile:', createResponse.error);
                  throw new Error(createResponse.error || 'Failed to create user profile');
                }
              } catch (createError) {
                console.error('❌ Error creating user profile:', createError);
                // If we can't create the profile, sign out the user
                console.log('🔄 Signing out user due to profile creation failure');
                try {
                  await forceSignOut(3000);
                  console.log('✅ User signed out due to profile creation failure');
                } catch (signOutError) {
                  console.error('❌ Error during signOut:', signOutError);
                  console.log('✅ Local state cleared despite signOut error');
                }
              }
            }
          } catch (error) {
            console.error('❌ API error while loading user profile:', error);
            // For network errors, we'll just set loading to false and let the user try again
            // Don't automatically sign out for network issues
            console.log('⚠️ Network error occurred - user remains authenticated');
            setUser(null); // Clear user but keep Supabase auth
          }
        } else {
          setSupabaseUser(null);
          setUser(null);
        }
      } catch (error) {
        console.error('❌ Error in auth state change handler:', error);
        setSupabaseUser(null);
        setUser(null);
      } finally {
        setLoading(false);
      }
    });

    return () => {
      subscription.unsubscribe();
    };
  }, []);


  const login = async (data: LoginData) => {
    setLoading(true);
    try {
      console.log('🔐 Starting login process...');
      console.log('🔐 Email:', data.email);

      // First authenticate with Supabase
      console.log('🔐 Calling Supabase signIn...');
      const { data: authData, error: authError } = await signIn(data.email, data.password);

      console.log('🔐 Supabase response:', {
        hasData: !!authData,
        hasUser: !!authData?.user,
        hasError: !!authError,
        errorMessage: authError?.message
      });

      if (authError) {
        throw new Error(authError.message);
      }

      if (!authData?.user) {
        throw new Error('Login failed');
      }

      console.log('🔐 Supabase login successful, getting user profile...');
      // Then get the user profile from our backend
      const response = await apiService.getUserProfile(authData.user.id);
      console.log('🔐 User profile response:', { hasError: !!response.error, hasData: !!response.data });

      if (response.data) {
        console.log('👤 Login - User profile data:', {
          id: response.data.id,
          email: response.data.email,
          role: response.data.role,
          user_type: response.data.user_type,
          first_name: response.data.first_name,
          last_name: response.data.last_name
        });
      }

      if (response.error || !response.data) {
        // User exists in Supabase but not in our database
        console.log('❌ User profile not found in database, signing out from Supabase');
        try {
          await signOut();
        } catch (signOutError) {
          console.error('Error signing out:', signOutError);
        }
        throw new Error('User profile not found. Please register again.');
      }

      console.log('🔐 Login completed successfully');
      setUser(response.data);
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const register = async (data: RegisterData) => {
    setLoading(true);
    isRegisteringRef.current = true;
    try {
      console.log('🔥 Starting registration process...');
      const user = await AuthService.register(data);
      setUser(user);
      console.log('✅ Registration completed successfully');
    } catch (error) {
      console.error('Registration error:', error);
      throw error;
    } finally {
      isRegisteringRef.current = false;
      setLoading(false);
    }
  };

  const logout = async () => {
    setLoading(true);
    try {
      console.log('🚪 Starting logout process...');

      // Use the forceSignOut function with timeout to prevent hanging
      await forceSignOut(3000); // 3 second timeout

      console.log('✅ Logout completed successfully');
    } catch (error) {
      console.error('❌ Logout error:', error);
      // Even if logout fails, we still clear local state (done in forceSignOut)
      console.log('✅ Local state cleared despite logout error');
    } finally {
      setLoading(false);
    }
  };

  const handleResetPassword = async (email: string) => {
    try {
      const { error } = await resetPassword(email);
      if (error) {
        throw new Error(error.message);
      }
    } catch (error) {
      console.error('Password reset error:', error);
      throw error;
    }
  };

  const value: AuthContextType = {
    user,
    supabaseUser,
    loading,
    login,
    register,
    logout,
    resetPassword: handleResetPassword,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
