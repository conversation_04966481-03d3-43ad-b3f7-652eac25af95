import { Router } from 'express';
import { DebugController } from './controller';
import { getModuleLogger } from '../../utils/logger';

const logger = getModuleLogger('Debug');
const router = Router();
const controller = new DebugController();

// Get all tables
router.get('/tables', async (req, res) => {
  try {
    logger.info('Fetching all tables');
    await controller.getAllTables(req, res);
    logger.info('Successfully fetched all tables');
  } catch (error) {
    logger.error(`Error in get_all_tables: ${error}`);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch tables'
    });
  }
});

// Get table entries
router.get('/tables/:tableName/entries', async (req, res) => {
  try {
    const { tableName } = req.params;
    logger.info(`Fetching entries from table: ${tableName}`);
    await controller.getTableEntries(req, res);
    logger.info(`Successfully fetched entries from table: ${tableName}`);
  } catch (error) {
    logger.error(`Error in get_table_entries: ${error}`);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch table entries'
    });
  }
});

// Delete table entries
router.delete('/tables/:tableName/entries', async (req, res) => {
  try {
    const { tableName } = req.params;
    logger.info(`Deleting entries from table: ${tableName}`);
    await controller.deleteTableEntries(req, res);
    logger.info(`Successfully deleted entries from table: ${tableName}`);
  } catch (error) {
    logger.error(`Error in delete_table_entries: ${error}`);
    res.status(500).json({
      success: false,
      error: 'Failed to delete table entries'
    });
  }
});

export default router; 