import { Request, Response } from 'express';
import { AuthService } from './service';
import { getModuleLogger } from '../../utils/logger';
import { RegisterDTO } from './types';

const logger = getModuleLogger('Auth');
const authService = new AuthService();

// Add debug logging to verify log level
logger.debug('Auth module initialized with level:', { level: logger.getLevel() });

export class AuthController {
  async register(req: Request, res: Response) {
    try {
      const data: RegisterDTO = req.body;

      logger.debug('POST /auth/register - Request received:', {
        email: data.email,
        role: data.role
      });

      const result = await authService.register(data);

      const response = {
        success: true,
        data: result
      };

      logger.debug('Response body:', response);

      res.status(201).json(response);
      return;
    } catch (error) {
      logger.error('Registration error:', { error });
      
      if (error instanceof Error && error.message === 'User already exists') {
        const response = {
          success: false,
          error: 'User already exists'
        };
        logger.debug('Response body:', response);
        return res.status(409).json(response);
      }

      const response = {
        success: false,
        error: 'Failed to register user'
      };
      logger.debug('Response body:', response);
      res.status(500).json(response);
      return;
    }
  }

  async login(req: Request, res: Response) {
    try {
      const { email, password } = req.body;

      logger.debug('POST /auth/login - Request received:', { email });

      const result = await authService.login({ email, password });

      const response = {
        success: true,
        data: result
      };

      logger.debug('Response body:', response);
      res.json(response);
    } catch (error) {
      logger.error('Login error:', { error });
      
      const response = {
        success: false,
        error: 'Invalid credentials'
      };
      logger.debug('Response body:', response);
      res.status(401).json(response);
    }
  }

  async refreshToken(req: Request, res: Response) {
    try {
      const { refreshToken } = req.body;

      if (!refreshToken) {
        return res.status(400).json({
          success: false,
          error: 'Refresh token is required'
        });
      }

      const result = await authService.refreshToken(refreshToken);

      const response = {
        success: true,
        data: result
      };

      res.json(response);
      return;
    } catch (error) {
      logger.error('Token refresh error:', { error });
      
      const response = {
        success: false,
        error: 'Invalid refresh token'
      };
      res.status(401).json(response);
      return;
    }
  }

  async logout(req: Request, res: Response) {
    try {
      const { refreshToken } = req.body;

      if (!refreshToken) {
        return res.status(400).json({
          success: false,
          error: 'Refresh token is required'
        });
      }

      await authService.logout(refreshToken);

      const response = {
        success: true,
        message: 'Logged out successfully'
      };

      res.json(response);
      return;
    } catch (error) {
      logger.error('Logout error:', { error });
      
      const response = {
        success: false,
        error: 'Failed to logout'
      };
      res.status(500).json(response);
      return;
    }
  }
} 