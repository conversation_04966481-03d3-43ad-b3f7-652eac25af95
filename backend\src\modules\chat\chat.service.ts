import prisma from '../../config/prismaClient';
import { ChatGroup, ChatMessage } from './chat.types';
import { ForbiddenError, NotFoundError } from '../../utils/errors';

function mapToChatGroup(prismaChatGroup: any): ChatGroup {
  return {
    id: prismaChatGroup.id,
    name: prismaChatGroup.name,
    createdAt: prismaChatGroup.created_at,
    createdBy: prismaChatGroup.created_by,
    members: prismaChatGroup.members,
    iconUrl: prismaChatGroup.icon_url,
    dataAiHint: prismaChatGroup.data_ai_hint,
    lastMessage: prismaChatGroup.last_message,
    lastMessageTimestamp: prismaChatGroup.last_message_timestamp
  };
}

function mapToPrismaChatGroupData(chatGroup: Partial<ChatGroup> & { community_id?: string, communityId?: string }): any {
  const data: any = {};
  if (chatGroup.name !== undefined) data.name = chatGroup.name;
  if (chatGroup.createdAt !== undefined) data.created_at = chatGroup.createdAt;
  if (chatGroup.createdBy !== undefined) data.created_by = chatGroup.createdBy;
  if (chatGroup.members !== undefined) data.members = chatGroup.members;
  if (chatGroup.iconUrl !== undefined) data.icon_url = chatGroup.iconUrl;
  if (chatGroup.dataAiHint !== undefined) data.data_ai_hint = chatGroup.dataAiHint;
  if (chatGroup.lastMessage !== undefined) data.last_message = chatGroup.lastMessage;
  if (chatGroup.lastMessageTimestamp !== undefined) data.last_message_timestamp = chatGroup.lastMessageTimestamp;
  if (chatGroup.community_id !== undefined) data.community_id = chatGroup.community_id;
  if (chatGroup.communityId !== undefined) data.community_id = chatGroup.communityId;
  return data;
}

function mapToChatMessage(prismaChatMessage: any): ChatMessage {
  return {
    id: prismaChatMessage.id,
    groupId: prismaChatMessage.group_id,
    senderId: prismaChatMessage.sender_id,
    senderName: prismaChatMessage.sender_name,
    text: prismaChatMessage.text,
    timestamp: prismaChatMessage.timestamp,
    avatarUrl: prismaChatMessage.avatar_url,
    avatarDataAiHint: prismaChatMessage.avatar_data_ai_hint
  };
}

function mapToPrismaChatMessageData(message: any) {
  console.debug('[mapToPrismaChatMessageData] incoming message:', message);
  const data: any = {};
  // Handle both camelCase and snake_case for all required fields
  data.community_id = message.community_id || message.communityId;
  data.group_id = message.group_id || message.groupId;
  data.sender_id = message.sender_id || message.senderId;
  data.sender_name = message.sender_name || message.senderName;
  data.text = message.text;
  data.timestamp = message.timestamp;
  data.avatar_url = message.avatar_url || message.avatarUrl;
  if (message.avatar_data_ai_hint || message.avatarDataAiHint) {
    data.avatar_data_ai_hint = message.avatar_data_ai_hint || message.avatarDataAiHint;
  }
  return data;
}

export class ChatService {
  constructor() {
    // Using centralized Prisma client
  }

  private async checkGroupMembership(groupId: string, userId: string): Promise<boolean> {
    const group = await prisma.chatGroup.findUnique({
      where: { id: groupId },
      select: { members: true }
    });
    if (!group) {
      throw new NotFoundError('Chat group not found');
    }
    return group.members.includes(userId);
  }

  private async checkGroupOwnership(groupId: string, userId: string): Promise<boolean> {
    const group = await prisma.chatGroup.findUnique({
      where: { id: groupId }
    });
    if (!group) {
      throw new NotFoundError('Chat group not found');
    }
    return group.created_by === userId;
  }

  private async checkMessageOwnership(messageId: string, userId: string): Promise<boolean> {
    const message = await prisma.chatMessage.findUnique({
      where: { id: messageId }
    });
    if (!message) {
      throw new NotFoundError('Chat message not found');
    }
    return message.sender_id === userId;
  }

  async createChatGroup(chatGroup: Omit<ChatGroup, 'id'>): Promise<ChatGroup> {
    console.debug('[createChatGroup] input:', chatGroup);
    const prismaData = mapToPrismaChatGroupData(chatGroup);
    console.debug('[createChatGroup] prismaData:', prismaData);
    try {
      const created = await prisma.chatGroup.create({ data: prismaData });
      return mapToChatGroup(created);
    } catch (error) {
      console.error('[createChatGroup] Prisma error:', error);
      throw error;
    }
  }

  async getAllChatGroups(): Promise<ChatGroup[]> {
    const groups = await prisma.chatGroup.findMany({ orderBy: { created_at: 'desc' } });
    return groups.map(mapToChatGroup);
  }

  async getChatGroup(id: string, userId: string): Promise<ChatGroup> {
    const group = await prisma.chatGroup.findUnique({ where: { id } });
    if (!group) {
      throw new NotFoundError('Chat group not found');
    }
    const isMember = await this.checkGroupMembership(id, userId);
    if (!isMember) {
      throw new ForbiddenError('You are not a member of this chat group');
    }
    return mapToChatGroup(group);
  }

  async updateChatGroup(id: string, data: Partial<ChatGroup>, userId: string): Promise<ChatGroup> {
    const isOwner = await this.checkGroupOwnership(id, userId);
    if (!isOwner) {
      throw new ForbiddenError('Only the group owner can update group details');
    }
    const prismaData = mapToPrismaChatGroupData(data);
    const group = await prisma.chatGroup.update({ where: { id }, data: prismaData });
    return mapToChatGroup(group);
  }

  async deleteChatGroup(id: string, userId: string): Promise<void> {
    const isOwner = await this.checkGroupOwnership(id, userId);
    if (!isOwner) {
      throw new ForbiddenError('Only the group owner can delete the group');
    }
    await prisma.chatGroup.delete({ where: { id } });
  }

  async createChatMessage(message: Omit<ChatMessage, 'id'>, userId: string): Promise<ChatMessage> {
    const isMember = await this.checkGroupMembership(message.groupId, userId);
    if (!isMember) {
      throw new ForbiddenError('You are not a member of this chat group');
    }
    const prismaData = mapToPrismaChatMessageData(message);
    console.debug('[createChatMessage] prismaData:', prismaData);
    try {
      const created = await prisma.chatMessage.create({ data: prismaData });
      return mapToChatMessage(created);
    } catch (error) {
      console.error('[createChatMessage] Prisma error:', error);
      throw error;
    }
  }

  async getAllChatMessages(): Promise<ChatMessage[]> {
    const messages = await prisma.chatMessage.findMany({ orderBy: { timestamp: 'desc' } });
    return messages.map(mapToChatMessage);
  }

  async getChatMessagesByGroupId(groupId: string, userId: string): Promise<ChatMessage[]> {
    const isMember = await this.checkGroupMembership(groupId, userId);
    if (!isMember) {
      throw new ForbiddenError('You are not a member of this chat group');
    }
    const messages = await prisma.chatMessage.findMany({
      where: { group_id: groupId },
      orderBy: { timestamp: 'asc' }
    });
    return messages.map(mapToChatMessage);
  }

  async getChatMessage(id: string, userId: string): Promise<ChatMessage> {
    const message = await prisma.chatMessage.findUnique({ where: { id } });
    if (!message) {
      throw new NotFoundError('Chat message not found');
    }
    const isMember = await this.checkGroupMembership(message.group_id, userId);
    if (!isMember) {
      throw new ForbiddenError('You are not a member of this chat group');
    }
    return mapToChatMessage(message);
  }

  async updateChatMessage(id: string, data: Partial<ChatMessage>, userId: string): Promise<ChatMessage> {
    const isOwner = await this.checkMessageOwnership(id, userId);
    if (!isOwner) {
      throw new ForbiddenError('You can only edit your own messages');
    }
    const prismaData = mapToPrismaChatMessageData(data);
    const message = await prisma.chatMessage.update({ where: { id }, data: prismaData });
    return mapToChatMessage(message);
  }

  async deleteChatMessage(id: string, userId: string): Promise<void> {
    const isOwner = await this.checkMessageOwnership(id, userId);
    if (!isOwner) {
      throw new ForbiddenError('You can only delete your own messages');
    }
    await prisma.chatMessage.delete({ where: { id } });
  }
} 