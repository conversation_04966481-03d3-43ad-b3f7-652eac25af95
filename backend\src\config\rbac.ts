// RBAC (Role-Based Access Control) Configuration
// Define which roles can access which API endpoints

export interface RBACRule {
  path: string;
  method?: string | string[];
  roles: string[];
  description?: string;
}

// RBAC Policy Configuration
export const rbacRules: RBACRule[] = [
  // Admin-only routes (future use)
  {
    path: '/api/admin/*',
    roles: ['ADMIN'],
    description: 'Admin-only endpoints'
  },
  
  // Moderator and above routes (future use)
  {
    path: '/api/moderator/*',
    roles: ['ADMIN', 'MODERATOR'],
    description: 'Moderator and admin endpoints'
  },
  
  // User management routes - currently allowing all roles as requested
  {
    path: '/api/users*',
    roles: ['ADMIN', 'MODERATOR', 'USER'],
    description: 'User management endpoints - currently open to all roles'
  },
  
  // Event routes - currently allowing all roles as requested
  {
    path: '/api/events*',
    roles: ['ADMIN', 'MODERATOR', 'USER'],
    description: 'Event management endpoints - currently open to all roles'
  },
  
  // Food routes - currently allowing all roles as requested
  {
    path: '/api/food*',
    roles: ['ADMIN', 'MODERAT<PERSON>', 'USER'],
    description: 'Food management endpoints - currently open to all roles'
  },
  
  // Job routes - currently allowing all roles as requested
  {
    path: '/api/jobs*',
    roles: ['ADMIN', 'MODERATOR', 'USER'],
    description: 'Job management endpoints - currently open to all roles'
  },
  
  // Survey routes - currently allowing all roles as requested
  {
    path: '/api/surveys*',
    roles: ['ADMIN', 'MODERATOR', 'USER'],
    description: 'Survey management endpoints - currently open to all roles'
  },
  
  // Deal routes - currently allowing all roles as requested
  {
    path: '/api/deals*',
    roles: ['ADMIN', 'MODERATOR', 'USER'],
    description: 'Deal management endpoints - currently open to all roles'
  },

  // Chat routes - currently allowing all roles as requested
  {
    path: '/api/chat*',
    roles: ['ADMIN', 'MODERATOR', 'USER'],
    description: 'Chat endpoints - currently open to all roles'
  },

  // Debug routes - admin only
  {
    path: '/api/debug*',
    roles: ['ADMIN'],
    description: 'Debug endpoints - admin only'
  }
];

// Helper function to check if a user role has access to a specific path
export function hasAccess(userRole: string, requestPath: string, method?: string): boolean {
  // Find matching RBAC rule
  const matchingRule = rbacRules.find(rule => {
    // Simple wildcard matching for paths
    const pathPattern = rule.path.replace('*', '.*');
    const pathRegex = new RegExp(`^${pathPattern}$`);
    
    const pathMatches = pathRegex.test(requestPath);
    
    // Check method if specified in rule
    if (rule.method) {
      const allowedMethods = Array.isArray(rule.method) ? rule.method : [rule.method];
      const methodMatches = !method || allowedMethods.includes(method.toUpperCase());
      return pathMatches && methodMatches;
    }
    
    return pathMatches;
  });
  
  // If no rule found, deny access by default
  if (!matchingRule) {
    return false;
  }
  
  // Check if user role is in allowed roles
  return matchingRule.roles.includes(userRole);
}

// Helper function to get required roles for a path
export function getRequiredRoles(requestPath: string, method?: string): string[] {
  const matchingRule = rbacRules.find(rule => {
    const pathPattern = rule.path.replace('*', '.*');
    const pathRegex = new RegExp(`^${pathPattern}$`);
    
    const pathMatches = pathRegex.test(requestPath);
    
    if (rule.method) {
      const allowedMethods = Array.isArray(rule.method) ? rule.method : [rule.method];
      const methodMatches = !method || allowedMethods.includes(method.toUpperCase());
      return pathMatches && methodMatches;
    }
    
    return pathMatches;
  });
  
  return matchingRule ? matchingRule.roles : [];
}
