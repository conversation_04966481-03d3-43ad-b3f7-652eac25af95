import prisma from './prismaClient';
import { getModuleLogger } from '../utils/logger';

const logger = getModuleLogger('DatabaseInit');

export const initializeDatabase = async () => {
  try {
    logger.info('Starting database initialization...');

    // Test database connection
    await prisma.$connect();
    logger.info('Database connection established');

    // Check if any users exist
    const userCount = await prisma.user.count();
    logger.info(`Database contains ${userCount} users`);

    // Verify database schema is up to date
    logger.info('Verifying database schema...');

    // You can add more initialization logic here, such as:
    // - Creating default categories
    // - Setting up initial configuration
    // - Running data migrations

    logger.info('Database initialization completed successfully');
  } catch (error) {
    logger.error('Database initialization failed:', error);
    throw error;
  }
};
