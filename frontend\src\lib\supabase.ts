import { createClient } from '@supabase/supabase-js';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';

// Platform-specific URL configuration using React Native's Platform.select()
// This is the recommended best practice for cross-platform development
const getBaseUrl = (port: number) => {
  return Platform.select({
    android: `http://********:${port}`, // Android emulator uses ******** to access host machine
    ios: `http://localhost:${port}`,     // iOS simulator can use localhost
    default: `http://localhost:${port}`, // Web and other platforms use localhost
  });
};

// Supabase configuration - use .env variables or platform-specific defaults
const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL || getBaseUrl(8000);
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;
export const API_BASE_URL = process.env.EXPO_PUBLIC_API_URL || getBaseUrl(3002);

// Debug logging
console.log('🔧 Platform:', Platform.OS);
console.log('🔧 Supabase URL:', supabaseUrl);
console.log('🔧 API Base URL:', API_BASE_URL);
console.log('🔧 Environment variables:', {
  EXPO_PUBLIC_SUPABASE_URL: process.env.EXPO_PUBLIC_SUPABASE_URL,
  EXPO_PUBLIC_API_URL: process.env.EXPO_PUBLIC_API_URL,
});

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('🚨 Supabase configuration missing!');
  console.error('Please check your .env file for EXPO_PUBLIC_SUPABASE_URL and EXPO_PUBLIC_SUPABASE_ANON_KEY');
  // Instead of throwing an error, we'll create a client with placeholder values
  // This allows the app to load and show a proper error message to the user
  console.warn('⚠️ Using placeholder values for development. Please set up your environment variables.');
}

// Create Supabase client with proper configuration
export const supabase = createClient(
  supabaseUrl || 'https://your-project.supabase.co',
  supabaseAnonKey || 'your-anon-key',
  {
    auth: {
      storage: AsyncStorage,
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: false,
    },
    global: {
      headers: {
        'X-Client-Info': 'kibbutz-app@1.0.0',
      },
    },
  }
);

// Auth helper functions
export const signUp = async (email: string, password: string, userData: any) => {
  try {
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: userData // Store user metadata in Supabase
      }
    });

    if (authError) throw authError;

    return { data: authData, error: null };
  } catch (error) {
    console.error('Error in signUp:', error);
    return { data: null, error };
  }
};

export const signIn = async (email: string, password: string) => {
  return supabase.auth.signInWithPassword({
    email,
    password,
  });
};

export const signOut = async () => {
  return supabase.auth.signOut();
};

// getCurrentSession removed - we use tokens directly now

export const getCurrentUser = async () => {
  return supabase.auth.getUser();
};

export const resetPassword = async (email: string) => {
  return supabase.auth.resetPasswordForEmail(email);
};

export const onAuthStateChange = (callback: (event: any, session: any) => void) => {
  return supabase.auth.onAuthStateChange(callback);
};

export const clearAllAuthData = async () => {
  console.log('🧹 Clearing all auth data...');
  try {
    await supabase.auth.signOut();
    await AsyncStorage.removeItem('supabase.auth.token');
    console.log('✅ All auth data cleared');
  } catch (error) {
    console.error('❌ Error clearing auth data:', error);
    throw error;
  }
};

export const updateProfile = async (updates: { name?: string; email?: string }) => {
  const { data, error } = await supabase.auth.updateUser({
    email: updates.email,
    data: { name: updates.name },
  });
  return { data, error };
};

// Log configuration
console.log('🔐 Supabase Auth initialized');
console.log('🌐 Supabase URL:', supabaseUrl);
console.log('🔑 Using GoTrue authentication');
console.log('💾 Session storage: AsyncStorage');
console.log('🚀 Ready for authentication!');