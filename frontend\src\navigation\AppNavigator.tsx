import React, { useRef, useEffect } from 'react';
import { View, Text, ActivityIndicator } from 'react-native';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { useAuth } from '../contexts/AuthContext';
import AuthNavigator from './AuthNavigator';
import HomeScreen from '../screens/home/<USER>';
import EventsNavigator from './EventsNavigator';
import JobsNavigator from './JobsNavigator';
import PeopleNavigator from './PeopleNavigator';
import ChatNavigator from './ChatNavigator';
import DebugScreen from '../screens/debug/DebugScreen';

import ProfileScreen from '../screens/profile/ProfileScreen';
import SettingsScreen from '../screens/settings/SettingsScreen';
import { RootStackParamList } from './types';

const Stack = createStackNavigator<RootStackParamList>();
type TabParamList = {
  Home: undefined;
  Events: undefined;
  Jobs: undefined;
  People: undefined;
  Chat: undefined;
  Admin: undefined;
};

const Tab = createBottomTabNavigator<TabParamList>();

const MainTabs = () => {
  const { user } = useAuth();
  const isAdmin = user?.role === 'ADMIN';

  console.log('🎛️ MainTabs - User data:', {
    hasUser: !!user,
    userId: user?.id,
    userRole: user?.role,
    isAdmin: isAdmin
  });

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused }) => {
          let icon = '';

          switch (route.name) {
            case 'Home':
              icon = '🏠';
              break;
            case 'Events':
              icon = '📅';
              break;
            case 'Jobs':
              icon = '💼';
              break;
            case 'People':
              icon = '👥';
              break;
            case 'Chat':
              icon = '💬';
              break;
            case 'Debug':
              icon = '🔧';
              break;
          }

          return <Text style={{ fontSize: 24 }}>{icon}</Text>;
        },
        tabBarActiveTintColor: '#667eea',
        tabBarInactiveTintColor: '#2c3e50',
        headerShown: false,
      })}
    >
      <Tab.Screen name="Home" component={HomeScreen} options={{ tabBarLabel: 'דף הבית' }} />
      <Tab.Screen name="Events" component={EventsNavigator} options={{ tabBarLabel: 'אירועים' }} />
      <Tab.Screen name="Jobs" component={JobsNavigator} options={{ tabBarLabel: 'משרות' }} />
      <Tab.Screen name="People" component={PeopleNavigator} options={{ tabBarLabel: 'אנשים' }} />
      <Tab.Screen name="Chat" component={ChatNavigator} options={{ tabBarLabel: 'צ\'אט' }} />
      {isAdmin && (
        <Tab.Screen name="Debug" component={DebugScreen} options={{ tabBarLabel: 'ניהול מערכת' }} />
      )}
    </Tab.Navigator>
  );
};

// More placeholder screen



export default function AppNavigator() {
  const { user, supabaseUser, loading } = useAuth();
  const previousUserRef = useRef(user);
  const isInitialLoadRef = useRef(true);

  // Track user state changes to prevent unnecessary navigation resets
  useEffect(() => {
    if (isInitialLoadRef.current) {
      isInitialLoadRef.current = false;
    }
    previousUserRef.current = user;
  }, [user]);

  // Only show loading on initial app load, not on subsequent auth state changes
  if (loading && isInitialLoadRef.current) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#007AFF' }}>
        <ActivityIndicator size="large" color="#fff" />
        <Text style={{ color: '#fff', marginTop: 16, fontSize: 16 }}>Loading Kibbutz App...</Text>
      </View>
    );
  }

  // If user is authenticated but profile is not loaded yet, show loading only on initial load
  if (supabaseUser && !user && isInitialLoadRef.current) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#007AFF' }}>
        <ActivityIndicator size="large" color="#fff" />
        <Text style={{ color: '#fff', marginTop: 16, fontSize: 16 }}>Setting up your profile...</Text>
      </View>
    );
  }

  // Always render the same navigation structure to prevent resets
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      {user ? (
        <>
          <Stack.Screen name="Main" component={MainTabs} />
          <Stack.Screen name="Profile" component={ProfileScreen} />
          <Stack.Screen name="Settings" component={SettingsScreen} />
        </>
      ) : (
        <Stack.Screen name="Auth" component={AuthNavigator} />
      )}
    </Stack.Navigator>
  );
}
