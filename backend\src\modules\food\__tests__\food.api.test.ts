import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import request from 'supertest';
import { app } from '../../../app';
import { SupabaseAuthWrapper } from '../../../__tests__/supabaseAuth';

describe('Food API', () => {
  let auth: SupabaseAuthWrapper;
  let testFoodItem: any;

  beforeEach(async () => {
    // Create new auth wrapper for each test
    auth = new SupabaseAuthWrapper();
    // Sign up a new user for this test
    const { user, token } = await auth.signUp();
    expect(user).toBeDefined();
    expect(token).toBeDefined();
    expect(user.id).toBeDefined();
    expect(user.email).toBeDefined();
    
    // Create a test food item via the public API
    const newFoodItem = {
      name: 'Test Food',
      description: 'Test Description',
      price: '15.99',
      pickupDetails: 'Pick up at main entrance',
      inlinePhotoId: 'photo123',
      dataAiHint: 'Test hint'
    };
    const response = await request(app)
      .post('/api/food')
      .set('Authorization', `Bearer ${auth.getUserToken()}`)
      .send(newFoodItem);
    expect(response.status).toBe(201);
    testFoodItem = response.body;
  });

  afterEach(async () => {
    try {
      // Delete the test food item via the public API (if it still exists)
      if (testFoodItem && testFoodItem.id) {
        await request(app)
          .delete(`/api/food/${testFoodItem.id}`)
          .set('Authorization', `Bearer ${auth.getUserToken()}`);
      }
      // Delete the test user
      await auth.deleteUser();
    } catch (error) {
      console.error('Error in afterEach cleanup:', error);
    }
  });

  describe('Authentication', () => {
    it('should require valid Supabase token for protected routes', async () => {
      const response = await request(app)
        .get('/api/food')
        .set('Authorization', 'Bearer invalid-token');
      expect(response.status).toBe(401);
    });

    it('should accept valid Supabase token for protected routes', async () => {
      const response = await request(app)
        .get('/api/food')
        .set('Authorization', `Bearer ${auth.getUserToken()}`);
      expect(response.status).toBe(200);
    });
  });

  describe('Public Routes', () => {
    it('should get all food items (unauthenticated should fail)', async () => {
      const response = await request(app)
        .get('/api/food');
      expect(response.status).toBe(401);
    });

    it('should get food item by ID (unauthenticated should fail)', async () => {
      const response = await request(app)
        .get(`/api/food/${testFoodItem.id}`);
      expect(response.status).toBe(401);
    });

    it('should return 404 for non-existent food item (unauthenticated)', async () => {
      const response = await request(app)
        .get('/api/food/non-existent-id');
      expect(response.status).toBe(401);
    });
  });

  describe('Protected Routes', () => {
    it('should create a new food item', async () => {
      const newFoodItem = {
        name: 'New Food Item',
        description: 'New Description',
        price: '25.99',
        pickupDetails: 'Pick up at main entrance',
        inlinePhotoId: 'photo123',
        dataAiHint: 'Test hint'
      };

      const response = await request(app)
        .post('/api/food')
        .set('Authorization', `Bearer ${auth.getUserToken()}`)
        .send(newFoodItem);

      expect(response.status).toBe(201);
      expect(response.body.name).toBe(newFoodItem.name);
      expect(response.body.description).toBe(newFoodItem.description);
      expect(response.body.price).toBe(newFoodItem.price);
      expect(response.body.pickupDetails).toBe(newFoodItem.pickupDetails);
      expect(response.body.inlinePhotoId).toBe(newFoodItem.inlinePhotoId);
      expect(response.body.dataAiHint).toBe(newFoodItem.dataAiHint);
      
      // Clean up
      await request(app)
        .delete(`/api/food/${response.body.id}`)
        .set('Authorization', `Bearer ${auth.getUserToken()}`);
    });

    it('should not create food item without authentication', async () => {
      const newFoodItem = {
        name: 'New Food Item',
        description: 'New Description',
        price: '25.99'
      };

      const response = await request(app)
        .post('/api/food')
        .send(newFoodItem);

      expect(response.status).toBe(401);
    });

    it('should update a food item', async () => {
      const updateData = {
        name: 'Updated Food Item',
        description: 'Updated Description',
        price: '35.99',
        pickupDetails: 'Updated pickup details'
      };

      const response = await request(app)
        .put(`/api/food/${testFoodItem.id}`)
        .set('Authorization', `Bearer ${auth.getUserToken()}`)
        .send(updateData);

      expect(response.status).toBe(200);
      expect(response.body.name).toBe(updateData.name);
      expect(response.body.description).toBe(updateData.description);
      expect(response.body.price).toBe(updateData.price);
      expect(response.body.pickupDetails).toBe(updateData.pickupDetails);
    });

    it('should not update food item without authentication', async () => {
      const updateData = {
        name: 'Updated Food Item',
        description: 'Updated Description',
        price: '35.99'
      };

      const response = await request(app)
        .put(`/api/food/${testFoodItem.id}`)
        .send(updateData);

      expect(response.status).toBe(401);
    });

    it('should not update non-existent food item', async () => {
      const updateData = {
        name: 'Updated Food Item',
        description: 'Updated Description',
        price: '35.99'
      };

      const response = await request(app)
        .put('/api/food/non-existent-id')
        .set('Authorization', `Bearer ${auth.getUserToken()}`)
        .send(updateData);

      expect(response.status).toBe(404);
      expect(response.body.message).toBe('Food item not found');
    });

    it('should delete a food item', async () => {
      // Create a new food item to delete
      const newFoodItem = {
        name: 'Delete Me',
        description: 'To be deleted',
        price: '10.99',
        pickupDetails: 'Delete Location'
      };
      const createRes = await request(app)
        .post('/api/food')
        .set('Authorization', `Bearer ${auth.getUserToken()}`)
        .send(newFoodItem);
      expect(createRes.status).toBe(201);
      const foodItemId = createRes.body.id;

      const response = await request(app)
        .delete(`/api/food/${foodItemId}`)
        .set('Authorization', `Bearer ${auth.getUserToken()}`);

      expect(response.status).toBe(204);

      // Verify food item is deleted
      const getResponse = await request(app)
        .get(`/api/food/${foodItemId}`)
        .set('Authorization', `Bearer ${auth.getUserToken()}`);

      expect(getResponse.status).toBe(404);
    });

    it('should not delete food item without authentication', async () => {
      const response = await request(app)
        .delete(`/api/food/${testFoodItem.id}`);

      expect(response.status).toBe(401);
    });

    it('should not delete non-existent food item', async () => {
      const response = await request(app)
        .delete('/api/food/non-existent-id')
        .set('Authorization', `Bearer ${auth.getUserToken()}`);

      expect(response.status).toBe(404);
    });
  });
});
