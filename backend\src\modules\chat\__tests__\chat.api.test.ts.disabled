import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import request from 'supertest';
import { app } from '../../../app';
import { createTestUser, getAuthToken } from '../../../__tests__/setup';
import prisma from '../../../config/prismaClient';

describe('Chat API', () => {
  let userId1: string;
  let userId2: string;
  let groupId: string;
  let token1: string;
  let token2: string;

  beforeEach(async () => {
    // Create test users and get their auth tokens
    const user1 = await createTestUser();
    const user2 = await createTestUser();
    userId1 = user1.id;
    userId2 = user2.id;
    token1 = await getAuthToken(user1);
    token2 = await getAuthToken(user2);
  });

  afterEach(async () => {
    // Clean up test data
    await prisma.chatMessage.deleteMany({});
    await prisma.chatGroup.deleteMany({});
    await prisma.user.deleteMany({ where: { id: { in: [userId1, userId2] } } });
  });

  describe('Chat Group API', () => {
    it('should create a new chat group', async () => {
      const response = await request(app)
        .post('/apichat/groups')
        .set('Authorization', `Bearer ${token1}`)
        .send({
          name: 'New Group',
          community_id: 'test-community',
          members: [userId1, userId2]
        });

      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('id');
      expect(response.body.name).toBe('New Group');
      expect(response.body.members).toContain(userId1);
      expect(response.body.members).toContain(userId2);
      
      groupId = response.body.id;
    });

    it('should get all chat groups for a user', async () => {
      // First create a group
      const createResponse = await request(app)
        .post('/apichat/groups')
        .set('Authorization', `Bearer ${token1}`)
        .send({
          name: 'Test Group',
          community_id: 'test-community',
          members: [userId1, userId2]
        });

      groupId = createResponse.body.id;

      const response = await request(app)
        .get('/apichat/groups')
        .set('Authorization', `Bearer ${token1}`);

      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
      expect(response.body[0].members).toContain(userId1);
    });

    it('should get a specific chat group', async () => {
      // First create a group
      const createResponse = await request(app)
        .post('/apichat/groups')
        .set('Authorization', `Bearer ${token1}`)
        .send({
          name: 'Test Group',
          community_id: 'test-community',
          members: [userId1, userId2]
        });

      groupId = createResponse.body.id;

      const response = await request(app)
        .get(`/apichat/groups/${groupId}`)
        .set('Authorization', `Bearer ${token1}`);

      expect(response.status).toBe(200);
      expect(response.body.id).toBe(groupId);
      expect(response.body.name).toBe('Test Group');
      expect(response.body.members).toContain(userId1);
      expect(response.body.members).toContain(userId2);
    });

    it('should update a chat group', async () => {
      // First create a group
      const createResponse = await request(app)
        .post('/apichat/groups')
        .set('Authorization', `Bearer ${token1}`)
        .send({
          name: 'Test Group',
          community_id: 'test-community',
          members: [userId1, userId2]
        });

      groupId = createResponse.body.id;

      const response = await request(app)
        .put(`/apichat/groups/${groupId}`)
        .set('Authorization', `Bearer ${token1}`)
        .send({
          name: 'Updated Group Name'
        });

      expect(response.status).toBe(200);
      expect(response.body.id).toBe(groupId);
      expect(response.body.name).toBe('Updated Group Name');
    });

    it('should delete a chat group', async () => {
      // First create a group
      const createResponse = await request(app)
        .post('/apichat/groups')
        .set('Authorization', `Bearer ${token1}`)
        .send({
          name: 'Test Group',
          community_id: 'test-community',
          members: [userId1, userId2]
        });

      groupId = createResponse.body.id;

      const response = await request(app)
        .delete(`/apichat/groups/${groupId}`)
        .set('Authorization', `Bearer ${token1}`);

      expect(response.status).toBe(204);

      // Verify the group is deleted
      const getResponse = await request(app)
        .get(`/apichat/groups/${groupId}`)
        .set('Authorization', `Bearer ${token1}`);

      expect(getResponse.status).toBe(404);
    });
  });

  describe('Chat Messages API', () => {
    beforeEach(async () => {
      // Create a test group first
      const createResponse = await request(app)
        .post('/apichat/groups')
        .set('Authorization', `Bearer ${token1}`)
        .send({
          name: 'Test Group',
          community_id: 'test-community',
          members: [userId1, userId2]
        });

      groupId = createResponse.body.id;
    });

    it('should create a new message', async () => {
      const response = await request(app)
        .post('/apichat/messages')
        .set('Authorization', `Bearer ${token1}`)
        .send({
          group_id: groupId,
          community_id: 'test-community',
          text: 'Hello, world!',
          avatar_url: 'https://example.com/avatar.jpg'
        });

      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('id');
      expect(response.body.text).toBe('Hello, world!');
      expect(response.body.sender_id).toBe(userId1);
    });

    it('should get messages for a group', async () => {
      // First create a message
      await request(app)
        .post('/apichat/messages')
        .set('Authorization', `Bearer ${token1}`)
        .send({
          group_id: groupId,
          community_id: 'test-community',
          text: 'Test message',
          avatar_url: 'https://example.com/avatar.jpg'
        });

      const response = await request(app)
        .get(`/apichat/groups/${groupId}/messages`)
        .set('Authorization', `Bearer ${token1}`);

      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
      expect(response.body[0].text).toBe('Test message');
    });

    it('should update a message', async () => {
      // First create a message
      const createResponse = await request(app)
        .post('/apichat/messages')
        .set('Authorization', `Bearer ${token1}`)
        .send({
          group_id: groupId,
          community_id: 'test-community',
          text: 'Original message',
          avatar_url: 'https://example.com/avatar.jpg'
        });

      const messageId = createResponse.body.id;

      const response = await request(app)
        .put(`/apichat/messages/${messageId}`)
        .set('Authorization', `Bearer ${token1}`)
        .send({
          text: 'Updated message'
        });

      expect(response.status).toBe(200);
      expect(response.body.id).toBe(messageId);
      expect(response.body.text).toBe('Updated message');
    });

    it('should delete a message', async () => {
      // First create a message
      const createResponse = await request(app)
        .post('/apichat/messages')
        .set('Authorization', `Bearer ${token1}`)
        .send({
          group_id: groupId,
          community_id: 'test-community',
          text: 'Test message',
          avatar_url: 'https://example.com/avatar.jpg'
        });

      const messageId = createResponse.body.id;

      const response = await request(app)
        .delete(`/apichat/messages/${messageId}`)
        .set('Authorization', `Bearer ${token1}`);

      expect(response.status).toBe(204);

      // Verify the message is deleted
      const getResponse = await request(app)
        .get(`/apichat/messages/${messageId}`)
        .set('Authorization', `Bearer ${token1}`);

      expect(getResponse.status).toBe(404);
    });
  });
}); 