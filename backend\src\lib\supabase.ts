// Supabase client for backend JWT verification
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env['SUPABASE_PUBLIC_URL'] || 'http://localhost:8000';
const supabaseServiceKey = process.env['SUPABASE_SERVICE_ROLE_KEY'] || '';

if (!supabaseServiceKey) {
  throw new Error('SUPABASE_SERVICE_ROLE_KEY is required for backend Supabase client');
}

// Create Supabase client with service role key for backend operations
export const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
});

// Function to verify JWT token and get user
export async function verifySupabaseJWT(token: string) {
  try {
    const { data: { user }, error } = await supabase.auth.getUser(token);
    
    if (error) {
      throw new Error(`JWT verification failed: ${error.message}`);
    }
    
    if (!user) {
      throw new Error('No user found for this token');
    }
    
    return user;
  } catch (error) {
    console.error('JWT verification error:', error);
    throw error;
  }
}

export default supabase;
