import React from 'react';
import { StyleSheet } from 'react-native';
import { List, useTheme } from 'react-native-paper';
import { rtlConfig } from '../../theme/rtl';

interface SelectProps {
  label: string;
  value: string;
  options: { label: string; value: string }[];
  onSelect: (value: string) => void;
  error?: string;
  style?: any;
}

export const Select = ({
  label,
  value,
  options,
  onSelect,
  error,
  style,
}: SelectProps) => {
  const theme = useTheme();
  const [expanded, setExpanded] = React.useState(false);

  const selectedOption = options.find(opt => opt.value === value);

  return (
    <List.Accordion
      title={label}
      description={selectedOption?.label}
      expanded={expanded}
      onPress={() => setExpanded(!expanded)}
      style={[styles.container, style]}
      titleStyle={[
        styles.title,
        { color: error ? theme.colors.error : theme.colors.onSurface },
      ]}
    >
      {options.map((option) => (
        <List.Item
          key={option.value}
          title={option.label}
          onPress={() => {
            onSelect(option.value);
            setExpanded(false);
          }}
          style={[
            styles.item,
            { backgroundColor: value === option.value ? theme.colors.primaryContainer : undefined },
          ]}
          titleStyle={[
            styles.itemTitle,
            { color: value === option.value ? theme.colors.onPrimaryContainer : theme.colors.onSurface },
          ]}
        />
      ))}
    </List.Accordion>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
  },
  title: {
    fontSize: 16,
  },
  item: {
    paddingLeft: 16,
  },
  itemTitle: {
    fontSize: 14,
  },
}); 