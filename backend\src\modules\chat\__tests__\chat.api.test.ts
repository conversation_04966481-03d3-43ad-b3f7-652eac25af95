import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import request from 'supertest';
import { app } from '../../../app';
import { SupabaseAuthWrapper } from '../../../__tests__/supabaseAuth';
import { logger } from '../../../utils/logger';

describe('Chat API', () => {
  let auth: SupabaseAuthWrapper;
  let testGroup: any;
  let testMessage: any;

  beforeEach(async () => {
    // Create new auth wrapper for each test
    auth = new SupabaseAuthWrapper();
    // Sign up a new user for this test
    const { user, token } = await auth.signUp();
    expect(user).toBeDefined();
    expect(token).toBeDefined();
    expect(user.id).toBeDefined();
    expect(user.email).toBeDefined();

    // Create a test group via the public API
    const newGroup = {
      name: 'Test Group',
      community_id: 'test-community',
      createdBy: user.id,
      createdAt: new Date().toISOString(),
      members: [user.id]
    };
    const response = await request(app)
      .post('/api/chat/groups')
      .set('Authorization', `Bearer ${auth.getUserToken()}`)
      .send(newGroup);
    expect(response.status).toBe(201);
    testGroup = response.body;
  });

  afterEach(async () => {
    logger.debug('Starting cleanup after test');
    
    try {
      // Delete test messages via API
      if (testMessage) {
        logger.debug('Deleting test message');
        await request(app)
          .delete(`/api/chat/messages/${testMessage.id}`)
          .set('Authorization', `Bearer ${auth.getUserToken()}`);
      }
      
      // Delete test groups via API
      if (testGroup) {
        logger.debug('Deleting test group');
        await request(app)
          .delete(`/api/chat/groups/${testGroup.id}`)
          .set('Authorization', `Bearer ${auth.getUserToken()}`);
      }
      
      // Delete auth user and sign out
      if (auth) {
        logger.debug('Deleting auth user');
        await auth.deleteUser();
        logger.debug('Signing out');
        await auth.signOut();
      }
    } catch (error) {
      logger.error('Error during cleanup:', error);
      // Don't throw the error to allow tests to continue
    }
    
    logger.debug('Cleanup completed');
  });

  describe('Authentication', () => {
    it('should require valid Supabase token for protected routes', async () => {
      const response = await request(app)
        .get('/api/chat/groups')
        .set('Authorization', 'Bearer invalid-token');
      expect(response.status).toBe(401);
    });

    it('should accept valid Supabase token for protected routes', async () => {
      const response = await request(app)
        .get('/api/chat/groups')
        .set('Authorization', `Bearer ${auth.getUserToken()}`);
      expect(response.status).toBe(200);
    });
  });

  describe('Chat Group API', () => {
    it('should create a new chat group', async () => {
      const newGroup = {
        name: 'New Group',
        community_id: 'test-community',
        createdBy: auth.getCurrentUser().id,
        createdAt: new Date().toISOString(),
        members: [auth.getCurrentUser().id]
      };
      const response = await request(app)
        .post('/api/chat/groups')
        .set('Authorization', `Bearer ${auth.getUserToken()}`)
        .send(newGroup);

      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('id');
      expect(response.body.name).toBe(newGroup.name);
      expect(response.body.members).toContain(auth.getCurrentUser().id);

      // Clean up
      await request(app)
        .delete(`/api/chat/groups/${response.body.id}`)
        .set('Authorization', `Bearer ${auth.getUserToken()}`);
    });

    it('should get all chat groups for a user', async () => {
      const response = await request(app)
        .get('/api/chat/groups')
        .set('Authorization', `Bearer ${auth.getUserToken()}`);

      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
      expect(response.body[0].members).toContain(auth.getCurrentUser().id);
    });

    it('should get a specific chat group', async () => {
      const response = await request(app)
        .get(`/api/chat/groups/${testGroup.id}`)
        .set('Authorization', `Bearer ${auth.getUserToken()}`);

      expect(response.status).toBe(200);
      expect(response.body.id).toBe(testGroup.id);
      expect(response.body.name).toBe(testGroup.name);
      expect(response.body.members).toContain(auth.getCurrentUser().id);
    });

    it('should update a chat group', async () => {
      const updateData = {
        name: 'Updated Group Name'
      };
      const response = await request(app)
        .put(`/api/chat/groups/${testGroup.id}`)
        .set('Authorization', `Bearer ${auth.getUserToken()}`)
        .send(updateData);

      expect(response.status).toBe(200);
      expect(response.body.id).toBe(testGroup.id);
      expect(response.body.name).toBe(updateData.name);
    });

    it('should delete a chat group', async () => {
      const response = await request(app)
        .delete(`/api/chat/groups/${testGroup.id}`)
        .set('Authorization', `Bearer ${auth.getUserToken()}`);

      expect(response.status).toBe(204);

      // Verify the group is deleted
      const getResponse = await request(app)
        .get(`/api/chat/groups/${testGroup.id}`)
        .set('Authorization', `Bearer ${auth.getUserToken()}`);

      expect(getResponse.status).toBe(404);
    });
  });

  describe('Chat Messages API', () => {
    beforeEach(async () => {
      // Create a test message
      const newMessage = {
        group_id: testGroup.id,
        community_id: 'test-community',
        text: 'Test message',
        avatar_url: 'https://example.com/avatar.jpg',
        sender_id: auth.getCurrentUser().id,
        sender_name: auth.getCurrentUser().email,
        timestamp: new Date().toISOString()
      };
      const response = await request(app)
        .post('/api/chat/messages')
        .set('Authorization', `Bearer ${auth.getUserToken()}`)
        .send(newMessage);
      expect(response.status).toBe(201);
      testMessage = response.body;
    });

    it('should create a new message', async () => {
      const newMessage = {
        group_id: testGroup.id,
        community_id: 'test-community',
        text: 'Test message',
        avatar_url: 'https://example.com/avatar.jpg',
        sender_id: auth.getCurrentUser().id,
        sender_name: auth.getCurrentUser().email,
        timestamp: new Date().toISOString()
      };

      const response = await request(app)
        .post('/api/chat/messages')
        .set('Authorization', `Bearer ${auth.getUserToken()}`)
        .send(newMessage);

      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('id');
      expect(response.body.text).toBe(newMessage.text);
      expect(response.body.senderId).toBe(auth.getCurrentUser().id);

      // Clean up
      await request(app)
        .delete(`/api/chat/messages/${response.body.id}`)
        .set('Authorization', `Bearer ${auth.getUserToken()}`);
    });

    it('should get messages for a group', async () => {
      const response = await request(app)
        .get(`/api/chat/groups/${testGroup.id}/messages`)
        .set('Authorization', `Bearer ${auth.getUserToken()}`);

      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
      expect(response.body[0].groupId).toBe(testGroup.id);
    });

    it('should update a message', async () => {
      const updateData = {
        text: 'Updated message text'
      };
      const response = await request(app)
        .put(`/api/chat/messages/${testMessage.id}`)
        .set('Authorization', `Bearer ${auth.getUserToken()}`)
        .send(updateData);

      expect(response.status).toBe(200);
      expect(response.body.id).toBe(testMessage.id);
      expect(response.body.text).toBe(updateData.text);
    });

    it('should delete a message', async () => {
      const response = await request(app)
        .delete(`/api/chat/messages/${testMessage.id}`)
        .set('Authorization', `Bearer ${auth.getUserToken()}`);

      expect(response.status).toBe(204);

      // Verify the message is deleted
      const getResponse = await request(app)
        .get(`/api/chat/groups/${testGroup.id}/messages`)
        .set('Authorization', `Bearer ${auth.getUserToken()}`);

      expect(getResponse.status).toBe(200);
      expect(getResponse.body.find((m: any) => m.id === testMessage.id)).toBeUndefined();
    });
  });
}); 