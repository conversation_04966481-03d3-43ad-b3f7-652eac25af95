import { Router } from 'express';
import { FoodController } from './food.controller';

const router = Router();
const foodController = new FoodController();

// Public routes
router.get('/', foodController.getFoodItems.bind(foodController));
router.get('/:id', foodController.getFoodItem.bind(foodController));

// Protected routes
router.post('/', foodController.createFoodItem.bind(foodController));
router.put('/:id', foodController.updateFoodItem.bind(foodController));
router.delete('/:id', foodController.deleteFoodItem.bind(foodController));

export default router; 