import { getSupabaseClient } from '../../config/supabase';
import { getModuleLogger } from '../../utils/logger';
import { LoginDTO, RegisterDTO, AuthResponse } from './types';
import { UserRole, UserType } from '../../generated/prisma';
import prisma from '../../config/prismaClient';

const logger = getModuleLogger('Auth');

export class AuthService {
  async register(data: RegisterDTO): Promise<AuthResponse> {
    try {
      logger.debug('Registering new user:', { email: data.email, role: data.role });

      // Check if this is the first user
      const userCount = await prisma.user.count();
      const isFirstUser = userCount === 0;
      const userRole = isFirstUser ? UserRole.ADMIN : (data.role || UserRole.USER);

      // Register user with Supabase Auth
      const supabase = getSupabaseClient();
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: data.email,
        password: data.password,
        options: {
          data: {
            role: userRole,
            user_type: data.userType || UserType.ADULT,
            first_name: data.firstName,
            last_name: data.lastName,
            language: data.language || 'en'
          }
        }
      });

      if (authError) throw authError;
      if (!authData.user) throw new Error('Failed to create user');

      // Use the session from auth data instead of making another call
      if (!authData.session) throw new Error('Failed to get session');
      const sessionData = { session: authData.session };

      return {
        success: true,
        data: {
          accessToken: sessionData.session.access_token,
          refreshToken: sessionData.session.refresh_token,
          user: {
            id: authData.user.id,
            email: authData.user.email!,
            role: userRole,
            user_type: data.userType || UserType.ADULT,
            first_name: data.firstName || null,
            last_name: data.lastName || null,
            language: data.language || 'en',
            phone: null,
            is_active: true,
            last_login: new Date(),
            google_id: null,
            created_at: new Date(),
            updated_at: new Date()
          }
        }
      };
    } catch (error) {
      logger.error('Registration error:', error);
      throw error;
    }
  }

  async login(data: LoginDTO): Promise<AuthResponse> {
    try {
      logger.debug('Starting login process:', { email: data.email });

      const supabase = getSupabaseClient();
      // Sign in with Supabase Auth
      const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
        email: data.email,
        password: data.password
      });

      if (authError) throw authError;
      if (!authData.user) throw new Error('Invalid credentials');

      // Use the session from auth data instead of making another call
      if (!authData.session) throw new Error('Failed to get session');
      const sessionData = { session: authData.session };

      const userMetadata = authData.user.user_metadata;
      return {
        success: true,
        data: {
          accessToken: sessionData.session.access_token,
          refreshToken: sessionData.session.refresh_token,
          user: {
            id: authData.user.id,
            email: authData.user.email!,
            role: userMetadata['role'] as UserRole,
            user_type: userMetadata['user_type'] as UserType,
            first_name: userMetadata['first_name'] || null,
            last_name: userMetadata['last_name'] || null,
            language: userMetadata['language'] || 'en',
            phone: userMetadata['phone'] || null,
            is_active: true,
            last_login: new Date(),
            google_id: userMetadata['google_id'] || null,
            created_at: new Date(authData.user.created_at),
            updated_at: new Date()
          }
        }
      };
    } catch (error) {
      logger.error('Login error:', error);
      throw error;
    }
  }

  async refreshToken(refreshToken: string): Promise<{ accessToken: string }> {
    try {
      const supabase = getSupabaseClient();
      const { data, error } = await supabase.auth.refreshSession({
        refresh_token: refreshToken
      });

      if (error) throw error;
      if (!data.session) throw new Error('Failed to refresh session');

      return { accessToken: data.session.access_token };
    } catch (error) {
      logger.error('Token refresh error:', error);
      throw error;
    }
  }

  async logout(_refreshToken: string): Promise<void> {
    try {
      const supabase = getSupabaseClient();
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
    } catch (error) {
      logger.error('Logout error:', error);
      throw error;
    }
  }

  async deleteUser(userId: string): Promise<void> {
    try {
      const supabase = getSupabaseClient();
      const { error } = await supabase.auth.admin.deleteUser(userId);
      if (error) {
        logger.error('Failed to delete user:', error);
        throw error;
      }
      logger.info('User deleted successfully:', userId);
    } catch (error) {
      logger.error('Delete user error:', error);
      throw error;
    }
  }
} 