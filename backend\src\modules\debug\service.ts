import prisma from '../../config/prismaClient';
import { getModuleLogger } from '../../utils/logger';
import { DeleteTableEntriesResponse, GetTableEntriesResponse, TableListResponse } from './types';

const logger = getModuleLogger('Debug');

export class DebugService {
  async getAllTables(): Promise<TableListResponse> {
    try {
      // Get all model names from Prisma schema
      const tables = Object.keys(prisma).filter(key => 
        typeof (prisma as any)[key] === 'object' && 
        'findMany' in (prisma as any)[key]
      );
      
      return { tables };
    } catch (error) {
      logger.error(`Error getting all tables: ${error}`);
      throw error;
    }
  }

  async getTableEntries(tableName: string, limit: number = 100): Promise<GetTableEntriesResponse> {
    try {
      const table = (prisma as any)[tableName];
      if (!table) {
        throw new Error(`Table ${tableName} not found`);
      }

      const [entries, total] = await Promise.all([
        table.findMany({
          take: limit,
        }),
        table.count()
      ]);

      return {
        entries,
        total
      };
    } catch (error) {
      logger.error(`Error getting entries from table ${tableName}: ${error}`);
      throw error;
    }
  }

  async deleteTableEntries(tableName: string): Promise<DeleteTableEntriesResponse> {
    try {
      const table = (prisma as any)[tableName];
      if (!table) {
        throw new Error(`Table ${tableName} not found`);
      }

      const { count } = await table.deleteMany({});
      
      return {
        deletedCount: count
      };
    } catch (error) {
      logger.error(`Error deleting entries from table ${tableName}: ${error}`);
      throw error;
    }
  }
} 