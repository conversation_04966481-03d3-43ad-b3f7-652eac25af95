// Super simple i18n for web compatibility
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

const resources = {
  he: {
    translation: {
      "auth.login": "התחברות",
      "auth.email": "כתובת אימייל",
      "auth.password": "סיסמה",
      "auth.loginButton": "התחבר",
      "auth.welcomeTitle": "ברוכים הבאים לאפליקציית הקיבוץ",
      "navigation.events": "אירועים",
      "navigation.chat": "צ'אט",
      "navigation.jobs": "עבודות",
      "navigation.profile": "פרופיל"
    }
  },
  en: {
    translation: {
      "auth.login": "Login",
      "auth.email": "Email",
      "auth.password": "Password",
      "auth.loginButton": "Login",
      "auth.welcomeTitle": "Welcome to Kibbutz Community App",
      "navigation.events": "Events",
      "navigation.chat": "Chat",
      "navigation.jobs": "Jobs",
      "navigation.profile": "Profile"
    }
  }
};

i18n.use(initReactI18next).init({
  resources,
  lng: 'he',
  fallbackLng: 'he',
  interpolation: {
    escapeValue: false,
  },
  react: {
    useSuspense: false,
  },
});

export default i18n;
