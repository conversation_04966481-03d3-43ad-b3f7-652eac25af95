export interface Job {
  id: string;
  community_id: string;
  title: string;
  description: string;
  location?: string | null;
  date_time_info: string;
  age_preference?: string | null;
  skills: string[];
  status: 'OPEN' | 'IN_PROGRESS' | 'COMPLETED'; // Match database enum
  job_type: 'OFFER' | 'REQUEST'; // Match database enum
  posted_by_id: string;
  posted_by_name: string;
  inline_photo_id?: string | null;
  data_ai_hint?: string | null;
  created_at: Date | null;
}

// Frontend-compatible interface for API responses
export interface JobResponse {
  id: string;
  title: string;
  description: string;
  location?: string;
  dateTimeInfo: string;
  agePreference?: string;
  skills: string[];
  status: 'open' | 'in_progress' | 'completed'; // Frontend uses lowercase
  jobType: 'offer' | 'request'; // Frontend uses lowercase
  postedById: string;
  postedByName: string;
  inlinePhotoId?: string;
  dataAiHint?: string;
  createdAt: Date;
  applications: any[]; // Empty array for now
}