import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { useRoute, RouteProp } from '@react-navigation/native';
import { getTextAlign } from '../../utils/rtl';
import Header from '../../components/Header';
import WebCompatibleScrollView from '../../components/WebCompatibleScrollView';
import { createWebCompatibleContainerStyle, createWebCompatibleScrollContentStyle } from '../../utils/webStyles';
import { EventsStackParamList } from '../../navigation/types';
import { Event } from '../../types';
import { eventsService } from '../../services/events';

type EventDetailsScreenRouteProp = RouteProp<EventsStackParamList, 'EventDetails'>;

export default function EventDetailsScreen() {
  const { t } = useTranslation();
  const route = useRoute<EventDetailsScreenRouteProp>();
  const { eventId } = route.params;

  const [event, setEvent] = useState<Event | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadEvent();
  }, [eventId]);

  const loadEvent = async () => {
    try {
      setLoading(true);
      console.log('🔄 Loading event details for ID:', eventId);
      const eventData = await eventsService.getEventById(eventId);

      if (eventData) {
        setEvent(eventData);
        console.log('✅ Event loaded successfully:', eventData.title);
      } else {
        Alert.alert('שגיאה', 'האירוע לא נמצא');
      }
    } catch (error) {
      console.error('❌ Error loading event:', error);
      Alert.alert('שגיאה', 'שגיאה בטעינת פרטי האירוע');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('he-IL', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <Header
          title={t('events.eventDetails')}
          showBackButton={true}
        />
        <View style={styles.centerContainer}>
          <Text style={styles.loadingText}>טוען פרטי אירוע...</Text>
        </View>
      </View>
    );
  }

  if (!event) {
    return (
      <View style={styles.container}>
        <Header
          title={t('events.eventDetails')}
          showBackButton={true}
        />
        <View style={styles.centerContainer}>
          <Text style={styles.errorText}>האירוע לא נמצא</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Header
        title={event.title}
        showBackButton={true}
      />
      <WebCompatibleScrollView style={styles.content} contentContainerStyle={styles.scrollContent}>
        {/* Event Hero Section */}
        <View style={styles.eventHero}>
          <View style={styles.eventDateLarge}>
            <Text style={styles.eventDayLarge}>{formatDate(event.date).split(' ')[0]}</Text>
            <Text style={styles.eventMonthLarge}>{formatDate(event.date).split(' ')[1]}</Text>
          </View>
          <Text style={styles.eventTitle}>{event.title}</Text>
          <Text style={styles.eventSubtitle}>
            יוצר: {event.createdBy} • {event.type === 'community' ? 'אירוע קהילתי' : 'אירוע דיירים'}
          </Text>
          {/* Display the selected icon prominently */}
          <View style={styles.eventIconDisplay}>
            <Text style={styles.eventIconLarge}>{event.icon || '📅'}</Text>
          </View>
        </View>

        {/* Event Details */}
        <View style={styles.eventDetails}>
          <View style={styles.detailItem}>
            <View style={styles.detailIcon}>
              <Text style={styles.detailIconText}>🕐</Text>
            </View>
            <View style={styles.detailText}>
              <Text style={styles.detailTitle}>זמן ומשך</Text>
              <Text style={styles.detailDescription}>{formatDate(event.date)}</Text>
            </View>
          </View>

          <View style={styles.detailItem}>
            <View style={styles.detailIcon}>
              <Text style={styles.detailIconText}>📍</Text>
            </View>
            <View style={styles.detailText}>
              <Text style={styles.detailTitle}>מיקום</Text>
              <Text style={styles.detailDescription}>{event.location || 'מיקום לא צוין'}</Text>
            </View>
          </View>

          <View style={styles.detailItem}>
            <View style={styles.detailIcon}>
              <Text style={styles.detailIconText}>👥</Text>
            </View>
            <View style={styles.detailText}>
              <Text style={styles.detailTitle}>מי מגיע</Text>
              <Text style={styles.detailDescription}>יוצר: {event.createdBy}</Text>
            </View>
          </View>
        </View>

        {/* Description */}
        <View style={styles.descriptionContainer}>
          <Text style={[styles.sectionTitle, { textAlign: getTextAlign() }]}>
            תיאור האירוע
          </Text>
          <Text style={[styles.description, { textAlign: getTextAlign() }]}>
            {event.description}
          </Text>
        </View>

        {/* Action Button */}
        <TouchableOpacity style={styles.button}>
          <Text style={[styles.buttonText, { textAlign: getTextAlign() }]}>
            הצטרף לאירוע
          </Text>
        </TouchableOpacity>
      </WebCompatibleScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    ...createWebCompatibleContainerStyle(),
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  content: {
    padding: 20,
  },
  scrollContent: {
    ...createWebCompatibleScrollContentStyle(),
    paddingBottom: 100,
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
  },
  errorText: {
    fontSize: 16,
    color: '#e74c3c',
  },
  eventHero: {
    backgroundColor: '#667eea',
    padding: 30,
    alignItems: 'center',
    marginBottom: 25,
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  eventDateLarge: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    borderRadius: 15,
    padding: 15,
    alignItems: 'center',
    marginBottom: 15,
  },
  eventDayLarge: {
    fontSize: 36,
    fontWeight: 'bold',
    color: 'white',
    lineHeight: 36,
  },
  eventMonthLarge: {
    fontSize: 14,
    color: 'white',
    textTransform: 'uppercase',
    marginTop: 5,
    fontWeight: 'bold',
  },
  eventTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 8,
    textAlign: 'center',
  },
  eventSubtitle: {
    fontSize: 14,
    color: 'white',
    opacity: 0.9,
    textAlign: 'center',
    marginBottom: 20,
  },
  eventIconDisplay: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    borderRadius: 20,
    padding: 15,
    alignItems: 'center',
    justifyContent: 'center',
  },
  eventIconLarge: {
    fontSize: 48,
  },
  eventDetails: {
    padding: 25,
    backgroundColor: 'white',
    borderRadius: 15,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  detailIcon: {
    width: 40,
    height: 40,
    borderRadius: 10,
    backgroundColor: '#f8f9fa',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 15,
  },
  detailIconText: {
    fontSize: 18,
  },
  detailText: {
    flex: 1,
  },
  detailTitle: {
    color: '#2c3e50',
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
    textAlign: 'right',
  },
  detailDescription: {
    color: '#7f8c8d',
    fontSize: 13,
    lineHeight: 18,
    textAlign: 'right',
  },
  descriptionContainer: {
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 20,
    marginBottom: 30,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  description: {
    fontSize: 16,
    color: '#666',
    lineHeight: 24,
  },
  button: {
    backgroundColor: '#667eea',
    paddingVertical: 15,
    paddingHorizontal: 30,
    borderRadius: 25,
    alignItems: 'center',
    marginTop: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});
