---
description: 
globs: 
alwaysApply: false
---
openapi: 3.0.0
info:
  title: KibbutzConnect Application API and Data Schemas
  version: v1.0.0
  description: |
    This document describes the server-side APIs available in the KibbutzConnect application and the data schemas used in its Google Cloud Firestore database.

    **Server-Side APIs (Server Actions):**
    The application utilizes Next.js Server Actions for specific backend logic, such as AI-powered operations. An example is the `summarizePostsAction`.

    **Data Operations (Client-Side via Firebase SDK):**
    Most data management operations (CRUD - Create, Read, Update, Delete) for core features
    like Events, Food Items, Jobs, Chat Messages, Surveys, User profiles, and Deals are handled
    **client-side directly via the Firebase SDK interacting with Google Cloud Firestore**.
    These operations utilize the Firebase client libraries and are subject to Firestore's
    security rules. As such, they are not listed as traditional HTTP endpoints in this
    OpenAPI specification but their data schemas are documented below for reference.
    Refer to the Firebase Firestore documentation for details on its API and capabilities.
    
components:
  schemas:
    ChatGroup:
      description: Stores information about chat groups. Document ID is auto-generated.
      properties:
        createdAt:
          description: Firestore Timestamp when the group was created.
          format: date-time
          type: string
        createdBy:
          description: User ID of the group creator (references `users` collection).
          type: string
        dataAiHint:
          description: Keywords for AI placeholder icon. Optional.
          type: string
        iconUrl:
          description: URL for the group's icon. Optional.
          format: url
          type: string
        lastMessage:
          description: Text of the last message sent in the group. Optional.
          type: string
        lastMessageTimestamp:
          description: Firestore Timestamp of the last message. Optional.
          format: date-time
          type: string
        members:
          description: List of User IDs who are members of this group.
          items:
            type: string
          type: array
        name:
          description: Name of the chat group.
          type: string
      required:
      - name
      - createdAt
      - createdBy
      - members
      type: object
    ChatMessage:
      description: Stores individual messages for chat groups. Document ID is auto-generated.
      properties:
        avatarDataAiHint:
          description: Keywords for AI placeholder avatar. Optional.
          type: string
        avatarUrl:
          description: URL or placeholder string for the sender's avatar image.
          type: string
        groupId:
          description: ID of the chat group this message belongs to (references `chatGroups`
            collection).
          type: string
        senderId:
          description: User ID of the message sender (references `users` collection).
          type: string
        senderName:
          description: Name of the message sender.
          type: string
        text:
          description: Content of the message.
          type: string
        timestamp:
          description: Firestore Timestamp when the message was sent.
          format: date-time
          type: string
      required:
      - groupId
      - senderId
      - senderName
      - text
      - timestamp
      - avatarUrl
      type: object
    Deal:
      description: Stores information about group purchase initiatives. Document ID
        is auto-generated.
      properties:
        category:
          description: Category of the deal (e.g., 'תקשורת', 'חינוך').
          type: string
        createdAt:
          description: Firestore Timestamp when the deal was created.
          format: date-time
          type: string
        currentSize:
          description: The current number of participants who have joined.
          type: integer
        dataAiHint:
          description: Keywords for AI placeholder image. Optional.
          type: string
        description:
          description: Detailed description of the purchase initiative.
          type: string
        imageUrl:
          description: URL for an image related to the deal. Optional.
          format: url
          type: string
        name:
          description: Name or title of the purchase group.
          type: string
        organizerId:
          description: User ID of the person/group organizing the deal (references
            `users` collection).
          type: string
        organizerName:
          description: Display name of the organizer.
          type: string
        participants:
          description: List of User IDs who have joined the deal. Optional.
          items:
            type: string
          type: array
        status:
          description: Status of the deal.
          enum:
          - פעיל
          - הושלם
          - בוטל
          type: string
        targetSize:
          description: The target number of participants needed for the deal.
          type: integer
      required:
      - name
      - category
      - description
      - targetSize
      - currentSize
      - status
      - organizerId
      - organizerName
      - createdAt
      type: object
    Event:
      description: Stores details about community and tenant events. Document ID is
        auto-generated.
      properties:
        attendees:
          description: Estimated or actual number of attendees. Optional.
          type: integer
        createdAt:
          description: Firestore Timestamp of when the event was created.
          format: date-time
          type: string
        dataAiHint:
          description: Keywords for AI placeholder image generation. Optional.
          type: string
        date:
          description: Firestore Timestamp for the date and time of the event.
          format: date-time
          type: string
        description:
          description: A detailed description of the event.
          type: string
        imageUrl:
          description: URL to an image for the event. Optional.
          format: url
          type: string
        location:
          description: The location where the event will take place.
          type: string
        title:
          description: The title of the event.
          type: string
        type:
          description: Type of event.
          enum:
          - community
          - tenant
          type: string
      required:
      - title
      - description
      - date
      - location
      - type
      - createdAt
      type: object
    FoodItem:
      description: Stores information about food items offered for sale. Document
        ID is auto-generated.
      properties:
        contactInfo:
          description: Contact information for the seller.
          type: string
        createdAt:
          description: Firestore Timestamp of when the food item was posted.
          format: date-time
          type: string
        dataAiHint:
          description: Keywords for AI placeholder image generation. Optional.
          type: string
        description:
          description: Description of the food item.
          type: string
        inlinePhotoId:
          description: ID of a document in the `inlinePhotos` collection. Optional.
          type: string
        name:
          description: Name of the food item.
          type: string
        pickupDetails:
          description: Details about where and when to pick up the item. Optional.
          type: string
        price:
          description: Price of the item (e.g., \"₪50\", \"חינם\").
          type: string
        sellerId:
          description: User ID of the seller (references `users` collection).
          type: string
        sellerName:
          description: Name of the user selling the item.
          type: string
      required:
      - name
      - description
      - price
      - sellerName
      - sellerId
      - contactInfo
      - createdAt
      type: object
    InlinePhoto:
      description: Stores images as Base64 encoded strings. Document ID is auto-generated.
      properties:
        base64Data:
          description: The image file encoded as a Base64 data URI (e.g., 'data:image/jpeg;base64,...').
          type: string
        createdAt:
          description: Firestore Timestamp of when the photo was uploaded.
          format: date-time
          type: string
        fileName:
          description: Original name of the uploaded file.
          type: string
        mimeType:
          description: MIME type of the uploaded file (e.g., 'image/jpeg').
          type: string
        uploaderId:
          description: User ID of the person who uploaded the photo (references `users`
            collection).
          type: string
      required:
      - base64Data
      - fileName
      - mimeType
      - uploaderId
      - createdAt
      type: object
    Job:
      description: Stores job postings and service offers. Document ID is auto-generated.
      properties:
        agePreference:
          description: Preferred age range for candidates. Optional.
          type: string
        createdAt:
          description: Firestore Timestamp of when the posting was created.
          format: date-time
          type: string
        dataAiHint:
          description: Keywords for AI placeholder image. Optional.
          type: string
        dateTimeInfo:
          description: Information about timing, schedule, or deadlines.
          type: string
        description:
          description: Detailed description.
          type: string
        inlinePhotoId:
          description: ID of a document in `inlinePhotos` for an associated image.
            Optional.
          type: string
        jobType:
          description: Type of posting.
          enum:
          - offer
          - request
          type: string
        location:
          description: Location relevant to the job/service. Optional.
          type: string
        postedById:
          description: User ID of the poster (references `users` collection).
          type: string
        postedByName:
          description: Name of the poster.
          type: string
        skills:
          description: List of required or offered skills.
          items:
            type: string
          type: array
        status:
          description: Current status.
          enum:
          - פתוח
          - בתהליך
          - הושלם
          type: string
        title:
          description: Title of the job or service.
          type: string
      required:
      - title
      - description
      - dateTimeInfo
      - skills
      - status
      - jobType
      - postedById
      - postedByName
      - createdAt
      type: object
    SummarizePostsInput:
      properties:
        postContent:
          description: The text content of the posts to be summarized.
          example: This is a very long text about the community's latest activities...
          type: string
      required:
      - postContent
      type: object
    SummarizePostsOutput:
      properties:
        summary:
          description: A concise summary of the provided post content.
          example: The community's recent activities include a successful festival...
          type: string
      required:
      - summary
      type: object
    Survey:
      description: Stores community surveys. Document ID is auto-generated.
      properties:
        closingDate:
          description: Firestore Timestamp for when the survey automatically closes.
            Optional.
          format: date-time
          type: string
        createdAt:
          description: Firestore Timestamp when the survey was created.
          format: date-time
          type: string
        createdBy:
          description: User ID of the admin who created the survey (references `users`
            collection).
          type: string
        dataAiHint:
          description: Keywords for AI placeholder image. Optional.
          type: string
        description:
          description: Description or purpose of the survey.
          type: string
        imageUrl:
          description: URL for an image associated with the survey. Optional.
          format: url
          type: string
        participantsCount:
          description: Number of users who have participated. Optional.
          type: integer
        questions:
          description: List of questions in the survey.
          items:
            $ref: '#/components/schemas/SurveyQuestion'
          type: array
        status:
          description: Current status of the survey.
          enum:
          - פתוח
          - סגור
          type: string
        title:
          description: Title of the survey.
          type: string
      required:
      - title
      - description
      - questions
      - status
      - createdBy
      - createdAt
      type: object
    SurveyQuestion:
      description: Defines a single question within a survey.
      properties:
        id:
          description: Unique identifier for the question within the survey.
          type: string
        options:
          description: Answer choices for 'single-choice' or 'multiple-choice' questions.
            Optional.
          items:
            type: string
          type: array
        text:
          description: The question text.
          type: string
        type:
          description: Type of question.
          enum:
          - text
          - single-choice
          - multiple-choice
          type: string
      required:
      - id
      - text
      - type
      type: object
    User:
      description: Stores information about registered users. Document ID is the User's
        unique ID.
      properties:
        createdAt:
          description: Firestore Timestamp of when the user account was created.
          format: date-time
          type: string
        dob:
          description: Date of birth (YYYY-MM-DD), primarily for 'Kid' role. Optional.
          format: date
          type: string
        email:
          description: Email address of the user (unique).
          format: email
          type: string
        id:
          description: The user's unique ID.
          type: string
        name:
          description: Full name of the user.
          type: string
        role:
          description: User role.
          enum:
          - Admin
          - Adult
          - Kid
          type: string
      required:
      - id
      - name
      - email
      - role
      - createdAt
      type: object
info:
  description: 'This document describes the server-side APIs available in the KibbutzConnect
    application and the data schemas used in its Google Cloud Firestore database.


    **Server-Side APIs (Server Actions):**

    The application utilizes Next.js Server Actions for specific backend logic, such
    as AI-powered operations. An example is the `summarizePostsAction`.


    **Data Operations (Client-Side via Firebase SDK):**

    Most data management operations (CRUD - Create, Read, Update, Delete) for core
    features

    like Events, Food Items, Jobs, Chat Messages, Surveys, User profiles, and Deals
    are handled

    **client-side directly via the Firebase SDK interacting with Google Cloud Firestore**.

    These operations utilize the Firebase client libraries and are subject to Firestore''s

    security rules. As such, they are not listed as traditional HTTP endpoints in
    this

    OpenAPI specification but their data schemas are documented below for reference.

    Refer to the Firebase Firestore documentation for details on its API and capabilities.

    '
  title: KibbutzConnect Application API and Data Schemas
  version: v1.0.0
openapi: 3.0.0
paths:
  /chatgroups:
    get:
      operationId: listChatGroups
      responses:
        '200':
          description: A list of ChatGroups
      summary: List all ChatGroups
    post:
      operationId: createChatGroup
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChatGroup'
        required: true
      responses:
        '201':
          description: ChatGroup created
      summary: Create a new ChatGroup
  /chatgroups/{id}:
    delete:
      operationId: deleteChatGroupById
      parameters:
      - in: path
        name: id
        required: true
        schema:
          type: string
      responses:
        '204':
          description: ChatGroup deleted
      summary: Delete a ChatGroup by ID
    get:
      operationId: getChatGroupById
      parameters:
      - in: path
        name: id
        required: true
        schema:
          type: string
      responses:
        '200':
          description: ChatGroup found
      summary: Get a ChatGroup by ID
    put:
      operationId: updateChatGroupById
      parameters:
      - in: path
        name: id
        required: true
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChatGroup'
        required: true
      responses:
        '200':
          description: ChatGroup updated
      summary: Update a ChatGroup by ID
  /chatmessages:
    get:
      operationId: listChatMessages
      responses:
        '200':
          description: A list of ChatMessages
      summary: List all ChatMessages
    post:
      operationId: createChatMessage
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChatMessage'
        required: true
      responses:
        '201':
          description: ChatMessage created
      summary: Create a new ChatMessage
  /chatmessages/{id}:
    delete:
      operationId: deleteChatMessageById
      parameters:
      - in: path
        name: id
        required: true
        schema:
          type: string
      responses:
        '204':
          description: ChatMessage deleted
      summary: Delete a ChatMessage by ID
    get:
      operationId: getChatMessageById
      parameters:
      - in: path
        name: id
        required: true
        schema:
          type: string
      responses:
        '200':
          description: ChatMessage found
      summary: Get a ChatMessage by ID
    put:
      operationId: updateChatMessageById
      parameters:
      - in: path
        name: id
        required: true
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChatMessage'
        required: true
      responses:
        '200':
          description: ChatMessage updated
      summary: Update a ChatMessage by ID
  /deals:
    get:
      operationId: listDeals
      responses:
        '200':
          description: A list of Deals
      summary: List all Deals
    post:
      operationId: createDeal
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Deal'
        required: true
      responses:
        '201':
          description: Deal created
      summary: Create a new Deal
  /deals/{id}:
    delete:
      operationId: deleteDealById
      parameters:
      - in: path
        name: id
        required: true
        schema:
          type: string
      responses:
        '204':
          description: Deal deleted
      summary: Delete a Deal by ID
    get:
      operationId: getDealById
      parameters:
      - in: path
        name: id
        required: true
        schema:
          type: string
      responses:
        '200':
          description: Deal found
      summary: Get a Deal by ID
    put:
      operationId: updateDealById
      parameters:
      - in: path
        name: id
        required: true
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Deal'
        required: true
      responses:
        '200':
          description: Deal updated
      summary: Update a Deal by ID
  /events:
    get:
      operationId: listEvents
      responses:
        '200':
          description: A list of Events
      summary: List all Events
    post:
      operationId: createEvent
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Event'
        required: true
      responses:
        '201':
          description: Event created
      summary: Create a new Event
  /events/{id}:
    delete:
      operationId: deleteEventById
      parameters:
      - in: path
        name: id
        required: true
        schema:
          type: string
      responses:
        '204':
          description: Event deleted
      summary: Delete a Event by ID
    get:
      operationId: getEventById
      parameters:
      - in: path
        name: id
        required: true
        schema:
          type: string
      responses:
        '200':
          description: Event found
      summary: Get a Event by ID
    put:
      operationId: updateEventById
      parameters:
      - in: path
        name: id
        required: true
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Event'
        required: true
      responses:
        '200':
          description: Event updated
      summary: Update a Event by ID
  /fooditems:
    get:
      operationId: listFoodItems
      responses:
        '200':
          description: A list of FoodItems
      summary: List all FoodItems
    post:
      operationId: createFoodItem
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FoodItem'
        required: true
      responses:
        '201':
          description: FoodItem created
      summary: Create a new FoodItem
  /fooditems/{id}:
    delete:
      operationId: deleteFoodItemById
      parameters:
      - in: path
        name: id
        required: true
        schema:
          type: string
      responses:
        '204':
          description: FoodItem deleted
      summary: Delete a FoodItem by ID
    get:
      operationId: getFoodItemById
      parameters:
      - in: path
        name: id
        required: true
        schema:
          type: string
      responses:
        '200':
          description: FoodItem found
      summary: Get a FoodItem by ID
    put:
      operationId: updateFoodItemById
      parameters:
      - in: path
        name: id
        required: true
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FoodItem'
        required: true
      responses:
        '200':
          description: FoodItem updated
      summary: Update a FoodItem by ID
  /jobs:
    get:
      operationId: listJobs
      responses:
        '200':
          description: A list of Jobs
      summary: List all Jobs
    post:
      operationId: createJob
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Job'
        required: true
      responses:
        '201':
          description: Job created
      summary: Create a new Job
  /jobs/{id}:
    delete:
      operationId: deleteJobById
      parameters:
      - in: path
        name: id
        required: true
        schema:
          type: string
      responses:
        '204':
          description: Job deleted
      summary: Delete a Job by ID
    get:
      operationId: getJobById
      parameters:
      - in: path
        name: id
        required: true
        schema:
          type: string
      responses:
        '200':
          description: Job found
      summary: Get a Job by ID
    put:
      operationId: updateJobById
      parameters:
      - in: path
        name: id
        required: true
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Job'
        required: true
      responses:
        '200':
          description: Job updated
      summary: Update a Job by ID
  /surveyquestions:
    get:
      operationId: listSurveyQuestions
      responses:
        '200':
          description: A list of SurveyQuestions
      summary: List all SurveyQuestions
    post:
      operationId: createSurveyQuestion
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SurveyQuestion'
        required: true
      responses:
        '201':
          description: SurveyQuestion created
      summary: Create a new SurveyQuestion
  /surveyquestions/{id}:
    delete:
      operationId: deleteSurveyQuestionById
      parameters:
      - in: path
        name: id
        required: true
        schema:
          type: string
      responses:
        '204':
          description: SurveyQuestion deleted
      summary: Delete a SurveyQuestion by ID
    get:
      operationId: getSurveyQuestionById
      parameters:
      - in: path
        name: id
        required: true
        schema:
          type: string
      responses:
        '200':
          description: SurveyQuestion found
      summary: Get a SurveyQuestion by ID
    put:
      operationId: updateSurveyQuestionById
      parameters:
      - in: path
        name: id
        required: true
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SurveyQuestion'
        required: true
      responses:
        '200':
          description: SurveyQuestion updated
      summary: Update a SurveyQuestion by ID
  /surveys:
    get:
      operationId: listSurveys
      responses:
        '200':
          description: A list of Surveys
      summary: List all Surveys
    post:
      operationId: createSurvey
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Survey'
        required: true
      responses:
        '201':
          description: Survey created
      summary: Create a new Survey
  /surveys/{id}:
    delete:
      operationId: deleteSurveyById
      parameters:
      - in: path
        name: id
        required: true
        schema:
          type: string
      responses:
        '204':
          description: Survey deleted
      summary: Delete a Survey by ID
    get:
      operationId: getSurveyById
      parameters:
      - in: path
        name: id
        required: true
        schema:
          type: string
      responses:
        '200':
          description: Survey found
      summary: Get a Survey by ID
    put:
      operationId: updateSurveyById
      parameters:
      - in: path
        name: id
        required: true
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Survey'
        required: true
      responses:
        '200':
          description: Survey updated
      summary: Update a Survey by ID
  /users:
    get:
      operationId: listUsers
      responses:
        '200':
          description: A list of Users
      summary: List all Users
    post:
      operationId: createUser
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/User'
        required: true
      responses:
        '201':
          description: User created
      summary: Create a new User
  /users/{id}:
    delete:
      operationId: deleteUserById
      parameters:
      - in: path
        name: id
        required: true
        schema:
          type: string
      responses:
        '204':
          description: User deleted
      summary: Delete a User by ID
    get:
      operationId: getUserById
      parameters:
      - in: path
        name: id
        required: true
        schema:
          type: string
      responses:
        '200':
          description: User found
      summary: Get a User by ID
    put:
      operationId: updateUserById
      parameters:
      - in: path
        name: id
        required: true
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/User'
        required: true
      responses:
        '200':
          description: User updated
      summary: Update a User by ID
servers: null

