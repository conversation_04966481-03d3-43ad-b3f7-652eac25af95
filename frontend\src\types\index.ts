// UI Preferences types
export interface UIPreferences {
  theme: 'light' | 'dark' | 'auto';
  colorScheme: 'default' | 'blue' | 'green' | 'purple' | 'orange';
  fontSize: 'small' | 'medium' | 'large' | 'extraLarge';
  spacing: 'compact' | 'medium' | 'comfortable';
  highContrast: boolean;
}

// User types
export interface User {
  id: string;
  community_id: string;
  email: string;
  first_name?: string;
  last_name?: string;
  phone?: string;
  role: 'ADMIN' | 'MODERATOR' | 'USER';
  user_type: 'ADULT' | 'YOUTH' | 'CHILD' | 'EXTERNAL';
  language: string;
  is_active: boolean;
  last_login?: Date;
  google_id?: string;
  ui_preferences?: UIPreferences;
  created_at: Date;
  updated_at: Date;
}

export interface NotificationPreferences {
  events: boolean;
  chat: boolean;
  jobs: boolean;
  surveys: boolean;
  commercialUnions: boolean;
}

// Event types
export interface Event {
  id: string;
  title: string;
  description: string;
  type?: 'community' | 'tenant';
  icon?: string;
  createdBy: string;
  date: Date;
  location: string;
  attachments?: string[];
  rsvp: RSVP[];
  comments: Comment[];
  reactions: Reaction[];
  createdAt: Date;
}

export interface RSVP {
  userId: string;
  status: 'attending' | 'not_attending';
  createdAt: Date;
}

export interface Comment {
  id: string;
  userId: string;
  text: string;
  createdAt: Date;
}

export interface Reaction {
  userId: string;
  type: string;
  createdAt: Date;
}

// Job types
export interface Job {
  id: string;
  title: string;
  description: string;
  location?: string;
  dateTimeInfo: string;
  agePreference?: string;
  skills: string[];
  status: 'open' | 'in_progress' | 'completed';
  jobType: 'offer' | 'request';
  postedById: string;
  postedByName: string;
  inlinePhotoId?: string;
  dataAiHint?: string;
  createdAt: Date;
  applications: JobApplication[];
}

export interface JobApplication {
  userId: string;
  appliedAt: Date;
  message?: string;
}

// Chat types
export interface ChatRoom {
  id: string;
  name: string;
  type: 'group' | 'direct';
  participants: string[];
  createdBy: string;
  createdAt: Date;
  lastMessage?: Message;
}

export interface Message {
  id: string;
  chatRoomId: string;
  senderId: string;
  text: string;
  type: 'text' | 'image' | 'file';
  createdAt: Date;
}

// Commercial Union types
export interface CommercialUnion {
  id: string;
  name: string;
  category: string;
  description: string;
  targetSize: number;
  currentSize: number;
  createdBy: string;
  members: string[];
  createdAt: Date;
  status: 'active' | 'completed' | 'cancelled';
}

// Survey types
export interface Survey {
  id: string;
  title: string;
  description: string;
  questions: SurveyQuestion[];
  createdBy: string;
  createdAt: Date;
  openDate: Date;
  closeDate: Date;
  isAnonymous: boolean;
  responses: SurveyResponse[];
}

export interface SurveyQuestion {
  id: string;
  text: string;
  type: 'multiple_choice' | 'yes_no' | 'rating' | 'text';
  options?: string[];
  required: boolean;
}

export interface SurveyResponse {
  userId: string;
  answers: { [questionId: string]: any };
  submittedAt: Date;
}
