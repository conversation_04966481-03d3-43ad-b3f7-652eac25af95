const { getDefaultConfig } = require('expo/metro-config');
const { resolver: { sourceExts, assetExts } } = getDefaultConfig(__dirname);

const config = getDefaultConfig(__dirname);

// Configure for web compatibility
config.transformer = {
  ...config.transformer,
  babelTransformerPath: require.resolve('react-native-svg-transformer'),
  assetPlugins: ['expo-asset/tools/hashAssetFiles'],
  getTransformOptions: async () => ({
    transform: {
      experimentalImportSupport: false,
      inlineRequires: true,
    },
  }),
};

config.resolver = {
  ...config.resolver,
  assetExts: assetExts.filter(ext => ext !== 'svg'),
  sourceExts: [...sourceExts, 'svg'],
  platforms: ['ios', 'android', 'native', 'web'],
};

config.server = {
  enhanceMiddleware: (middleware) => {
    return (req, res, next) => {
      // Fix MIME type for web bundle
      if (req.url.endsWith('.bundle')) {
        res.setHeader('Content-Type', 'application/javascript');
      }
      return middleware(req, res, next);
    };
  },
  // Enable hot reload
  port: 8081,
  reloadOnChange: true,
};

module.exports = config;
