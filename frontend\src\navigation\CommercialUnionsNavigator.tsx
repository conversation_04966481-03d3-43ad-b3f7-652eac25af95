import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { CommercialUnionsStackParamList } from './types';
import UnionsListScreen from '../screens/commercialUnions/UnionsListScreen';
import UnionDetailsScreen from '../screens/commercialUnions/UnionDetailsScreen';
import CreateUnionScreen from '../screens/commercialUnions/CreateUnionScreen';
import MyUnionsScreen from '../screens/commercialUnions/MyUnionsScreen';

const Stack = createStackNavigator<CommercialUnionsStackParamList>();

export default function CommercialUnionsNavigator() {
  return (
    <Stack.Navigator
      initialRouteName="UnionsList"
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen name="UnionsList" component={UnionsListScreen} />
      <Stack.Screen name="UnionDetails" component={UnionDetailsScreen} />
      <Stack.Screen name="CreateUnion" component={CreateUnionScreen} />
      <Stack.Screen name="MyUnions" component={MyUnionsScreen} />
    </Stack.Navigator>
  );
}
