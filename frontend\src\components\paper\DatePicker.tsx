import React from 'react';
import { StyleSheet, Platform } from 'react-native';
import { Button, Portal, Modal, useTheme } from 'react-native-paper';
import DateTimePicker from '@react-native-community/datetimepicker';
import { rtlConfig } from '../../theme/rtl';

interface DatePickerProps {
  label: string;
  value: Date;
  onChange: (date: Date) => void;
  error?: string;
  style?: any;
}

export const DatePicker = ({
  label,
  value,
  onChange,
  error,
  style,
}: DatePickerProps) => {
  const theme = useTheme();
  const [visible, setVisible] = React.useState(false);
  const [date, setDate] = React.useState(value);

  const showModal = () => setVisible(true);
  const hideModal = () => setVisible(false);

  const handleChange = (event: any, selectedDate?: Date) => {
    if (Platform.OS === 'android') {
      setVisible(false);
    }
    if (selectedDate) {
      setDate(selectedDate);
      onChange(selectedDate);
    }
  };

  return (
    <>
      <Button
        mode="outlined"
        onPress={showModal}
        style={[styles.button, style]}
        textColor={error ? theme.colors.error : theme.colors.onSurface}
      >
        {label}: {date.toLocaleDateString()}
      </Button>

      {Platform.OS === 'ios' ? (
        <Portal>
          <Modal
            visible={visible}
            onDismiss={hideModal}
            contentContainerStyle={[
              styles.modal,
              { backgroundColor: theme.colors.surface },
            ]}
          >
            <DateTimePicker
              value={date}
              mode="date"
              display="spinner"
              onChange={handleChange}
              style={styles.picker}
            />
            <Button onPress={hideModal}>Done</Button>
          </Modal>
        </Portal>
      ) : (
        visible && (
          <DateTimePicker
            value={date}
            mode="date"
            display="default"
            onChange={handleChange}
          />
        )
      )}
    </>
  );
};

const styles = StyleSheet.create({
  button: {
    marginVertical: 8,
  },
  modal: {
    padding: 20,
    margin: 20,
    borderRadius: 8,
  },
  picker: {
    width: '100%',
  },
}); 