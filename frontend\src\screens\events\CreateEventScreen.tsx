import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { EventsStackParamList } from '../../navigation/types';
import { getTextAlign } from '../../utils/rtl';
import { eventsService } from '../../services/events';
import Header from '../../components/Header';
import { IconPicker } from '../../components/IconPicker';
import { SimpleCalendar } from '../../components/SimpleCalendar';
import { CrossPlatformTimePicker } from '../../components/CrossPlatformCalendar';

type CreateEventScreenNavigationProp = StackNavigationProp<EventsStackParamList, 'CreateEvent'>;

export default function CreateEventScreen() {
  const { t } = useTranslation();
  const navigation = useNavigation<CreateEventScreenNavigationProp>();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    location: '',
    date: '',
    time: '',
    type: 'tenant' as 'community' | 'tenant',
    category: 'Social Gathering',
    ageGroup: 'All Ages',
    icon: '📅', // Default calendar icon
  });

  // Date picker state
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [showDatePicker, setShowDatePicker] = useState(false);

  // Time picker state
  const [selectedTime, setSelectedTime] = useState<Date | null>(null);
  const [showTimePicker, setShowTimePicker] = useState(false);

  // Track which fields have been touched for validation
  const [touchedFields, setTouchedFields] = useState({
    title: false,
    description: false,
    location: false,
    date: false,
    time: false,
  });

  // Check if a field is invalid (empty and touched)
  const isFieldInvalid = (fieldName: keyof typeof touchedFields) => {
    return touchedFields[fieldName] && !formData[fieldName];
  };

  // Check if form is valid
  const isFormValid = () => {
    return formData.title && formData.description && formData.location && formData.date && formData.time;
  };

  // Format date for display
  const formatDateForDisplay = (date: Date) => {
    return date.toLocaleDateString('he-IL', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  };

  // Format time for display
  const formatTimeForDisplay = (date: Date) => {
    return date.toLocaleTimeString('he-IL', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
  };

  // Handle date selection
  const handleDateSelect = (selectedDate: Date) => {
    setSelectedDate(selectedDate);
    const formattedDate = selectedDate.toISOString().split('T')[0]; // YYYY-MM-DD format
    setFormData({ ...formData, date: formattedDate });
    setTouchedFields({ ...touchedFields, date: true });
    setShowDatePicker(false);
  };

  // Handle time selection
  const handleTimeSelect = (selectedTime: Date) => {
    setSelectedTime(selectedTime);
    const formattedTime = selectedTime.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
    setFormData({ ...formData, time: formattedTime });
    setTouchedFields({ ...touchedFields, time: true });
    setShowTimePicker(false);
  };

  const handleCreateEvent = async () => {
    console.log('🔄 handleCreateEvent called');
    console.log('🔄 Form data:', formData);

    if (!formData.title || !formData.description || !formData.date || !formData.time || !formData.location) {
      console.log('❌ Missing required fields');
      Alert.alert('שגיאה', 'אנא מלא את כל השדות הנדרשים (כותרת, תיאור, תאריך, שעה, מיקום)');
      return;
    }

    console.log('✅ Validation passed, creating event...');
    setLoading(true);
    try {
      console.log('🔄 Creating event:', formData);

      // Create event data - combine date and time
      let eventDate = new Date();
      if (formData.date && formData.time) {
        const dateTimeString = `${formData.date}T${formData.time}:00`;
        eventDate = new Date(dateTimeString);
      } else if (formData.date) {
        eventDate = new Date(formData.date);
      }

      const eventData = {
        title: formData.title,
        description: formData.description,
        location: formData.location || '',
        date: eventDate,
        type: formData.type,
        icon: formData.icon,
        category: formData.category,
        ageGroup: formData.ageGroup,
      };

      const newEvent = await eventsService.createEvent(eventData);
      console.log('✅ Event created successfully:', newEvent);

      Alert.alert(
        'הצלחה',
        `האירוע "${newEvent.title}" נוצר בהצלחה! 🎉`,
        [
          {
            text: 'אישור',
            onPress: () => navigation.goBack()
          }
        ]
      );
    } catch (error) {
      console.error('❌ Error creating event:', error);
      Alert.alert(
        'שגיאה',
        'שגיאה ביצירת האירוע. אנא נסה שוב.'
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <Header
        title={t('events.createEvent')}
        showBackButton={true}
      />
      <ScrollView style={styles.content}>
        <Text style={[styles.title, { textAlign: getTextAlign() }]}>
          {t('events.createEvent')}
        </Text>

        <View style={styles.form}>
          {/* Event Title */}
          <View style={styles.formGroup}>
            <Text style={[styles.formLabel, isFieldInvalid('title') && styles.formLabelError]}>
              כותרת האירוע *
            </Text>
            <TextInput
              style={[
                styles.formInput,
                { textAlign: getTextAlign() },
                isFieldInvalid('title') && styles.formInputError
              ]}
              placeholder="לדוגמה: מסיבת ברביקיו קיץ"
              value={formData.title}
              onChangeText={(text) => setFormData({ ...formData, title: text })}
              onBlur={() => setTouchedFields({ ...touchedFields, title: true })}
            />
            {isFieldInvalid('title') && (
              <Text style={styles.errorText}>שדה חובה</Text>
            )}
          </View>

          {/* Description */}
          <View style={styles.formGroup}>
            <Text style={[styles.formLabel, isFieldInvalid('description') && styles.formLabelError]}>
              תיאור *
            </Text>
            <TextInput
              style={[
                styles.formTextarea,
                { textAlign: getTextAlign() },
                isFieldInvalid('description') && styles.formInputError
              ]}
              placeholder="ספר לכולם על האירוע שלך..."
              value={formData.description}
              onChangeText={(text) => setFormData({ ...formData, description: text })}
              onBlur={() => setTouchedFields({ ...touchedFields, description: true })}
              multiline
              numberOfLines={4}
            />
            {isFieldInvalid('description') && (
              <Text style={styles.errorText}>שדה חובה</Text>
            )}
          </View>

          {/* Date and Time Row */}
          <View style={styles.formRow}>
            <View style={styles.formGroup}>
              <Text style={[styles.formLabel, isFieldInvalid('date') && styles.formLabelError]}>
                תאריך *
              </Text>
              <TouchableOpacity
                style={[
                  styles.datePickerButton,
                  isFieldInvalid('date') && styles.formInputError
                ]}
                onPress={() => setShowDatePicker(true)}
              >
                <Text style={styles.calendarIcon}>📅</Text>
                <Text style={[
                  styles.datePickerText,
                  !formData.date && styles.placeholderText
                ]}>
                  {selectedDate ? formatDateForDisplay(selectedDate) : 'בחר תאריך'}
                </Text>
              </TouchableOpacity>
              {isFieldInvalid('date') && (
                <Text style={styles.errorText}>שדה חובה</Text>
              )}
            </View>
            <View style={styles.formGroup}>
              <Text style={[styles.formLabel, isFieldInvalid('time') && styles.formLabelError]}>
                שעה *
              </Text>
              <TouchableOpacity
                style={[
                  styles.timePickerButton,
                  isFieldInvalid('time') && styles.formInputError
                ]}
                onPress={() => setShowTimePicker(true)}
              >
                <Text style={styles.clockIcon}>🕐</Text>
                <Text style={[
                  styles.timePickerText,
                  !formData.time && styles.placeholderText
                ]}>
                  {selectedTime ? formatTimeForDisplay(selectedTime) : 'בחר שעה'}
                </Text>
              </TouchableOpacity>
              {isFieldInvalid('time') && (
                <Text style={styles.errorText}>שדה חובה</Text>
              )}
            </View>
          </View>

          {/* Location */}
          <View style={styles.formGroup}>
            <Text style={[styles.formLabel, isFieldInvalid('location') && styles.formLabelError]}>
              מיקום *
            </Text>
            <TextInput
              style={[
                styles.formInput,
                { textAlign: getTextAlign() },
                isFieldInvalid('location') && styles.formInputError
              ]}
              placeholder="לדוגמה: פארק מרכזי, מרכז קהילתי"
              value={formData.location}
              onChangeText={(text) => setFormData({ ...formData, location: text })}
              onBlur={() => setTouchedFields({ ...touchedFields, location: true })}
            />
            {isFieldInvalid('location') && (
              <Text style={styles.errorText}>שדה חובה</Text>
            )}
          </View>

          {/* Category */}
          <View style={styles.formGroup}>
            <Text style={styles.formLabel}>קטגוריה</Text>
            <View style={styles.selectContainer}>
              <Text style={styles.selectText}>{formData.category}</Text>
            </View>
          </View>

          {/* Age Group */}
          <View style={styles.formGroup}>
            <Text style={styles.formLabel}>קבוצת גיל</Text>
            <View style={styles.selectContainer}>
              <Text style={styles.selectText}>{formData.ageGroup}</Text>
            </View>
          </View>

          {/* Icon Selection */}
          <IconPicker
            selectedIcon={formData.icon}
            onIconSelect={(icon) => setFormData({ ...formData, icon })}
            label="אייקון האירוע"
          />

          <View style={styles.typeContainer}>
            <Text style={[styles.typeLabel, { textAlign: getTextAlign() }]}>
              {t('events.eventType')}:
            </Text>
            <View style={styles.typeButtons}>
              <TouchableOpacity
                style={[
                  styles.typeButton,
                  formData.type === 'community' && styles.typeButtonActive
                ]}
                onPress={() => setFormData({ ...formData, type: 'community' })}
              >
                <Text style={[
                  styles.typeButtonText,
                  formData.type === 'community' && styles.typeButtonTextActive
                ]}>
                  {t('events.communityEvent')}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.typeButton,
                  formData.type === 'tenant' && styles.typeButtonActive
                ]}
                onPress={() => setFormData({ ...formData, type: 'tenant' })}
              >
                <Text style={[
                  styles.typeButtonText,
                  formData.type === 'tenant' && styles.typeButtonTextActive
                ]}>
                  {t('events.tenantEvent')}
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          <TouchableOpacity
            style={[
              styles.createButton,
              (loading || !isFormValid()) && styles.createButtonDisabled
            ]}
            onPress={handleCreateEvent}
            disabled={loading || !isFormValid()}
          >
            <Text style={styles.createButtonText}>
              {loading ? 'יוצר אירוע...' : 'צור אירוע'}
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>

      {/* Simple Calendar Date Picker */}
      <SimpleCalendar
        visible={showDatePicker}
        onClose={() => setShowDatePicker(false)}
        onDateSelect={handleDateSelect}
        selectedDate={selectedDate || undefined}
        minimumDate={new Date()}
        title="בחר תאריך"
      />

      {/* Cross-Platform Time Picker */}
      <CrossPlatformTimePicker
        visible={showTimePicker}
        onClose={() => setShowTimePicker(false)}
        onTimeSelect={handleTimeSelect}
        selectedTime={selectedTime || undefined}
        title="בחר שעה"
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f7fa',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    color: '#333',
  },
  form: {
    width: '100%',
  },
  formGroup: {
    marginBottom: 20,
    flex: 1, // Ensure equal space in form rows
  },
  formLabel: {
    color: '#2c3e50',
    fontWeight: '600',
    marginBottom: 8,
    fontSize: 14,
    textAlign: 'right',
  },
  formInput: {
    width: '100%',
    padding: 12,
    borderWidth: 2,
    borderColor: '#ecf0f1',
    borderRadius: 10,
    fontSize: 14,
    color: '#2c3e50',
    backgroundColor: 'white',
    textAlign: 'right',
  },
  formTextarea: {
    width: '100%',
    padding: 12,
    borderWidth: 2,
    borderColor: '#ecf0f1',
    borderRadius: 10,
    fontSize: 14,
    color: '#2c3e50',
    backgroundColor: 'white',
    minHeight: 80,
    textAlignVertical: 'top',
    textAlign: 'right',
  },
  formRow: {
    flexDirection: 'row-reverse', // RTL layout: time on left, date on right
    gap: 15,
  },
  selectContainer: {
    width: '100%',
    padding: 12,
    borderWidth: 2,
    borderColor: '#ecf0f1',
    borderRadius: 10,
    backgroundColor: 'white',
  },
  selectText: {
    fontSize: 14,
    color: '#2c3e50',
    textAlign: 'right',
  },
  formLabelError: {
    color: '#e74c3c',
  },
  formInputError: {
    borderColor: '#e74c3c',
    borderWidth: 2,
  },
  errorText: {
    color: '#e74c3c',
    fontSize: 12,
    marginTop: 4,
    textAlign: 'right',
  },
  datePickerButton: {
    width: '100%',
    padding: 12,
    borderWidth: 2,
    borderColor: '#ecf0f1',
    borderRadius: 10,
    backgroundColor: 'white',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  datePickerText: {
    fontSize: 14,
    color: '#2c3e50',
    textAlign: 'right',
    flex: 1,
    marginLeft: 8, // Add space between icon and text
  },
  placeholderText: {
    color: '#999',
  },
  calendarIcon: {
    fontSize: 16,
  },
  timePickerButton: {
    width: '100%',
    padding: 12,
    borderWidth: 2,
    borderColor: '#ecf0f1',
    borderRadius: 10,
    backgroundColor: 'white',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  timePickerText: {
    fontSize: 14,
    color: '#2c3e50',
    textAlign: 'right',
    flex: 1,
    marginLeft: 8, // Add space between icon and text
  },
  clockIcon: {
    fontSize: 16,
  },
  typeContainer: {
    marginBottom: 20,
  },
  typeLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  typeButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  typeButton: {
    flex: 1,
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 15,
    marginHorizontal: 5,
    borderWidth: 2,
    borderColor: '#ddd',
    alignItems: 'center',
  },
  typeButtonActive: {
    borderColor: '#007AFF',
    backgroundColor: '#f0f8ff',
  },
  typeButtonText: {
    fontSize: 14,
    color: '#666',
    fontWeight: 'bold',
  },
  typeButtonTextActive: {
    color: '#007AFF',
  },
  createButton: {
    backgroundColor: '#667eea',
    borderRadius: 12,
    padding: 15,
    alignItems: 'center',
    marginTop: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  createButtonDisabled: {
    backgroundColor: '#ccc',
    opacity: 0.6,
  },
  createButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },

});
