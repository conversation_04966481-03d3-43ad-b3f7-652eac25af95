import { Router } from 'express';
import { authenticateToken } from '../../middleware/auth';
import { UserController } from './controller';
import { getModuleLogger } from '../../utils/logger';

const logger = getModuleLogger('User');
const router = Router();
const controller = new UserController();

// Get user count (no auth required for registration flow)
router.get('/count', async (req, res) => {
  try {
    logger.info('Fetching user count');
    await controller.getUserCount(req, res);
    logger.info('Successfully fetched user count');
  } catch (error) {
    logger.error(`Error in get_user_count: ${error}`);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch user count'
    });
  }
});

// Get all users
router.get('/', authenticateToken, async (req, res) => {
  try {
    logger.info('Fetching all users');
    await controller.getAllUsers(req, res);
    logger.info('Successfully fetched all users');
  } catch (error) {
    logger.error(`Error in get_all_users: ${error}`);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch users'
    });
  }
});

// Get users by type or role (for debug randomization)
router.get('/by-type-or-role', authenticateToken, async (req, res) => {
  try {
    logger.info('Fetching users by type or role');
    await controller.getUsersByTypeOrRole(req, res);
    logger.info('Successfully fetched users by type or role');
  } catch (error) {
    logger.error(`Error in get_users_by_type_or_role: ${error}`);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch users by type or role'
    });
  }
});

// Get user by ID
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    logger.info(`Fetching user with ID: ${req.params['id']}`);
    await controller.getUserById(req, res);
    logger.info(`Successfully fetched user with ID: ${req.params['id']}`);
  } catch (error) {
    logger.error(`Error in get_user_by_id: ${error}`);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch user'
    });
  }
});

// Create a user profile (after Supabase auth)
router.post('/', authenticateToken, async (req, res) => {
  try {
    logger.info('Creating user profile');
    await controller.createUserProfile(req, res);
    logger.info('Successfully created user profile');
  } catch (error) {
    logger.error(`Error in create_user_profile: ${error}`);
    res.status(500).json({
      success: false,
      error: 'Failed to create user profile'
    });
  }
});

// Create a new user (admin only)
router.post('/admin', authenticateToken, async (req, res) => {
  try {
    logger.info('Creating new user');
    await controller.createUser(req, res);
    logger.info('Successfully created new user');
  } catch (error) {
    logger.error(`Error in create_user: ${error}`);
    res.status(500).json({
      success: false,
      error: 'Failed to create user'
    });
  }
});

// Update user
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    logger.info(`Updating user with ID: ${req.params['id']}`);
    await controller.updateUser(req, res);
    logger.info(`Successfully updated user with ID: ${req.params['id']}`);
  } catch (error) {
    logger.error(`Error in update_user: ${error}`);
    res.status(500).json({
      success: false,
      error: 'Failed to update user'
    });
  }
});

// Delete user
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    logger.info(`Deleting user with ID: ${id}`);
    await controller.deleteUser(req, res);
    logger.info(`Successfully deleted user with ID: ${id}`);
  } catch (error) {
    logger.error(`Error in delete_user: ${error}`);
    res.status(500).json({
      success: false,
      error: 'Failed to delete user'
    });
  }
});

// Get user UI preferences
router.get('/:id/ui-preferences', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    logger.info(`Fetching UI preferences for user with ID: ${id}`);
    await controller.getUserUIPreferences(req, res);
    logger.info(`Successfully fetched UI preferences for user with ID: ${id}`);
  } catch (error) {
    logger.error(`Error in get_user_ui_preferences: ${error}`);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch UI preferences'
    });
  }
});

// Update user UI preferences
router.put('/:id/ui-preferences', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    logger.info(`Updating UI preferences for user with ID: ${id}`);
    await controller.updateUIPreferences(req, res);
    logger.info(`Successfully updated UI preferences for user with ID: ${id}`);
  } catch (error) {
    logger.error(`Error in update_user_ui_preferences: ${error}`);
    res.status(500).json({
      success: false,
      error: 'Failed to update UI preferences'
    });
  }
});

export default router;