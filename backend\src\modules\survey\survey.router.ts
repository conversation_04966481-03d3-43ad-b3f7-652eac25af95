import { Router } from 'express';
import { SurveyController } from './survey.controller';
import { SurveyService } from './survey.service';
import { authenticateToken } from '../../middleware/auth';

const router = Router();
const surveyService = new SurveyService();
const surveyController = new SurveyController(surveyService);

// Public routes (no authentication required)
router.get('/', surveyController.getSurveys.bind(surveyController)); // Get all surveys
router.get('/:id', surveyController.getSurvey.bind(surveyController)); // Get specific survey

// Protected routes (authentication required)
router.post('/', authenticateToken, surveyController.createSurvey.bind(surveyController));
router.put('/:id', authenticateToken, surveyController.updateSurvey.bind(surveyController));
router.delete('/:id', authenticateToken, surveyController.deleteSurvey.bind(surveyController));

export default router; 