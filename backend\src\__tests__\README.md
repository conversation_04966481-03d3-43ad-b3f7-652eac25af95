# API Tests

This directory contains only pure public API tests for the <PERSON><PERSON><PERSON> backend. These tests are designed to run against a real Supabase instance for authentication and test only through HTTP requests without direct database access.

## Test Setup

### Prerequisites

1. Supabase instance running (local or remote)
2. Environment variables set:
   - `SUPABASE_URL`: URL of your Supabase instance
   - `SUPABASE_ANON_KEY`: Anonymous key for Supabase
   - `JWT_SECRET`: Secret for JWT token generation

### Test Structure

Each test module follows these rules:

1. Uses `SupabaseAuthWrapper` for user authentication
2. Creates a new user for each test
3. Cleans up all test data after each test via API calls only
4. Tests both authenticated and unauthenticated scenarios
5. Uses real Supabase authentication
6. **NO DIRECT DATABASE ACCESS** - all operations through HTTP API

### Running Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run specific test file
npm test -- path/to/test/file.test.ts
```

### Test Utilities

- `SupabaseAuthWrapper`: Handles user authentication lifecycle
  - `signUp()`: Creates a new user in Supabase and our database
  - `signIn()`: Signs in an existing user
  - `signOut()`: Signs out the current user
  - `deleteUser()`: Deletes the current user
  - `getUserToken()`: Gets the current user's access token
  - `getCurrentUser()`: Gets the current user object

### Best Practices

1. Each test should be independent and not rely on data from other tests
2. Always clean up test data in `afterEach` blocks via API calls
3. Use the auth wrapper for all user-related operations
4. Test both success and failure scenarios
5. Verify authentication requirements for protected routes
6. **Only test through public API endpoints** - no direct database access