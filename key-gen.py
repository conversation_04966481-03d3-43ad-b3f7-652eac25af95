import jwt
import time

# Your JWT secret from GOTRUE_JWT_SECRET
JWT_SECRET = 'super-secret-jwt-token-with-at-least-32-characters-for-kibbutz-app'

# Payload for Service Role token
payload = {
    'role': 'service_role',
    'iss': 'supabase',
    'aud': 'authenticated',
    'iat': int(time.time()),                     # issued at now
    'exp': int(time.time()) + (365 * 24 * 3600),  # expires in 1 year
}

# Encode the token
token = jwt.encode(payload, JWT_SECRET, algorithm='HS256')

# If using PyJWT >= 2.0, token will be bytes — decode to string:
if isinstance(token, bytes):
    token = token.decode('utf-8')

# Print the token
print('SUPABASE_SERVICE_ROLE_KEY=' + token)
