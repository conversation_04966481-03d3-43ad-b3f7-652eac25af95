import React from 'react';
import { StyleSheet } from 'react-native';
import { TextInput, useTheme } from 'react-native-paper';
import { rtlConfig } from '../../theme/rtl';

interface InputProps {
  label: string;
  value: string;
  onChangeText: (text: string) => void;
  error?: string;
  secureTextEntry?: boolean;
  keyboardType?: 'default' | 'email-address' | 'numeric' | 'phone-pad';
  style?: any;
}

export const Input = ({
  label,
  value,
  onChangeText,
  error,
  secureTextEntry = false,
  keyboardType = 'default',
  style,
}: InputProps) => {
  const theme = useTheme();

  return (
    <TextInput
      label={label}
      value={value}
      onChangeText={onChangeText}
      error={!!error}
      secureTextEntry={secureTextEntry}
      keyboardType={keyboardType}
      style={[
        styles.input,
        { backgroundColor: theme.colors.surface },
        style,
      ]}
      mode="outlined"
      textAlign={rtlConfig.textAlign}
      textContentType={secureTextEntry ? 'password' : 'none'}
    />
  );
};

const styles = StyleSheet.create({
  input: {
    marginVertical: 8,
  },
}); 