import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { JobsStackParamList } from './types';
import JobsListScreen from '../screens/jobs/JobsListScreen';
import JobDetailsScreen from '../screens/jobs/JobDetailsScreen';
import CreateJobScreen from '../screens/jobs/CreateJobScreen';
import MyJobsScreen from '../screens/jobs/MyJobsScreen';

const Stack = createStackNavigator<JobsStackParamList>();

export default function JobsNavigator() {
  return (
    <Stack.Navigator
      initialRouteName="JobsList"
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen name="JobsList" component={JobsListScreen} />
      <Stack.Screen name="JobDetails" component={JobDetailsScreen} />
      <Stack.Screen name="CreateJob" component={CreateJobScreen} />
      <Stack.Screen name="MyJobs" component={MyJobsScreen} />
    </Stack.Navigator>
  );
}
