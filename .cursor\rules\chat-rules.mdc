---
description: 
globs: 
alwaysApply: false
---
### Chat Module Rules

#### 1. Chat Group Access Rules
- Only group members can:
  - View group details
  - View group messages
  - Send messages to the group
- Only group creator/owner can:
  - Update group details (name, etc.)
  - Delete the group
  - Add/remove members

#### 2. Message Access Rules
- Only message sender can:
  - Edit their own messages
  - Delete their own messages
- Only group members can:
  - View messages in the group
  - Send new messages to the group
- Non-members should:
  - Get 403 (Forbidden) when trying to access group messages
  - Get 403 when trying to send messages to the group

#### 3. User Authentication Rules
- All API endpoints require valid Supabase authentication token
- Invalid/missing tokens should return 401 (Unauthorized)
- Valid tokens should be verified against Supabase

#### 4. Error Response Rules
- 401 (Unauthorized): Invalid/missing authentication token
- 403 (Forbidden): Valid token but insufficient permissions
- 404 (Not Found): Resource doesn't exist
- 200 (OK): Successful GET/PUT operations
- 201 (Created): Successful POST operations
- 204 (No Content): Successful DELETE operations

#### 5. Data Validation Rules
- Group creation requires:
  - Name
  - Community ID
  - Creator ID
  - At least one member (creator)
- Message creation requires:
  - Group ID
  - Sender ID
  - Text content
  - Community ID

#### 6. Cleanup Rules
- When a group is deleted:
  - All messages in the group should be deleted
  - Group membership should be removed
- When a user is deleted:
  - Their messages should be preserved

  - They should be removed from group memberships 