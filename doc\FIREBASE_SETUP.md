# 🔥 Firebase Authentication Setup Guide

## ⚠️ IMPORTANT: You need to set up a real Firebase project to use authentication

The current error `auth/api-key-not-valid` means you're using placeholder values. Follow these steps to fix it:

## 📋 Step-by-Step Setup (5 minutes)

### 1. Create Firebase Project (FREE)
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Click **"Create a project"**
3. Project name: `kibbutz-community-app` (or any name)
4. **Disable Google Analytics** (not needed)
5. Click **"Create project"**

### 2. Add Web App
1. Click the **web icon** `</>`
2. App nickname: `Kibbutz Web App`
3. **Don't check Firebase Hosting**
4. Click **"Register app"**

### 3. Copy Configuration
You'll see a config object like this:
```javascript
const firebaseConfig = {
  apiKey: "AIzaSyC1234567890abcdefghijklmnopqrstuvwxyz",
  authDomain: "your-project.firebaseapp.com",
  projectId: "your-project-id",
  storageBucket: "your-project.appspot.com",
  messagingSenderId: "123456789012",
  appId: "1:123456789012:web:abcdef1234567890"
};
```

### 4. Enable Email Authentication
1. In Firebase Console, go to **"Authentication"**
2. Click **"Get started"**
3. Go to **"Sign-in method"** tab
4. Click **"Email/Password"**
5. **Enable** the first option (Email/Password)
6. Click **"Save"**

### 5. Update Your App Config
1. Open `.env` file in the root directory
2. Replace the placeholder values with your real config:

```env
EXPO_PUBLIC_FIREBASE_API_KEY=AIzaSyC1234567890abcdefghijklmnopqrstuvwxyz
EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
EXPO_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=123456789012
EXPO_PUBLIC_FIREBASE_APP_ID=1:123456789012:web:abcdef1234567890
```

**OR** use the interactive setup script:
```bash
node setup-firebase.js
```

### 6. Test the App
1. Save the file
2. The app will automatically reload
3. Try registering a new user
4. Try logging in

## 🆓 Firebase Free Tier Limits

✅ **50,000 monthly active users**
✅ **Unlimited sign-ins**
✅ **Password reset emails**
✅ **No credit card required**

## 🔐 What We're Using

- ✅ **Firebase Authentication** - for secure login/registration
- ✅ **Local Storage** - for user data (no Firestore costs)
- ❌ **NOT using Firestore** - saves money
- ❌ **NOT using Firebase Storage** - saves money

## 🚨 Security Notes

- Your API key is safe to expose in client-side code
- Firebase automatically secures it with domain restrictions
- Only your domain can use the API key

## 🆘 Need Help?

If you get stuck:
1. Make sure you enabled **Email/Password** authentication
2. Check that you copied the config correctly
3. Verify your project is active in Firebase Console

## 🎯 After Setup

Once configured, you'll have:
- Secure user authentication
- Password reset emails
- Local user data storage
- Zero ongoing costs
- Support for 50,000 users

The authentication will work perfectly with your kibbutz community app!
