Frontend

    Expo (React Native)
    Cross-platform mobile app (iOS/Android/web) using React Native and JavaScript/TypeScript.

🗄️ Database

    PostgreSQL (running via Docker locally)
    Relational database storing all app data. Designed for multi-tenant use with a tenant_id column in shared tables.

🧰 Backend Services (via Supabase – Self-hosted)

Supabase components run locally using Docker Compose:

    PostgREST – Auto-generates REST API for all tables and views

    GoTrue – Auth service for email/password, OAuth, magic links

    Realtime – Live data updates over WebSockets

    Storage – Manage and serve file uploads (images, documents)

    Supabase Studio – Web dashboard to view and edit DB + auth

⚙️ Custom Business Logic Layer

    Node.js + Express (Optional) – Custom backend logic if needed

    Prisma ORM – Type-safe database client for PostgreSQL
    Used in your custom backend service to interact with the database directly, offering:

        Data validation

        Type-safe queries

        Easy migrations and modeling

🔐 Authentication

    Managed by GoTrue (Supabase Auth)
    Includes session management, JWT support, and OAuth providers (Google, GitHub, etc.)