// Supabase Authentication Service
// Uses Supabase GoTrue for authentication + Backend API for user data
import { User } from '../types';
import {
  signIn,
  signOut,
  getCurrentUser,
  resetPassword,
  supabase
} from '../lib/supabase';
import { apiService } from './api';

export interface RegisterData {
  email: string;
  password: string;
  name: string;
  phone?: string;
  dateOfBirth?: Date;
  userType: 'ADULT' | 'YOUTH' | 'CHILD' | 'EXTERNAL'; // Match backend UserType enum
}

export interface LoginData {
  email: string;
  password: string;
}

export class AuthService {
  // Note: First user detection is now handled by the backend

  // Get all users from database
  static async getAllUsers(): Promise<User[]> {
    try {
      const response = await apiService.getAllUsers();

      if (response.error) {
        console.error('Error getting users:', response.error);
        return [];
      }

      return response.data || [];
    } catch (error) {
      console.error('Error getting users:', error);
      return [];
    }
  }

  static async register(data: RegisterData): Promise<User> {
    try {
      console.log('🔥 AuthService.register called with:', data);

      // 1. Create Supabase user
      console.log('🔐 Creating Supabase user...');
      const { data: authData, error: signUpError } = await supabase.auth.signUp({
        email: data.email,
        password: data.password,
        options: {
          data: {
            name: data.name,
            phone: data.phone,
            dateOfBirth: data.dateOfBirth,
            user_type: data.userType // Changed from userType to user_type to match backend
          }
        }
      });

      if (signUpError) {
        console.error('❌ Supabase signup error:', signUpError);
        throw new Error(signUpError.message);
      }

      if (!authData.user || !authData.session) {
        console.error('❌ No user data or session returned from Supabase');
        throw new Error('Failed to create user');
      }

      console.log('✅ Supabase user created:', authData.user.id);
      console.log('✅ Session obtained from signup response');

      // 4. Set token in API service for authenticated requests
      console.log('🔑 Setting API token...');
      apiService.setToken(authData.session.access_token);

      // 5. Create user profile in backend
      console.log('👤 Creating user profile...');

      // Let backend determine the role (first user gets ADMIN automatically)
      const response = await apiService.createUserProfile({
        id: authData.user.id,
        email: data.email,
        firstName: data.name.split(' ')[0] || data.name,
        lastName: data.name.split(' ').slice(1).join(' ') || '',
        role: 'USER', // Backend will override this to ADMIN for first user
        userType: data.userType,
        phone: data.phone || '',
        language: 'he'
      });

      if (response.error) {
        console.error('❌ Error creating user profile:', response.error);
        throw new Error('Failed to create user profile');
      }

      console.log('✅ User profile created successfully');
      console.log('👤 Backend assigned role:', response.data?.role);

      return response.data as User;
    } catch (error) {
      console.error('💥 Registration error in AuthService:', error);
      throw error;
    }
  }

  static async login(data: LoginData): Promise<User> {
    try {
      console.log('🔥 AuthService.login called for:', data.email);

      console.log('🔐 Attempting Supabase authentication...');
      const { data: authData, error: authError } = await signIn(data.email, data.password);

      if (authError) {
        throw new Error(authError.message || 'Login failed');
      }

      if (!authData?.user) {
        throw new Error('Login failed');
      }

      const supabaseUser = authData.user;
      console.log('✅ Supabase authentication successful:', supabaseUser.id);

      // Use the session from auth data instead of making another call
      console.log('🔑 Using session from auth data...');
      if (!authData.session) {
        throw new Error('Failed to get session');
      }
      const sessionData = { session: authData.session };
      console.log('✅ Session token obtained');

      // Get user profile from database
      console.log('📄 Fetching user profile from database...');
      const { data: userProfile, error: profileError } = await apiService.getUserProfile(supabaseUser.id);

      if (profileError || !userProfile) {
        console.log('⚠️ User profile not found in database, creating from auth user...');
        // Set token for API requests
        apiService.setToken(sessionData.session.access_token);

        // Create user profile from auth user info (fallback)
        const { data: newProfile, error: createError } = await apiService.createUserProfile({
          id: supabaseUser.id,
          email: supabaseUser.email || data.email,
          firstName: supabaseUser.user_metadata?.name?.split(' ')[0] || 'User',
          lastName: supabaseUser.user_metadata?.name?.split(' ').slice(1).join(' ') || '',
          role: 'USER', // Backend will determine actual role
          userType: supabaseUser.user_metadata?.user_type || 'ADULT',
          language: 'he',
        });

        if (createError) {
          throw new Error('Failed to create user profile');
        }

        console.log('✅ User profile created');
        console.log('👤 Login fallback - Backend assigned role:', newProfile?.role);
        return newProfile as User;
      }

      console.log('✅ User profile retrieved:', {
        id: userProfile.id,
        email: userProfile.email,
        name: `${userProfile.firstName} ${userProfile.lastName}`,
        role: userProfile.role
      });

      console.log('🎉 Login completed successfully');
      return userProfile as User;
    } catch (error) {
      console.error('💥 Login error in AuthService:', error);
      console.error('Error details:', {
        message: (error as any).message,
        code: (error as any).code,
        stack: (error as any).stack
      });
      throw error;
    }
  }

  static async logout(): Promise<void> {
    try {
      console.log('🚪 Logging out user...');

      // Sign out from Supabase
      const { error } = await signOut();
      if (error) {
        throw new Error(error.message || 'Logout failed');
      }

      console.log('✅ Supabase logout completed');
    } catch (error) {
      console.error('Logout error:', error);
      throw error;
    }
  }

  static async resetPassword(email: string): Promise<void> {
    try {
      console.log('🔄 Sending password reset email via Supabase...');
      const { error } = await resetPassword(email);
      if (error) {
        throw new Error(error.message || 'Failed to send reset email');
      }
      console.log('✅ Password reset email sent');
    } catch (error) {
      console.error('Password reset error:', error);
      throw error;
    }
  }

  static async getCurrentUser(): Promise<User | null> {
    try {
      // Get current user from Supabase auth
      const { data: authData, error: authError } = await getCurrentUser();
      if (authError || !authData?.user) {
        return null;
      }

      // Get user profile from database
      const { data: userProfile, error: profileError } = await apiService.getUserProfile(authData.user.id);
      if (profileError || !userProfile) {
        console.error('Error getting user profile:', profileError);
        return null;
      }

      return userProfile as User;
    } catch (error) {
      console.error('Get current user error:', error);
      return null;
    }
  }

  // Function for super admins to promote users to admin
  static async promoteUserToAdmin(userId: string, currentUserRole: string): Promise<void> {
    if (currentUserRole !== 'SUPER_ADMIN') {
      throw new Error('Only super admins can promote users to admin');
    }

    try {
      // Get user profile from database
      const { data: userData, error: getUserError } = await apiService.getUserProfile(userId);
      if (getUserError || !userData) {
        throw new Error('User not found');
      }

      if (userData.role === 'USER' && userData.userType === 'GUEST') {
        throw new Error('Cannot promote guests to admin');
      }

      if (userData.role === 'SUPER_ADMIN') {
        throw new Error('Cannot modify super admin role');
      }

      // Update user role in database
      const { error: updateError } = await apiService.updateUserProfile(userId, { role: 'ADMIN' });
      if (updateError) {
        throw new Error('Failed to update user role');
      }

      console.log(`User ${userId} promoted to admin`);
    } catch (error) {
      console.error('Error promoting user:', error);
      throw error;
    }
  }

  // Function to demote admin back to adult (super admin only)
  static async demoteAdminToAdult(userId: string, currentUserRole: string): Promise<void> {
    if (currentUserRole !== 'SUPER_ADMIN') {
      throw new Error('Only super admins can demote admins');
    }

    try {
      // Get user profile from database
      const { data: userData, error: getUserError } = await apiService.getUserProfile(userId);
      if (getUserError || !userData) {
        throw new Error('User not found');
      }

      if (userData.role === 'SUPER_ADMIN') {
        throw new Error('Cannot demote super admin');
      }

      if (userData.role !== 'ADMIN') {
        throw new Error('User is not an admin');
      }

      // Update user role in database
      const { error: updateError } = await apiService.updateUserProfile(userId, { role: 'USER' });
      if (updateError) {
        throw new Error('Failed to update user role');
      }

      console.log(`Admin ${userId} demoted to user`);
    } catch (error) {
      console.error('Error demoting admin:', error);
      throw error;
    }
  }
}
