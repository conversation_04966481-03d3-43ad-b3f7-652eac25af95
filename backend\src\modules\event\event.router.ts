import { Router } from 'express';
import { EventController } from './event.controller';
import { EventService } from './event.service';
import { authenticateToken } from '../../middleware/auth';

const router = Router();
const eventService = new EventService();
const eventController = new EventController(eventService);

// Public routes (no authentication required)
router.get('/', eventController.getEvents.bind(eventController)); // Get all events
router.get('/:id', eventController.getEvent.bind(eventController)); // Get specific event

// Protected routes (authentication required)
router.post('/', authenticateToken, eventController.createEvent.bind(eventController));
router.put('/:id', authenticateToken, eventController.updateEvent.bind(eventController));
router.delete('/:id', authenticateToken, eventController.deleteEvent.bind(eventController));

export default router; 