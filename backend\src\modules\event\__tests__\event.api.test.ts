import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import request from 'supertest';
import { app } from '../../../app';
import { SupabaseAuthWrapper } from '../../../__tests__/supabaseAuth';

describe('Event API', () => {
  let auth: SupabaseAuthWrapper;
  let testEvent: any;

  beforeEach(async () => {
    // Create new auth wrapper for each test
    auth = new SupabaseAuthWrapper();
    // Sign up a new user for this test
    const { user, token } = await auth.signUp();
    expect(user).toBeDefined();
    expect(token).toBeDefined();
    expect(user.id).toBeDefined();
    expect(user.email).toBeDefined();
    // Create a test event via the public API
    const newEvent = {
      title: 'Test Event',
      description: 'Test Description',
      date: new Date().toISOString(),
      location: 'Test Location',
      type: 'community',
      imageUrl: 'https://example.com/image.jpg',
      dataAiHint: 'Test hint'
    };
    const response = await request(app)
      .post('/api/events')
      .set('Authorization', `Bearer ${auth.getUserToken()}`)
      .send(newEvent);
    expect(response.status).toBe(201);
    testEvent = response.body;
  });

  afterEach(async () => {
    try {
      // Delete the test event via the public API (if it still exists)
      if (testEvent && testEvent.id) {
        await request(app)
          .delete(`/api/events/${testEvent.id}`)
          .set('Authorization', `Bearer ${auth.getUserToken()}`);
      }
      // Delete the test user
      await auth.deleteUser();
    } catch (error) {
      console.error('Error in afterEach cleanup:', error);
    }
  });

  describe('Authentication', () => {
    it('should require valid Supabase token for protected routes', async () => {
      const response = await request(app)
        .get('/api/events')
        .set('Authorization', 'Bearer invalid-token');
      expect(response.status).toBe(401);
    });
    it('should accept valid Supabase token for protected routes', async () => {
      const response = await request(app)
        .get('/api/events')
        .set('Authorization', `Bearer ${auth.getUserToken()}`);
      expect(response.status).toBe(200);
    });
  });

  describe('Public Routes', () => {
    it('should get all events (unauthenticated should fail)', async () => {
      const response = await request(app)
        .get('/api/events');
      expect(response.status).toBe(401);
    });
    it('should get event by ID (unauthenticated should fail)', async () => {
      const response = await request(app)
        .get(`/api/events/${testEvent.id}`);
      expect(response.status).toBe(401);
    });
    it('should return 404 for non-existent event (unauthenticated)', async () => {
      const response = await request(app)
        .get('/api/events/non-existent-id');
      expect(response.status).toBe(401);
    });
  });

  describe('Protected Routes', () => {
    it('should create a new event', async () => {
      const newEvent = {
        title: 'New Event',
        description: 'New Description',
        date: new Date().toISOString(),
        location: 'New Location',
        type: 'community',
        imageUrl: 'https://example.com/image.jpg',
        dataAiHint: 'Test hint'
      };
      const response = await request(app)
        .post('/api/events')
        .set('Authorization', `Bearer ${auth.getUserToken()}`)
        .send(newEvent);
      expect(response.status).toBe(201);
      expect(response.body.title).toBe(newEvent.title);
      expect(response.body.description).toBe(newEvent.description);
      expect(response.body.type).toBe(newEvent.type);
      expect(response.body.imageUrl).toBe(newEvent.imageUrl);
      expect(response.body.dataAiHint).toBe(newEvent.dataAiHint);
      expect(response.body.created_by).toBe(auth.getCurrentUser().id);
      // Clean up
      await request(app)
        .delete(`/api/events/${response.body.id}`)
        .set('Authorization', `Bearer ${auth.getUserToken()}`);
    });
    it('should not create event without authentication', async () => {
      const newEvent = {
        title: 'New Event',
        description: 'New Description',
        date: new Date().toISOString(),
        location: 'New Location',
        type: 'community'
      };
      const response = await request(app)
        .post('/api/events')
        .send(newEvent);
      expect(response.status).toBe(401);
    });
    it('should update an event', async () => {
      const updateData = {
        title: 'Updated Event',
        description: 'Updated Description',
        location: 'Updated Location',
        type: 'tenant'
      };
      const response = await request(app)
        .put(`/api/events/${testEvent.id}`)
        .set('Authorization', `Bearer ${auth.getUserToken()}`)
        .send(updateData);
      expect(response.status).toBe(200);
      expect(response.body.title).toBe(updateData.title);
      expect(response.body.description).toBe(updateData.description);
      expect(response.body.location).toBe(updateData.location);
      expect(response.body.type).toBe(updateData.type);
    });
    it('should not update event without authentication', async () => {
      const updateData = {
        title: 'Updated Event',
        description: 'Updated Description'
      };
      const response = await request(app)
        .put(`/api/events/${testEvent.id}`)
        .send(updateData);
      expect(response.status).toBe(401);
    });
    it('should not update non-existent event', async () => {
      const updateData = {
        title: 'Updated Event',
        description: 'Updated Description'
      };
      const response = await request(app)
        .put('/api/events/non-existent-id')
        .set('Authorization', `Bearer ${auth.getUserToken()}`)
        .send(updateData);
      expect(response.status).toBe(404);
      expect(response.body.message).toBe('Event not found');
    });
    it('should delete an event', async () => {
      // Create a new event to delete
      const newEvent = {
        title: 'Delete Me',
        description: 'To be deleted',
        date: new Date().toISOString(),
        location: 'Delete Location',
        type: 'community'
      };
      const createRes = await request(app)
        .post('/api/events')
        .set('Authorization', `Bearer ${auth.getUserToken()}`)
        .send(newEvent);
      expect(createRes.status).toBe(201);
      const eventId = createRes.body.id;
      const response = await request(app)
        .delete(`/api/events/${eventId}`)
        .set('Authorization', `Bearer ${auth.getUserToken()}`);
      expect(response.status).toBe(204);
      // Verify event is deleted
      const getResponse = await request(app)
        .get(`/api/events/${eventId}`)
        .set('Authorization', `Bearer ${auth.getUserToken()}`);
      expect(getResponse.status).toBe(404);
    });
    it('should not delete event without authentication', async () => {
      const response = await request(app)
        .delete(`/api/events/${testEvent.id}`);
      expect(response.status).toBe(401);
    });
    it('should not delete non-existent event', async () => {
      const response = await request(app)
        .delete('/api/events/non-existent-id')
        .set('Authorization', `Bearer ${auth.getUserToken()}`);
      expect(response.status).toBe(404);
      expect(response.body.message).toBe('Event not found');
    });
  });

  describe('Multi-user Scenarios', () => {
    let otherAuth: SupabaseAuthWrapper;
    let otherUserEvent: any;

    beforeEach(async () => {
      // Create another user
      otherAuth = new SupabaseAuthWrapper();
      const { user, token } = await otherAuth.signUp();
      expect(user).toBeDefined();
      expect(token).toBeDefined();

      // Create an event for the other user via the public API
      const newEvent = {
        title: 'Other User Event',
        description: 'Other User Description',
        date: new Date().toISOString(),
        location: 'Other Location',
        type: 'community'
      };
      const response = await request(app)
        .post('/api/events')
        .set('Authorization', `Bearer ${otherAuth.getUserToken()}`)
        .send(newEvent);
      expect(response.status).toBe(201);
      otherUserEvent = response.body;
    });

    afterEach(async () => {
      try {
        // Clean up other user's event via the public API
        if (otherUserEvent && otherUserEvent.id) {
          await request(app)
            .delete(`/api/events/${otherUserEvent.id}`)
            .set('Authorization', `Bearer ${otherAuth.getUserToken()}`);
        }
        // Delete the other user
        await otherAuth.deleteUser();
      } catch (error) {
        console.error('Error in multi-user afterEach cleanup:', error);
      }
    });

    it('should not allow user to update another user\'s event', async () => {
      const updateData = {
        title: 'Unauthorized Update',
        description: 'This should fail'
      };
      const response = await request(app)
        .put(`/api/events/${otherUserEvent.id}`)
        .set('Authorization', `Bearer ${auth.getUserToken()}`)
        .send(updateData);
      expect(response.status).toBe(403);
      expect(response.body.message).toBe('Not authorized to update this event');
    });
    it('should not allow user to delete another user\'s event', async () => {
      const response = await request(app)
        .delete(`/api/events/${otherUserEvent.id}`)
        .set('Authorization', `Bearer ${auth.getUserToken()}`);
      expect(response.status).toBe(403);
      expect(response.body.message).toBe('Not authorized to delete this event');
    });
  });
}); 