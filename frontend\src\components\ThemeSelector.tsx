import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import { useTheme } from 'react-native-paper';
import { getTextAlign } from '../utils/rtl';
import { useThemeStyles } from '../hooks/useThemeStyles';
import { UIPreferences } from '../types';

interface ThemeSelectorProps {
  title: string;
  options: Array<{
    key: string;
    label: string;
    value: any;
  }>;
  selectedValue: any;
  onSelect: (value: any) => void;
  disabled?: boolean;
}

export const ThemeSelector: React.FC<ThemeSelectorProps> = ({
  title,
  options,
  selectedValue,
  onSelect,
  disabled = false,
}) => {
  const theme = useTheme();

  return (
    <View style={styles.container}>
      <Text style={[styles.title, { color: theme.colors.onBackground, textAlign: getTextAlign() }]}>
        {title}
      </Text>
      <View style={styles.optionsContainer}>
        {options.map((option) => (
          <TouchableOpacity
            key={option.key}
            style={[
              styles.optionButton,
              {
                backgroundColor: selectedValue === option.value ? theme.colors.primary : theme.colors.surface,
                borderColor: theme.colors.outline,
              },
              disabled && styles.disabledButton,
            ]}
            onPress={() => {
              console.log('🎨 ThemeSelector: Option selected:', option.value);
              onSelect(option.value);
            }}
            disabled={disabled}
          >
            <Text
              style={[
                styles.optionText,
                {
                  color: selectedValue === option.value ? theme.colors.onPrimary : theme.colors.onSurface,
                  textAlign: getTextAlign(),
                },
              ]}
            >
              {option.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};

interface ThemeToggleProps {
  title: string;
  value: boolean;
  onToggle: () => void;
  disabled?: boolean;
}

export const ThemeToggle: React.FC<ThemeToggleProps> = ({
  title,
  value,
  onToggle,
  disabled = false,
}) => {
  const theme = useTheme();

  return (
    <TouchableOpacity
      style={[
        styles.toggleContainer,
        { borderColor: theme.colors.outline },
        disabled && styles.disabledButton,
      ]}
      onPress={onToggle}
      disabled={disabled}
    >
      <Text style={[styles.toggleTitle, { color: theme.colors.onBackground, textAlign: getTextAlign() }]}>
        {title}
      </Text>
      <View
        style={[
          styles.toggle,
          {
            backgroundColor: value ? theme.colors.primary : theme.colors.surface,
            borderColor: theme.colors.outline,
          },
        ]}
      >
        <Text style={[styles.toggleText, { color: value ? theme.colors.onPrimary : theme.colors.onSurface }]}>
          {value ? 'פעיל' : 'כבוי'}
        </Text>
      </View>
    </TouchableOpacity>
  );
};

interface ThemePreviewProps {
  preferences: UIPreferences;
}

export const ThemePreview: React.FC<ThemePreviewProps> = ({ preferences }) => {
  const theme = useTheme();
  const { textStyles, spacing } = useThemeStyles();

  return (
    <View style={[styles.previewContainer, { backgroundColor: theme.colors.surface, borderColor: theme.colors.outline, padding: spacing.md }]}>
      <Text style={[textStyles.h6, { textAlign: getTextAlign(), marginBottom: spacing.sm }]}>
        תצוגה מקדימה
      </Text>
      <View style={[styles.previewContent, { gap: spacing.sm }]}>
        <View style={[styles.previewCard, { backgroundColor: theme.colors.primary, padding: spacing.sm }]}>
          <Text style={[textStyles.subtitle1, { color: theme.colors.onPrimary }]}>
            כרטיס דוגמה
          </Text>
        </View>
        <Text style={textStyles.body1}>
          טקסט דוגמה בגודל {preferences.fontSize}
        </Text>
        <Text style={textStyles.body2}>
          טקסט קטן יותר
        </Text>
        <Text style={textStyles.caption}>
          מרווחים: {preferences.spacing} | גופן: {preferences.fontSize}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 20,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  optionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  optionButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    borderWidth: 1,
    minWidth: 70,
  },
  disabledButton: {
    opacity: 0.5,
  },
  optionText: {
    fontSize: 14,
    fontWeight: '500',
  },
  toggleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 20,
  },
  toggleTitle: {
    fontSize: 16,
    fontWeight: '500',
  },
  toggle: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    borderWidth: 1,
  },
  toggleText: {
    fontSize: 14,
    fontWeight: '500',
  },
  previewContainer: {
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 20,
  },
  previewTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  previewContent: {
    gap: 8,
  },
  previewCard: {
    padding: 12,
    borderRadius: 6,
  },
  previewCardText: {
    fontSize: 14,
    fontWeight: '500',
  },
  previewText: {
    fontSize: 14,
  },
  previewSubtext: {
    fontSize: 12,
    opacity: 0.7,
  },
});
