import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import request from 'supertest';
import { app } from '../../../app';
import prisma from '../../../config/prismaClient';
import { UserRole, UserType } from '../../../generated/prisma';

describe('Auth API', () => {
  const testUser = {
    email: `test-${Date.now()}@example.com`,
    password: 'Test123!@#',
    firstName: 'Test',
    lastName: 'User',
    role: UserRole.USER,
    userType: UserType.ADULT,
    language: 'en'
  };

  afterEach(async () => {
    // Clean up test data
    await prisma.user.deleteMany({
      where: { email: testUser.email }
    });
  });

  describe('Registration', () => {
    it('should register a new user', async () => {
      const response = await request(app)
        .post('/apiauth/register')
        .send(testUser);

      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('accessToken');
      expect(response.body.data).toHaveProperty('refreshToken');
      expect(response.body.data.user).toMatchObject({
        email: testUser.email,
        role: testUser.role,
        user_type: testUser.userType,
        first_name: testUser.firstName,
        last_name: testUser.lastName,
        language: testUser.language
      });
    });

    it('should not register a user with existing email', async () => {
      // First registration
      await request(app)
        .post('/apiauth/register')
        .send(testUser);

      // Try to register again with same email
      const response = await request(app)
        .post('/apiauth/register')
        .send(testUser);

      expect(response.status).toBe(409);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('User already exists');
    });
  });

  describe('Login', () => {
    beforeEach(async () => {
      // Register a test user
      await request(app)
        .post('/apiauth/register')
        .send(testUser);
    });

    it('should login with valid credentials', async () => {
      const response = await request(app)
        .post('/apiauth/login')
        .send({
          email: testUser.email,
          password: testUser.password
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('accessToken');
      expect(response.body.data).toHaveProperty('refreshToken');
      expect(response.body.data.user.email).toBe(testUser.email);
    });

    it('should not login with invalid password', async () => {
      const response = await request(app)
        .post('/apiauth/login')
        .send({
          email: testUser.email,
          password: 'wrongpassword'
        });

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Invalid credentials');
    });

    it('should not login with non-existent email', async () => {
      const response = await request(app)
        .post('/apiauth/login')
        .send({
          email: '<EMAIL>',
          password: testUser.password
        });

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Invalid credentials');
    });
  });

  describe('Token Management', () => {
    let refreshToken: string;

    beforeEach(async () => {
      // Register and login to get tokens
      const registerResponse = await request(app)
        .post('/apiauth/register')
        .send(testUser);

      refreshToken = registerResponse.body.data.refreshToken;
    });

    it('should refresh access token', async () => {
      const response = await request(app)
        .post('/apiauth/refresh-token')
        .send({ refreshToken });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('accessToken');
    });

    it('should not refresh with invalid token', async () => {
      const response = await request(app)
        .post('/apiauth/refresh-token')
        .send({ refreshToken: 'invalid-token' });

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Invalid refresh token');
    });

    it('should logout successfully', async () => {
      const response = await request(app)
        .post('/apiauth/logout')
        .send({ refreshToken });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Logged out successfully');
    });

    it('should not logout without refresh token', async () => {
      const response = await request(app)
        .post('/apiauth/logout')
        .send({});

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Refresh token is required');
    });
  });
}); 