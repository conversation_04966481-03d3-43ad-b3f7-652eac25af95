import { UserRole, UserType } from '../../generated/prisma';

export interface LoginDTO {
  email: string;
  password: string;
}

export interface RegisterDTO {
  email: string;
  password: string;
  firstName?: string;
  lastName?: string;
  role: UserRole;
  userType: UserType;
  phone?: string;
  language?: string;
}

export interface AuthResponse {
  success: boolean;
  data: {
    accessToken: string;
    refreshToken: string;
    user: {
      id: string;
      email: string;
      role: UserRole;
      user_type: UserType;
      first_name: string | null;
      last_name: string | null;
      language: string;
      phone: string | null;
      is_active: boolean;
      last_login: Date | null;
      google_id: string | null;
      created_at: Date;
      updated_at: Date;
    };
  };
} 