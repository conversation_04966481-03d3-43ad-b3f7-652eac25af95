import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { PeopleStackParamList } from './types';
import PeopleDirectoryScreen from '../screens/people/PeopleDirectoryScreen';

const Stack = createStackNavigator<PeopleStackParamList>();

export default function PeopleNavigator() {
  return (
    <Stack.Navigator
      initialRouteName="PeopleDirectory"
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen name="PeopleDirectory" component={PeopleDirectoryScreen} />
    </Stack.Navigator>
  );
}
