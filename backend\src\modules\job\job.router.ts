import { Router } from 'express';
import { Job<PERSON>ontroller } from './job.controller';
import { JobService } from './job.service';
import { authenticateToken } from '../../middleware/auth';

const router = Router();
const jobService = new JobService();
const jobController = new JobController(jobService);

// Public routes (no authentication required)
router.get('/', jobController.getJobs.bind(jobController)); // Get all jobs
router.get('/:id', jobController.getJob.bind(jobController)); // Get specific job

// Protected routes (authentication required)
router.post('/', authenticateToken, jobController.createJob.bind(jobController));
router.put('/:id', authenticateToken, jobController.updateJob.bind(jobController));
router.delete('/:id', authenticateToken, jobController.deleteJob.bind(jobController));

export default router; 