import React from 'react';
import { StyleSheet, Image, View } from 'react-native';
import { Button, useTheme } from 'react-native-paper';
import * as ImagePicker from 'react-native-image-picker';
import { rtlConfig } from '../../theme/rtl';

interface ImagePickerProps {
  value?: string;
  onChange: (uri: string) => void;
  error?: string;
  style?: any;
}

export const ImagePickerComponent = ({
  value,
  onChange,
  error,
  style,
}: ImagePickerProps) => {
  const theme = useTheme();

  const handleImagePick = () => {
    ImagePicker.launchImageLibrary({
      mediaType: 'photo',
      includeBase64: false,
      maxHeight: 1000,
      maxWidth: 1000,
    }, (response) => {
      if (response.didCancel) {
        return;
      }
      if (response.errorCode) {
        console.error('ImagePicker Error: ', response.errorMessage);
        return;
      }
      if (response.assets && response.assets[0]?.uri) {
        onChange(response.assets[0].uri);
      }
    });
  };

  return (
    <View style={[styles.container, style]}>
      {value && (
        <Image
          source={{ uri: value }}
          style={styles.image}
          resizeMode="cover"
        />
      )}
      <Button
        mode="outlined"
        onPress={handleImagePick}
        style={styles.button}
        textColor={error ? theme.colors.error : theme.colors.primary}
      >
        {value ? 'Change Image' : 'Pick an Image'}
      </Button>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
  },
  image: {
    width: '100%',
    height: 200,
    marginBottom: 8,
    borderRadius: 8,
  },
  button: {
    marginTop: 8,
  },
}); 