import prisma from '../../config/prismaClient';
import { JobResponse } from './job.types';

export class JobService {
  constructor() {
    // Using centralized Prisma client
  }

  async createJob(job: any): Promise<JobResponse> {
    const dbJob = await prisma.job.create({
      data: {
        community_id: job.community_id,
        title: job.title,
        description: job.description,
        location: job.location,
        date_time_info: job.dateTimeInfo || job.date_time_info,
        age_preference: job.agePreference || job.age_preference,
        skills: job.skills || [],
        status: job.status?.toUpperCase() || 'OPEN',
        job_type: job.jobType?.toUpperCase() || job.job_type?.toUpperCase() || 'REQUEST',
        posted_by_id: job.posted_by_id,
        posted_by_name: job.posted_by_name,
        inline_photo_id: job.inlinePhotoId || job.inline_photo_id,
        data_ai_hint: job.dataAiHint || job.data_ai_hint
      }
    });

    // Transform to frontend-compatible format
    return this.transformJobForResponse(dbJob);
  }

  private transformJobForResponse(dbJob: any): JobResponse {
    return {
      id: dbJob.id,
      title: dbJob.title,
      description: dbJob.description,
      location: dbJob.location,
      dateTimeInfo: dbJob.date_time_info,
      agePreference: dbJob.age_preference,
      skills: dbJob.skills || [],
      status: dbJob.status.toLowerCase(),
      jobType: dbJob.job_type.toLowerCase(),
      postedById: dbJob.posted_by_id,
      postedByName: dbJob.posted_by_name,
      inlinePhotoId: dbJob.inline_photo_id,
      dataAiHint: dbJob.data_ai_hint,
      createdAt: dbJob.created_at,
      applications: [] // Empty array for now
    };
  }

  async getJobs(): Promise<JobResponse[]> {
    const dbJobs = await prisma.job.findMany({
      orderBy: { created_at: 'desc' }
    });
    return dbJobs.map(job => this.transformJobForResponse(job));
  }

  async getJob(id: string): Promise<JobResponse | null> {
    const dbJob = await prisma.job.findUnique({
      where: { id }
    });
    return dbJob ? this.transformJobForResponse(dbJob) : null;
  }

  async updateJob(id: string, data: any): Promise<JobResponse | null> {
    const dbJob = await prisma.job.update({
      where: { id },
      data: {
        title: data.title,
        description: data.description,
        location: data.location,
        date_time_info: data.dateTimeInfo || data.date_time_info,
        age_preference: data.agePreference || data.age_preference,
        skills: data.skills,
        status: data.status?.toUpperCase(),
        job_type: data.jobType?.toUpperCase() || data.job_type?.toUpperCase(),
        inline_photo_id: data.inlinePhotoId || data.inline_photo_id,
        data_ai_hint: data.dataAiHint || data.data_ai_hint
      }
    });
    return this.transformJobForResponse(dbJob);
  }

  async deleteJob(id: string): Promise<void> {
    await prisma.job.delete({
      where: { id }
    });
  }
}