import prisma from '../../config/prismaClient';
import { Survey } from './survey.types';

export class SurveyService {
  constructor() {
    // Using centralized Prisma client
  }

  private mapToSurvey(prismaSurvey: any): Survey {
    return {
      id: prismaSurvey.id,
      title: prismaSurvey.title,
      description: prismaSurvey.description,
      questions: prismaSurvey.questions,
      status: prismaSurvey.status,
      createdBy: prismaSurvey.created_by,
      createdAt: prismaSurvey.created_at,
      communityId: prismaSurvey.community_id,
      participantsCount: prismaSurvey.participants_count,
      closingDate: prismaSurvey.closing_date,
      imageUrl: prismaSurvey.image_url,
      dataAiHint: prismaSurvey.data_ai_hint
    };
  }

  private mapToPrismaData(survey: Partial<Survey>): any {
    const data: any = {};
    if (survey.title !== undefined) data.title = survey.title;
    if (survey.description !== undefined) data.description = survey.description;
    if (survey.questions !== undefined) data.questions = survey.questions;
    if (survey.status !== undefined) data.status = survey.status;
    if (survey.createdBy !== undefined) data.created_by = survey.createdBy;
    if (survey.communityId !== undefined) data.community_id = survey.communityId;
    if (survey.participantsCount !== undefined) data.participants_count = survey.participantsCount;
    if (survey.closingDate !== undefined) data.closing_date = survey.closingDate;
    if (survey.imageUrl !== undefined) data.image_url = survey.imageUrl;
    if (survey.dataAiHint !== undefined) data.data_ai_hint = survey.dataAiHint;
    return data;
  }

  async createSurvey(survey: Omit<Survey, 'id'>): Promise<Survey> {
    const prismaData = this.mapToPrismaData(survey);
    const createdSurvey = await prisma.survey.create({
      data: prismaData
    });
    return this.mapToSurvey(createdSurvey);
  }

  async getSurveys(): Promise<Survey[]> {
    const surveys = await prisma.survey.findMany({
      orderBy: { created_at: 'desc' }
    });
    return surveys.map(survey => this.mapToSurvey(survey));
  }

  async getSurvey(id: string): Promise<Survey | null> {
    const survey = await prisma.survey.findUnique({
      where: { id }
    });
    return survey ? this.mapToSurvey(survey) : null;
  }

  async updateSurvey(id: string, data: Partial<Survey>): Promise<Survey | null> {
    const prismaData = this.mapToPrismaData(data);
    const survey = await prisma.survey.update({
      where: { id },
      data: prismaData
    });
    return this.mapToSurvey(survey);
  }

  async deleteSurvey(id: string): Promise<void> {
    await prisma.survey.delete({
      where: { id }
    });
  }
} 