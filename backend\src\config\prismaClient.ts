import { PrismaClient } from  '../generated/prisma';
import dotenv from 'dotenv';
import { envRegistry } from '../utils/env';

// Register environment variables
envRegistry.register({
  name: 'DATABASE_URL',
  description: 'PostgreSQL database connection string',
  defaultValue: 'postgresql://user:password@localhost:5432/kibbutz_db?schema=public',
  required: true,
  module: 'Database'
});

// Load environment variables
dotenv.config();

if (!process.env['DATABASE_URL']) {
  throw new Error('DATABASE_URL environment variable is required');
}

// Log the database connection parameters (excluding password)
console.log('[Prisma] Initializing PrismaClient with:', {
  url: process.env['DATABASE_URL'].replace(/\/\/[^:]+:[^@]+@/, '//****:****@'),
  schema: 'public'
});

const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env['DATABASE_URL']
    }
  }
});

export default prisma;