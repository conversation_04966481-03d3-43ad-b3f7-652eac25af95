import { Router } from 'express';
import { Chat<PERSON>ontroller } from './chat.controller';
import { ChatService } from './chat.service';
import { authenticateToken } from '../../middleware/auth';

const router = Router();
const chatService = new ChatService();
const chatController = new ChatController(chatService);

// All chat routes require authentication
router.use(authenticateToken);

// Chat Group routes
router.get('/groups', chatController.getChatGroups.bind(chatController));
router.post('/groups', chatController.createChatGroup.bind(chatController));
router.get('/groups/:id', chatController.getChatGroup.bind(chatController));
router.put('/groups/:id', chatController.updateChatGroup.bind(chatController));
router.delete('/groups/:id', chatController.deleteChatGroup.bind(chatController));

// Chat Message routes
router.get('/messages', chatController.getChatMessages.bind(chatController));
router.post('/messages', chatController.createChatMessage.bind(chatController));
router.get('/messages/:id', chatController.getChatMessage.bind(chatController));
router.put('/messages/:id', chatController.updateChatMessage.bind(chatController));
router.delete('/messages/:id', chatController.deleteChatMessage.bind(chatController));
router.get('/groups/:groupId/messages', chatController.getChatMessagesByGroupId.bind(chatController));

export default router; 