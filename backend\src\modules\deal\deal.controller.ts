import { Request, Response } from 'express';
import { DealService } from './deal.service';

export class DealController {
  constructor(private readonly dealService: DealService) {}

  async getDeals(_req: Request, res: Response) {
    try {
      const deals = await this.dealService.getDeals();
      return res.json(deals);
    } catch (error) {
      return res.status(500).json({ message: 'Error fetching deals', error });
    }
  }

  async createDeal(req: Request, res: Response) {
    try {
      const deal = await this.dealService.createDeal(req.body);
      return res.status(201).json(deal);
    } catch (error) {
      return res.status(500).json({ message: 'Error creating deal', error });
    }
  }

  async getDeal(req: Request, res: Response) {
    try {
      const id = req.params['id'];
      if (!id) {
        return res.status(400).json({ message: 'Deal ID is required' });
      }
      const deal = await this.dealService.getDeal(id);
      if (!deal) {
        return res.status(404).json({ message: 'Deal not found' });
      }
      return res.json(deal);
    } catch (error) {
      return res.status(500).json({ message: 'Error fetching deal', error });
    }
  }

  async updateDeal(req: Request, res: Response) {
    try {
      const id = req.params['id'];
      if (!id) {
        return res.status(400).json({ message: 'Deal ID is required' });
      }
      const updatedDeal = await this.dealService.updateDeal(id, req.body);
      if (!updatedDeal) {
        return res.status(404).json({ message: 'Deal not found' });
      }
      return res.json(updatedDeal);
    } catch (error) {
      return res.status(500).json({ message: 'Error updating deal', error });
    }
  }

  async deleteDeal(req: Request, res: Response) {
    try {
      const id = req.params['id'];
      if (!id) {
        return res.status(400).json({ message: 'Deal ID is required' });
      }
      await this.dealService.deleteDeal(id);
      return res.status(204).send();
    } catch (error) {
      return res.status(500).json({ message: 'Error deleting deal', error });
    }
  }
} 