import prisma from '../../config/prismaClient';
import { FoodItem } from './food.types';

export class FoodService {
  constructor() {
    // Using centralized Prisma client
  }

  async createFoodItem(foodItem: Omit<FoodItem, 'id'>): Promise<FoodItem> {
    return prisma.foodItem.create({
      data: {
        name: foodItem.name,
        description: foodItem.description,
        price: foodItem.price,
        community_id: foodItem.community_id,
        seller_name: foodItem.seller_name,
        seller_id: foodItem.seller_id,
        contact_info: foodItem.contact_info,
        pickup_details: foodItem.pickup_details,
        inline_photo_id: foodItem.inline_photo_id,
        data_ai_hint: foodItem.data_ai_hint,
        created_at: new Date(),
      }
    });
  }

  async getFoodItems(): Promise<FoodItem[]> {
    return prisma.foodItem.findMany({
      orderBy: { created_at: 'desc' }
    });
  }

  async getFoodItem(id: string): Promise<FoodItem | null> {
    return prisma.foodItem.findUnique({
      where: { id }
    });
  }

  async updateFoodItem(id: string, data: Partial<FoodItem>): Promise<FoodItem | null> {
    // Check if food item exists first
    const existingItem = await prisma.foodItem.findUnique({
      where: { id }
    });

    if (!existingItem) {
      return null;
    }

    const updateData: any = {};
    if (data.name !== undefined) updateData.name = data.name;
    if (data.description !== undefined) updateData.description = data.description;
    if (data.price !== undefined) updateData.price = data.price;
    if (data.community_id !== undefined) updateData.community_id = data.community_id;
    if (data.seller_name !== undefined) updateData.seller_name = data.seller_name;
    if (data.seller_id !== undefined) updateData.seller_id = data.seller_id;
    if (data.contact_info !== undefined) updateData.contact_info = data.contact_info;
    if (data.pickup_details !== undefined) updateData.pickup_details = data.pickup_details;
    if (data.inline_photo_id !== undefined) updateData.inline_photo_id = data.inline_photo_id;
    if (data.data_ai_hint !== undefined) updateData.data_ai_hint = data.data_ai_hint;

    return prisma.foodItem.update({
      where: { id },
      data: updateData
    });
  }

  async deleteFoodItem(id: string): Promise<boolean> {
    // Check if food item exists first
    const existingItem = await prisma.foodItem.findUnique({
      where: { id }
    });

    if (!existingItem) {
      return false;
    }

    await prisma.foodItem.delete({
      where: { id }
    });

    return true;
  }
} 