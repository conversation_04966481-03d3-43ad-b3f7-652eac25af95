import { API_BASE_URL } from '../lib/supabase';
import { getAuthToken } from './auth';

export interface ChatGroup {
  id: string;
  name: string;
  createdAt: Date;
  createdBy: string;
  members: string[];
  iconUrl: string | null;
  dataAiHint: string | null;
  lastMessage: string | null;
  lastMessageTimestamp: Date | null;
}

export interface ChatMessage {
  id: string;
  groupId: string;
  senderId: string;
  senderName: string;
  text: string;
  timestamp: Date;
  avatarUrl: string;
  avatarDataAiHint: string | null;
}

class ChatService {
  private async makeRequest(endpoint: string, options: RequestInit = {}) {
    const token = await getAuthToken();
    
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        ...options.headers,
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response.json();
  }

  async getChatGroups(): Promise<ChatGroup[]> {
    try {
      console.log('🔄 Loading chat groups...');
      const groups = await this.makeRequest('/apichat/groups');
      console.log('✅ Chat groups loaded successfully:', groups.length);
      return groups.map((group: any) => ({
        ...group,
        createdAt: new Date(group.createdAt),
        lastMessageTimestamp: group.lastMessageTimestamp ? new Date(group.lastMessageTimestamp) : null,
      }));
    } catch (error) {
      console.error('❌ Error loading chat groups:', error);
      throw error;
    }
  }

  async getChatGroup(id: string): Promise<ChatGroup> {
    try {
      console.log('🔄 Loading chat group:', id);
      const group = await this.makeRequest(`/apichat/groups/${id}`);
      console.log('✅ Chat group loaded successfully');
      return {
        ...group,
        createdAt: new Date(group.createdAt),
        lastMessageTimestamp: group.lastMessageTimestamp ? new Date(group.lastMessageTimestamp) : null,
      };
    } catch (error) {
      console.error('❌ Error loading chat group:', error);
      throw error;
    }
  }

  async createChatGroup(group: Omit<ChatGroup, 'id'>): Promise<ChatGroup> {
    try {
      console.log('🔄 Creating chat group...');
      const newGroup = await this.makeRequest('/apichat/groups', {
        method: 'POST',
        body: JSON.stringify(group),
      });
      console.log('✅ Chat group created successfully');
      return {
        ...newGroup,
        createdAt: new Date(newGroup.createdAt),
        lastMessageTimestamp: newGroup.lastMessageTimestamp ? new Date(newGroup.lastMessageTimestamp) : null,
      };
    } catch (error) {
      console.error('❌ Error creating chat group:', error);
      throw error;
    }
  }

  async getChatMessages(groupId: string): Promise<ChatMessage[]> {
    try {
      console.log('🔄 Loading chat messages for group:', groupId);
      const messages = await this.makeRequest(`/apichat/groups/${groupId}/messages`);
      console.log('✅ Chat messages loaded successfully:', messages.length);
      return messages.map((message: any) => ({
        ...message,
        timestamp: new Date(message.timestamp),
      }));
    } catch (error) {
      console.error('❌ Error loading chat messages:', error);
      throw error;
    }
  }

  async sendMessage(message: Omit<ChatMessage, 'id' | 'timestamp'>): Promise<ChatMessage> {
    try {
      console.log('🔄 Sending message...');
      const newMessage = await this.makeRequest('/apichat/messages', {
        method: 'POST',
        body: JSON.stringify({
          ...message,
          timestamp: new Date(),
        }),
      });
      console.log('✅ Message sent successfully');
      return {
        ...newMessage,
        timestamp: new Date(newMessage.timestamp),
      };
    } catch (error) {
      console.error('❌ Error sending message:', error);
      throw error;
    }
  }
}

export const chatService = new ChatService();
