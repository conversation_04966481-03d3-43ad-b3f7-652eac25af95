import { getModuleLogger } from './logger';

// Create separate loggers for different types of database operations
const queryLogger = getModuleLogger('DatabaseQuery');
const resultLogger = getModuleLogger('DatabaseResult');
const errorLogger = getModuleLogger('DatabaseError');

export const logQuery = (query: string, params: any[] = []) => {
  queryLogger.debug('Database Query:', {
    query,
    params,
    timestamp: new Date().toISOString()
  });
};

export const logQueryResult = (query: string, result: any) => {
  resultLogger.debug('Database Query Result:', {
    query,
    rowCount: result?.count || result?.length || 'unknown',
    timestamp: new Date().toISOString()
  });
};

export const logQueryError = (query: string, error: any) => {
  errorLogger.error('Database Query Error:', {
    query,
    error: error.message,
    stack: error.stack,
    timestamp: new Date().toISOString()
  });
};

export const logPrismaOperation = (operation: string, model: string, data?: any) => {
  queryLogger.debug('Prisma Operation:', {
    operation,
    model,
    data: data ? JSON.stringify(data) : undefined,
    timestamp: new Date().toISOString()
  });
};

export const logPrismaResult = (operation: string, model: string, result: any) => {
  resultLogger.debug('Prisma Operation Result:', {
    operation,
    model,
    resultType: Array.isArray(result) ? 'array' : typeof result,
    count: Array.isArray(result) ? result.length : result ? 1 : 0,
    timestamp: new Date().toISOString()
  });
};

export const logPrismaError = (operation: string, model: string, error: any) => {
  errorLogger.error('Prisma Operation Error:', {
    operation,
    model,
    error: error.message,
    code: error.code,
    stack: error.stack,
    timestamp: new Date().toISOString()
  });
};
