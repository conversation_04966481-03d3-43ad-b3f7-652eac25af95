---
description: 
globs: 
alwaysApply: false
---
# <PERSON><PERSON><PERSON> App UI Stack Migration Plan

## Overview
This document outlines the plan to migrate the Kibbutz App from its current UI implementation to a more robust and consistent UI stack using React Native Paper and associated libraries.

## Current Stack
- React Navigation (Native, Stack, Bottom Tabs, Drawer)
- React Native Reanimated
- React Native Gesture Handler
- React Native Safe Area Context
- i18next for internationalization
- Firebase for backend
- AsyncStorage for local storage

## Target Stack
### Core UI Framework
- React Native Paper
- React Native Vector Icons

### Additional Styling
- NativeWind (utility-first styling)
- React Native Linear Gradient

### Community Features
- React Native Image Picker
- React Native Keyboard Aware ScrollView

## Migration Phases

### Phase 1: Foundation Setup
#### 1.1 Dependencies Installation
```bash
# Core UI
npm install react-native-paper react-native-vector-icons

# Styling
npm install nativewind tailwindcss
npm install react-native-linear-gradient

# Community Features
npm install react-native-image-picker
npm install react-native-keyboard-aware-scrollview
```

#### 1.2 Configuration Files
1. Create `tailwind.config.js`
2. Update `babel.config.js`
3. Add PostCSS configuration
4. Configure Metro bundler for new dependencies

### Phase 2: Theme Implementation
#### 2.1 Theme Structure
Create `src/theme/index.ts` with:
- Color palette
- Typography system
- Spacing system
- Component-specific themes

#### 2.2 RTL Support
- Configure RTL layout support
- Test with Hebrew content
- Verify navigation direction

### Phase 3: Component Migration
#### 3.1 Navigation Components
Priority order:
1. Bottom tabs
2. Stack navigation
3. Drawer navigation
4. Modal screens

#### 3.2 Core Components
Priority order:
1. Buttons
2. Cards
3. Lists
4. Forms
5. Modals
6. Loading states

#### 3.3 Form Components
Priority order:
1. Text inputs
2. Selection components
3. Date pickers
4. File uploads
5. Validation states

### Phase 4: Enhanced Features
#### 4.1 Image Handling
1. Image picker implementation
2. Image preview components
3. Upload functionality
4. Error handling

#### 4.2 Animations
1. Screen transitions
2. Component animations
3. Gesture-based interactions
4. Loading states

### Phase 5: Testing & Optimization
#### 5.1 Testing Strategy
1. Component testing
2. Integration testing
3. Performance testing
4. RTL testing
5. Accessibility testing

#### 5.2 Performance Optimization
1. Bundle size optimization
2. Animation performance
3. Memory usage
4. Load time optimization

## Implementation Guidelines

### Component Migration Pattern
For each component:
1. Create new component with Paper
2. Implement basic functionality
3. Add styling and theming
4. Add animations
5. Test thoroughly
6. Replace old component

### Code Structure
```
src/
  ├── components/
  │   ├── paper/           # Paper-based components
  │   └── legacy/          # Old components (temporary)
  ├── theme/
  │   ├── index.ts         # Theme configuration
  │   ├── colors.ts        # Color definitions
  │   └── typography.ts    # Typography system
  ├── navigation/
  │   ├── PaperNavigator.tsx
  │   └── components/      # Navigation components
  └── screens/
      └── [screen-name]/
          ├── index.tsx
          └── styles.ts
```

### Testing Requirements
For each migrated component:
- [ ] Renders correctly
- [ ] Handles RTL properly
- [ ] Animations work smoothly
- [ ] Accessibility features work
- [ ] Performance meets standards
- [ ] Error states handled
- [ ] Loading states implemented

## Timeline
- Phase 1: 1-2 days
- Phase 2: 1-2 days
- Phase 3: 3-4 days
- Phase 4: 2-3 days
- Phase 5: 2-3 days

Total: 9-14 days

## Success Criteria
1. All components use React Native Paper
2. RTL support works correctly
3. Animations are smooth
4. Performance metrics meet targets
5. No regression in functionality
6. All tests pass
7. Bundle size within limits

## Rollback Plan
1. Keep old components in `legacy` folder
2. Maintain feature flags for gradual rollout
3. Document all changes for easy rollback
4. Regular backups of working state

## Notes
- Maintain existing functionality during migration
- Test thoroughly after each component migration
- Document any issues or solutions
- Regular progress updates

- Performance monitoring throughout 