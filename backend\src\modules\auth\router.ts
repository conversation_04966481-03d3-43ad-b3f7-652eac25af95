import { Router } from 'express';
import { AuthController } from './controller';
import { getModuleLogger } from '../../utils/logger';

const logger = getModuleLogger('Auth');
const router = Router();
const controller = new AuthController();

// Register new user
router.post('/register', async (req, res) => {
  try {
    logger.info('Register endpoint called');
    await controller.register(req, res);
    logger.info('Register endpoint completed');
  } catch (error) {
    logger.error('Error in register endpoint:', { error });
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

// Login route
router.post('/login', async (req, res) => {
  try {
    logger.info('Login endpoint called');
    await controller.login(req, res);
    logger.info('Login endpoint completed');
  } catch (error) {
    logger.error('Error in login endpoint:', { error });
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

// Refresh token route
router.post('/refresh-token', async (req, res) => {
  try {
    logger.info('Refresh token endpoint called');
    await controller.refreshToken(req, res);
    logger.info('Refresh token endpoint completed');
  } catch (error) {
    logger.error('Error in refresh token endpoint:', { error });
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

// Logout route
router.post('/logout', async (req, res) => {
  try {
    logger.info('Logout endpoint called');
    await controller.logout(req, res);
    logger.info('Logout endpoint completed');
  } catch (error) {
    logger.error('Error in logout endpoint:', { error });
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

export default router; 