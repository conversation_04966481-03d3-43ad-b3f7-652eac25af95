import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { SurveysStackParamList } from './types';
import SurveysListScreen from '../screens/surveys/SurveysListScreen';
import SurveyDetailsScreen from '../screens/surveys/SurveyDetailsScreen';
import CreateSurveyScreen from '../screens/surveys/CreateSurveyScreen';
import SurveyResultsScreen from '../screens/surveys/SurveyResultsScreen';

const Stack = createStackNavigator<SurveysStackParamList>();

export default function SurveysNavigator() {
  return (
    <Stack.Navigator
      initialRouteName="SurveysList"
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen name="SurveysList" component={SurveysListScreen} />
      <Stack.Screen name="SurveyDetails" component={SurveyDetailsScreen} />
      <Stack.Screen name="CreateSurvey" component={CreateSurveyScreen} />
      <Stack.Screen name="SurveyResults" component={SurveyResultsScreen} />
    </Stack.Navigator>
  );
}
