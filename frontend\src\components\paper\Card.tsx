import React from 'react';
import { StyleSheet, View } from 'react-native';
import { Card as PaperCard, useTheme } from 'react-native-paper';
import { rtlConfig } from '../../theme/rtl';

interface CardProps {
  title?: string;
  children: React.ReactNode;
  onPress?: () => void;
  style?: any;
}

export const Card = ({ title, children, onPress, style }: CardProps) => {
  const theme = useTheme();

  return (
    <PaperCard
      style={[styles.card, style]}
      onPress={onPress}
      mode={onPress ? 'elevated' : 'outlined'}
    >
      {title && (
        <PaperCard.Title
          title={title}
          titleStyle={[
            styles.title,
            { color: theme.colors.onSurface, textAlign: rtlConfig.textAlign },
          ]}
        />
      )}
      <PaperCard.Content style={styles.content}>{children}</PaperCard.Content>
    </PaperCard>
  );
};

const styles = StyleSheet.create({
  card: {
    marginVertical: 8,
    marginHorizontal: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  content: {
    paddingTop: 8,
  },
}); 