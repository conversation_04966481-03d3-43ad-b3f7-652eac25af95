import { getModuleLogger } from './logger';
import { getSupabaseClient } from '../config/supabase';

const logger = getModuleLogger('AuthUtils');

interface SupabaseUser {
  id: string;
  email: string;
  role?: string;
  user_metadata?: any;
  app_metadata?: any;
}

export const verifySupabaseToken = async (token: string): Promise<SupabaseUser> => {
  try {
    const supabase = getSupabaseClient();

    // Verify the token with Supabase
    const { data: { user }, error } = await supabase.auth.getUser(token);

    if (error) {
      logger.error('Supabase token verification failed:', error);
      throw new Error('Invalid token');
    }

    if (!user) {
      logger.error('No user found for token');
      throw new Error('Invalid token');
    }

    logger.debug('Supabase token verified successfully', {
      userId: user.id,
      email: user.email
    });

    return {
      id: user.id,
      email: user.email || '',
      role: user.app_metadata?.['role'] || user.user_metadata?.['role'],
      user_metadata: user.user_metadata,
      app_metadata: user.app_metadata
    };
  } catch (error) {
    logger.error('Token verification failed:', error);
    throw new Error('Invalid token');
  }
};

export const extractTokenFromHeader = (authHeader: string | undefined): string | null => {
  if (!authHeader) {
    logger.debug('No authorization header provided');
    return null;
  }

  const parts = authHeader.split(' ');
  if (parts.length !== 2 || parts[0] !== 'Bearer') {
    logger.debug('Invalid authorization header format');
    return null;
  }

  return parts[1] || null;
};

export const getUserFromToken = async (token: string): Promise<SupabaseUser> => {
  try {
    return await verifySupabaseToken(token);
  } catch (error) {
    logger.error('Failed to get user from token:', error);
    throw error;
  }
};
