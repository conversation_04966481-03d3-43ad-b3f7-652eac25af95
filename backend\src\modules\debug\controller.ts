import { Request, Response } from 'express';
import { DebugService } from './service';
import { getModuleLogger } from '../../utils/logger';

const logger = getModuleLogger('Debug');
const service = new DebugService();

export class DebugController {
  async getAllTables(req: Request, res: Response): Promise<void> {
    try {
      const result = await service.getAllTables();
      res.json(result);
    } catch (error) {
      logger.error(`Error in getAllTables: ${error}`);
      res.status(500).json({
        success: false,
        error: 'Failed to get tables'
      });
    }
  }

  async getTableEntries(req: Request, res: Response): Promise<void> {
    try {
      const { tableName } = req.params;
      if (!tableName) {
        res.status(400).json({
          success: false,
          error: 'Table name is required'
        });
        return;
      }

      const limit = parseInt(req.query['limit'] as string) || 100;
      const result = await service.getTableEntries(tableName, limit);
      res.json(result);
    } catch (error) {
      logger.error(`Error in getTableEntries: ${error}`);
      res.status(500).json({
        success: false,
        error: 'Failed to get table entries'
      });
    }
  }

  async deleteTableEntries(req: Request, res: Response): Promise<void> {
    try {
      const { tableName } = req.params;
      if (!tableName) {
        res.status(400).json({
          success: false,
          error: 'Table name is required'
        });
        return;
      }

      const result = await service.deleteTableEntries(tableName);
      res.json(result);
    } catch (error) {
      logger.error(`Error in deleteTableEntries: ${error}`);
      res.status(500).json({
        success: false,
        error: 'Failed to delete table entries'
      });
    }
  }
} 