import prisma from '../../config/prismaClient';
import { EventResponse } from './event.types';

export class EventService {
  constructor() {
    // Using centralized Prisma client
  }

  async createEvent(event: any): Promise<EventResponse> {
    const dbEvent = await prisma.event.create({
      data: {
        community_id: event.community_id,
        title: event.title,
        description: event.description,
        date: event.date,
        location: event.location,
        type: event.type.toUpperCase(), // Convert to uppercase for enum
        icon: event.icon || "📅", // Default to calendar emoji if not provided
        image_url: event.imageUrl || event.image_url,
        data_ai_hint: event.dataAiHint || event.data_ai_hint,
        attendees: event.attendees || 0,
        created_by: event.created_by
      }
    });

    // Transform to frontend-compatible format
    return this.transformEventForResponse(dbEvent);
  }

  private transformEventForResponse(dbEvent: any): EventResponse {
    // Format creator name from user data
    let createdByName = 'משתמש לא ידוע'; // Default fallback
    if (dbEvent.user) {
      const firstName = dbEvent.user.first_name || '';
      const lastName = dbEvent.user.last_name || '';
      if (firstName || lastName) {
        createdByName = `${firstName} ${lastName}`.trim();
      } else if (dbEvent.user.email) {
        // Fallback to email if no name available
        createdByName = dbEvent.user.email.split('@')[0];
      }
    }

    return {
      id: dbEvent.id,
      title: dbEvent.title,
      description: dbEvent.description,
      date: dbEvent.date,
      location: dbEvent.location,
      type: dbEvent.type.toLowerCase(), // Convert back to lowercase for frontend
      icon: dbEvent.icon,
      imageUrl: dbEvent.image_url,
      dataAiHint: dbEvent.data_ai_hint,
      createdAt: dbEvent.created_at,
      attendees: dbEvent.attendees,
      createdBy: createdByName, // Now contains the actual name
      created_by: dbEvent.created_by, // <-- Add this for test compatibility
      rsvp: [], // Empty array for now
      comments: [], // Empty array for now
      reactions: [] // Empty array for now
    } as any;
  }

  async getEvents(): Promise<EventResponse[]> {
    const dbEvents = await prisma.event.findMany({
      include: {
        user: {
          select: {
            first_name: true,
            last_name: true,
            email: true
          }
        }
      },
      orderBy: { date: 'asc' }
    });
    return dbEvents.map(event => this.transformEventForResponse(event));
  }

  async getEvent(id: string): Promise<any> {
    return prisma.event.findUnique({ where: { id } });
  }

  async updateEvent(id: string, data: any): Promise<EventResponse | null> {
    const dbEvent = await prisma.event.update({
      where: { id },
      data: {
        title: data.title,
        description: data.description,
        date: data.date,
        location: data.location,
        type: data.type?.toUpperCase(),
        icon: data.icon,
        image_url: data.imageUrl || data.image_url,
        data_ai_hint: data.dataAiHint || data.data_ai_hint,
        attendees: data.attendees
      },
      include: {
        user: {
          select: {
            first_name: true,
            last_name: true,
            email: true
          }
        }
      }
    });
    return this.transformEventForResponse(dbEvent);
  }

  async deleteEvent(id: string): Promise<void> {
    await prisma.event.delete({
      where: { id }
    });
  }

  async getEventById(id: string): Promise<any> {
    return prisma.event.findUnique({ where: { id } });
  }

  async isOwner(eventId: string, userId: string): Promise<boolean> {
    const event = await prisma.event.findUnique({ where: { id: eventId } });
    return event?.created_by === userId;
  }
} 