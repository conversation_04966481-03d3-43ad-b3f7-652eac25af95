{"common": {"loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "save": "Save", "edit": "Edit", "delete": "Delete", "add": "Add", "search": "Search", "back": "Back", "next": "Next", "previous": "Previous", "submit": "Submit", "confirm": "Confirm"}, "auth": {"login": "<PERSON><PERSON>", "register": "Register", "logout": "Logout", "email": "Email Address", "password": "Password", "confirmPassword": "Confirm Password", "forgotPassword": "Forgot Password?", "noAccount": "Don't have an account? Register here", "hasAccount": "Have an account? Login here", "loginButton": "<PERSON><PERSON>", "registerButton": "Register", "welcomeTitle": "Welcome to Kibbutz Community App", "loginError": "Login error", "registerError": "Registration error", "fillAllFields": "Please fill all fields", "passwordMismatch": "Passwords do not match"}, "navigation": {"events": "Events", "chat": "Cha<PERSON>", "jobs": "Jobs", "commercialUnions": "Commercial Unions", "surveys": "Surveys", "profile": "Profile", "debug": "Admin"}, "events": {"title": "Events", "addEvent": "Add Event", "eventDetails": "Event Details", "createEvent": "Create Event", "editEvent": "Edit Event", "eventTitle": "Event Title", "eventDescription": "Event Description", "eventDate": "Date and Time", "eventLocation": "Location", "eventType": "Event Type", "communityEvent": "Community Event", "tenantEvent": "Tenant Event", "attending": "Attending", "notAttending": "Not Attending", "participants": "Participants", "comments": "Comments", "addComment": "Add Comment", "noEvents": "No events to display"}, "chat": {"title": "Cha<PERSON>", "newMessage": "New Message", "typeMessage": "Type a message...", "send": "Send", "createGroup": "Create Group", "groupName": "Group Name", "addParticipants": "Add Participants", "noChats": "No chats", "searchConversations": "Search conversations...", "villageChat": "Village Chat", "loading": "Loading chats...", "loadingMessages": "Loading messages...", "createNewGroup": "Create New Group", "groupDescription": "Group Description", "optional": "Optional"}, "jobs": {"title": "Jobs", "addJob": "Add Job", "jobDetails": "Job Details", "createJob": "Create Job", "jobTitle": "Job Title", "jobDescription": "Job Description", "jobLocation": "Job Location", "jobDate": "Date and Time", "agePreference": "Age Preference", "skills": "Required Skills", "apply": "Apply", "applications": "Applications", "myJobs": "My Jobs", "jobStatus": {"open": "Open", "inProgress": "In Progress", "completed": "Completed"}, "noJobs": "No jobs to display"}, "commercialUnions": {"title": "Commercial Unions", "addUnion": "Add Union", "unionDetails": "Union Details", "createUnion": "Create Union", "unionName": "Union Name", "unionDescription": "Union Description", "category": "Category", "targetSize": "Target Size", "currentSize": "Current Size", "joinUnion": "Join <PERSON>", "leaveUnion": "Leave Union", "myUnions": "My Unions", "noUnions": "No unions to display"}, "surveys": {"title": "Surveys", "addSurvey": "Add Survey", "surveyDetails": "Survey Details", "createSurvey": "Create Survey", "surveyTitle": "Survey Title", "surveyDescription": "Survey Description", "questions": "Questions", "addQuestion": "Add Question", "questionText": "Question Text", "questionType": "Question Type", "multipleChoice": "Multiple Choice", "yesNo": "Yes/No", "rating": "Rating", "textAnswer": "Text Answer", "vote": "Vote", "results": "Results", "anonymous": "Anonymous", "noSurveys": "No surveys to display"}, "profile": {"title": "Profile", "editProfile": "Edit Profile", "displayName": "Display Name", "phone": "Phone", "dateOfBirth": "Date of Birth", "role": "Role", "notifications": "Notifications", "settings": "Settings", "language": "Language", "theme": "Theme", "about": "About", "version": "Version"}, "debug": {"title": "Admin", "management": "System Management", "tables": "Tables", "logs": "Logs", "cache": "<PERSON><PERSON>", "settings": "Settings", "selectTable": "Select Table", "tableEntries": "Table Entries", "totalEntries": "Total Entries", "loading": "Loading data...", "error": "Error loading data", "noTables": "No tables found", "noEntries": "No entries in this table", "refreshData": "Refresh Data", "logsComingSoon": "Logs - Coming Soon", "cacheComingSoon": "Cache Management - Coming Soon", "settingsComingSoon": "System Settings - Coming Soon"}}