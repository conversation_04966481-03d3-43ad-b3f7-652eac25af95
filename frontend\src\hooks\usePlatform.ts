import { useMemo } from 'react';
import { Platform } from 'react-native';
import { platformConfig } from '../config/platform';

export const usePlatform = () => {
  const platform = useMemo(() => {
    return {
      ...platformConfig,
      isNative: !platformConfig.isWeb,
      platform: Platform.OS,
      select: Platform.select,
      version: Platform.Version,
    };
  }, []);

  return platform;
}; 