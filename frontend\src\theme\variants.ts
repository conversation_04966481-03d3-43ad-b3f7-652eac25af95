import { MD3LightTheme, MD3DarkTheme } from 'react-native-paper';
import { UIPreferences } from '../types';

// Color schemes
const colorSchemes = {
  default: {
    light: {
      primary: '#6200ee',
      primaryVariant: '#3700b3',
      secondary: '#03dac6',
      secondaryVariant: '#018786',
    },
    dark: {
      primary: '#bb86fc',
      primaryVariant: '#3700b3',
      secondary: '#03dac6',
      secondaryVariant: '#03dac6',
    },
  },
  blue: {
    light: {
      primary: '#1976d2',
      primaryVariant: '#0d47a1',
      secondary: '#42a5f5',
      secondaryVariant: '#1565c0',
    },
    dark: {
      primary: '#64b5f6',
      primaryVariant: '#1976d2',
      secondary: '#90caf9',
      secondaryVariant: '#42a5f5',
    },
  },
  green: {
    light: {
      primary: '#388e3c',
      primaryVariant: '#1b5e20',
      secondary: '#66bb6a',
      secondaryVariant: '#2e7d32',
    },
    dark: {
      primary: '#81c784',
      primaryVariant: '#388e3c',
      secondary: '#a5d6a7',
      secondaryVariant: '#66bb6a',
    },
  },
  purple: {
    light: {
      primary: '#7b1fa2',
      primaryVariant: '#4a148c',
      secondary: '#ba68c8',
      secondaryVariant: '#8e24aa',
    },
    dark: {
      primary: '#ce93d8',
      primaryVariant: '#7b1fa2',
      secondary: '#e1bee7',
      secondaryVariant: '#ba68c8',
    },
  },
  orange: {
    light: {
      primary: '#f57c00',
      primaryVariant: '#e65100',
      secondary: '#ffb74d',
      secondaryVariant: '#ff9800',
    },
    dark: {
      primary: '#ffb74d',
      primaryVariant: '#f57c00',
      secondary: '#ffcc02',
      secondaryVariant: '#ffb74d',
    },
  },
};

// Font size multipliers
const fontSizeMultipliers = {
  small: 0.85,
  medium: 1.0,
  large: 1.15,
  extraLarge: 1.3,
};

// Spacing multipliers
const spacingMultipliers = {
  compact: 0.75,
  medium: 1.0,
  comfortable: 1.25,
};

export function createTheme(preferences: UIPreferences, isDark: boolean) {
  const baseTheme = isDark ? MD3DarkTheme : MD3LightTheme;
  const colorScheme = colorSchemes[preferences.colorScheme] || colorSchemes.default;
  const colors = colorScheme[isDark ? 'dark' : 'light'];

  const fontMultiplier = fontSizeMultipliers[preferences.fontSize];
  const spacingMultiplier = spacingMultipliers[preferences.spacing];

  // High contrast adjustments
  const highContrastAdjustments = preferences.highContrast ? {
    outline: isDark ? '#ffffff' : '#000000',
    outlineVariant: isDark ? '#e0e0e0' : '#424242',
  } : {};

  // Create a proper React Native Paper theme with only supported properties
  const customTheme = {
    ...baseTheme,
    colors: {
      ...baseTheme.colors,
      primary: colors.primary,
      onPrimary: isDark ? '#000000' : '#ffffff',
      primaryContainer: colors.primaryVariant,
      onPrimaryContainer: isDark ? '#ffffff' : '#000000',
      secondary: colors.secondary,
      onSecondary: isDark ? '#000000' : '#ffffff',
      secondaryContainer: colors.secondaryVariant,
      onSecondaryContainer: isDark ? '#ffffff' : '#000000',
      ...highContrastAdjustments,
    },
    // Add custom properties for our use (not used by RN Paper directly)
    custom: {
      fontMultiplier,
      spacingMultiplier,
      fontSize: {
        h1: Math.round(96 * fontMultiplier),
        h2: Math.round(60 * fontMultiplier),
        h3: Math.round(48 * fontMultiplier),
        h4: Math.round(34 * fontMultiplier),
        h5: Math.round(24 * fontMultiplier),
        h6: Math.round(20 * fontMultiplier),
        subtitle1: Math.round(16 * fontMultiplier),
        subtitle2: Math.round(14 * fontMultiplier),
        body1: Math.round(16 * fontMultiplier),
        body2: Math.round(14 * fontMultiplier),
        button: Math.round(14 * fontMultiplier),
        caption: Math.round(12 * fontMultiplier),
        overline: Math.round(10 * fontMultiplier),
      },
      spacing: {
        xs: Math.round(4 * spacingMultiplier),
        sm: Math.round(8 * spacingMultiplier),
        md: Math.round(16 * spacingMultiplier),
        lg: Math.round(24 * spacingMultiplier),
        xl: Math.round(32 * spacingMultiplier),
        xxl: Math.round(48 * spacingMultiplier),
      },
    },
  };

  return customTheme;
}
